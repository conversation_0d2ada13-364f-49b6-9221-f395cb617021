import React from 'react';
import {
  SafeAreaView,
  StyleSheet,
  ScrollView,
  View,
  Text,
  Image,
  ActivityIndicator,
  Dimensions,
  TouchableOpacity,
} from 'react-native';
import HTML from 'react-native-render-html';
import { useWindowDimensions } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import LinearGradient from 'react-native-linear-gradient';

import metrics from 'themes/metrics';
import { usePostDetail } from './hooks';
import HomeHeader from 'components/Header/HomeHeader';
import PostCardList from './components/PostCardList';
import colors from 'themes/colors';

type RootStackParamList = {
  PostDetail: { id: number | string };
  AppTab: undefined;
};

type NavigationProps = NativeStackNavigationProp<RootStackParamList>;

interface PostDetailProps {
  route: {
    params?: {
      id?: string | number;
    };
  };
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const PostDetail: React.FC<PostDetailProps> = ({ route }) => {
  const id = route?.params?.id;
  const { post, loading, error } = usePostDetail({ id });
  const { width } = useWindowDimensions();
  const navigation = useNavigation<NavigationProps>();

  // Helper function to normalize HTML content and fix formatting issues
  const normalizeHtmlContent = (html: string) => {
    return html
      // Fix h4 tags margin
      .replace(
        /<h4 style="([^"]*)"/g,
        (match, style) => {
          if (!style.includes('margin-left')) {
            return `<h4 style="margin-left:0cm;${style}"`;
          }
          return match;
        }
      )
      // Fix list items to prevent indentation issues
      .replace(/<li[^>]*style="[^"]*text-align:justify[^"]*"[^>]*>/gi, '<li style="text-align:left;">')
      .replace(/<li[^>]*>/gi, '<li style="text-align:left;margin-left:0;padding-left:0;">')
      // Fix ul tags
      .replace(/<ul[^>]*>/gi, '<ul style="margin-left:0;padding-left:20px;list-style-position:outside;">')
      // Fix p tags inside li to prevent double spacing
      .replace(/<li[^>]*><p[^>]*>/gi, '<li><p style="margin:0;padding:0;">')
      .replace(/<\/p><\/li>/gi, '</p></li>')
      // Remove any existing bullet characters that might cause duplication
      .replace(/(<li[^>]*>)\s*[•·▪▫◦‣⁃]\s*/gi, '$1')
      // Normalize spacing
      .replace(/\s+/g, ' ')
      .trim();
  };

  // Helper function to format date
  const formatDate = (dateString: string) => {
    try {
      if (!dateString) return 'Không xác định';

      const date = new Date(dateString);

      // Kiểm tra xem date có hợp lệ không
      if (isNaN(date.getTime())) {
        return 'Không xác định';
      }

      return date.toLocaleDateString('vi-VN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Không xác định';
    }
  };

  // Helper function to get author initials
  const getAuthorInitials = (author: string) => {
    if (!author) return '?';
    return author
      .split(' ')
      .map(name => name.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Helper function to strip HTML tags from text and clean up formatting
  const stripHtmlTags = (html: string) => {
    if (!html) return '';
    let cleaned = html
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
      .replace(/&amp;/g, '&') // Replace &amp; with &
      .replace(/&lt;/g, '<') // Replace &lt; with <
      .replace(/&gt;/g, '>') // Replace &gt; with >
      .replace(/&quot;/g, '"') // Replace &quot; with "
      .replace(/&#39;/g, "'") // Replace &#39; with '
      .replace(/&bull;/g, '') // Remove HTML bullet entity
      .replace(/&middot;/g, '') // Remove middle dot entity
      .replace(/^\s*[\d]+\.\s*/gm, '') // Remove leading numbers like "1. " globally (multiline)
      .replace(/^\s*[•·▪▫◦‣⁃]\s*/gm, '') // Remove bullet points at start globally (multiline)
      .replace(/^\s*[-*]\s*/gm, '') // Remove dash/asterisk bullets at start globally (multiline)
      .replace(/[•·▪▫◦‣⁃]/g, '') // Remove all bullet characters anywhere in text
      .replace(/^\s*\.\s*/gm, '') // Remove leading dots (multiline)
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim();

    // Remove any remaining bullet-like characters at the start (loop until clean)
    let previousLength = 0;
    while (cleaned.length !== previousLength && cleaned.match(/^[\s•·▪▫◦‣⁃\-*\.]/)) {
      previousLength = cleaned.length;
      cleaned = cleaned.replace(/^[\s•·▪▫◦‣⁃\-*\.]+/, '').trim();
    }

    return cleaned;
  };

  // Helper function to clean HTML content for TOC display
  const cleanHtmlForToc = (html: string) => {
    if (!html) return '';

    // First, convert HTML to clean text
    let cleaned = html
      .replace(/<li[^>]*>/gi, '') // Remove <li> opening tags
      .replace(/<\/li>/gi, '\n') // Replace </li> with newline
      .replace(/<ul[^>]*>/gi, '') // Remove <ul> tags
      .replace(/<\/ul>/gi, '') // Remove </ul> tags
      .replace(/<p[^>]*>/gi, '') // Remove <p> opening tags
      .replace(/<\/p>/gi, '\n') // Replace </p> with newline
      .replace(/<[^>]*>/g, '') // Remove all other HTML tags
      .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
      .replace(/&amp;/g, '&') // Replace HTML entities
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&bull;/g, '') // Remove HTML bullet entity
      .replace(/&middot;/g, '') // Remove middle dot entity
      .replace(/[•·▪▫◦‣⁃]/g, '') // Remove all bullet characters
      .replace(/^\s*[\d]+\.\s*/gm, '') // Remove leading numbers
      .replace(/^\s*[-*]\s*/gm, '') // Remove dash/asterisk bullets
      .split('\n') // Split by newlines
      .map(line => line.trim()) // Trim each line
      .filter(line => line.length > 0) // Remove empty lines
      .map(line => `• ${line}`) // Add clean bullet point
      .join('\n'); // Join back with newlines

    return cleaned;
  };

  // Helper function to safely get nested content
  const getTextContent = (tnode: any) => {
    if (!tnode) return '';
    
    // Try different paths to get text content
    if (tnode.children?.[0]?.children?.[0]?.data) {
      return tnode.children[0].children[0].data;
    }
    if (tnode.children?.[0]?.data) {
      return tnode.children[0].data;
    }
    if (typeof tnode === 'string') {
      return tnode;
    }
    return '';
  };

  const renderers = {
    img: ({ tnode }: any) => {
      const { src, alt, width: imgWidth, height: imgHeight } = tnode.attributes || {};
      
      if (!src) return null;

      const imageWidth = screenWidth - 40;
      let imageHeight = 200;
      
      if (imgWidth && imgHeight) {
        const parsedWidth = parseInt(imgWidth.toString());
        const parsedHeight = parseInt(imgHeight.toString());
        if (!isNaN(parsedWidth) && !isNaN(parsedHeight) && parsedWidth > 0) {
          const ratio = parsedHeight / parsedWidth;
          imageHeight = Math.min(imageWidth * ratio, 300);
        }
      }

      return (
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: src }}
            style={[styles.articleImage, { width: imageWidth, height: imageHeight }]}
            resizeMode="cover"
            onError={() => console.log('Image failed to load:', src)}
          />
          {alt && <Text style={styles.imageCaption}>{alt}</Text>}
        </View>
      );
    },
    
    figure: ({ tnode, ...props }: any) => (
      <View style={styles.figureWrapper}>
        {props.children}
      </View>
    ),
    
    figcaption: ({ tnode }: any) => {
      const captionText = getTextContent(tnode);
      if (!captionText) return null;
      
      return (
        <Text style={styles.figureCaption}>
          {stripHtmlTags(captionText)}
        </Text>
      );
    },
  };

  const handlePostPress = (postId: number) => {
    navigation.push('PostDetail', { id: postId });
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <HomeHeader backButton noStretch />
        <View style={styles.centerContainer}>
          <ActivityIndicator size="large" color="#2E7D32" />
          <Text style={styles.loadingText}>Đang tải bài viết...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !post) {
    return (
      <SafeAreaView style={styles.container}>
        <HomeHeader backButton noStretch />
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>
            {error || 'Không tìm thấy bài viết'}
          </Text>
          <TouchableOpacity 
            style={styles.retryButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.retryButtonText}>Quay lại</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <HomeHeader backButton noStretch />
      
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        bounces={true}
      >
        {/* Hero Section */}
        {(post.permalink || post.thumbnail) && (
          <View style={styles.heroSection}>
            <Image
              source={{ uri: post.permalink || post.thumbnail }}
              style={styles.heroImage}
              resizeMode="cover"
              onError={() => console.log('Hero image failed to load')}
            />
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.7)']}
              style={styles.heroOverlay}
            />
            <View style={styles.heroContent}>
              <Text style={styles.heroTitle}>{post.title || 'Tiêu đề không có'}</Text>
            </View>
          </View>
        )}

        {/* Main Content */}
        <View style={styles.contentContainer}>
          
          {/* Article Header (if no hero image) */}
          {!(post.permalink || post.thumbnail) && (
            <View style={styles.articleHeader}>
              <Text style={styles.articleTitle}>{post.title || 'Tiêu đề không có'}</Text>
            </View>
          )}

          {/* Author & Date Info */}
          <View style={styles.metaSection}>
            <View style={styles.authorCard}>
              <View style={styles.authorAvatar}>
                <Text style={styles.avatarText}>
                  {getAuthorInitials(post.author)}
                </Text>
              </View>
              <View style={styles.authorInfo}>
                <Text style={styles.authorName}>{post.author || 'Tác giả ẩn danh'}</Text>
                <Text style={styles.publishDate}>
                  {formatDate(post.createdAt)}
                </Text>
                {post.views && typeof post.views === 'number' && (
                  <Text style={styles.viewCount}>
                    {post.views.toLocaleString('vi-VN')} lượt xem
                  </Text>
                )}
              </View>
            </View>
          </View>

          {/* Description Section */}
          {(post as any).description && (
            <View style={styles.descriptionSection}>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionIcon}>
                  <Text style={styles.iconText}>📄</Text>
                </View>
                <Text style={styles.sectionTitle}>Tóm tắt nội dung</Text>
              </View>
              <View style={styles.descriptionContent}>
                <HTML
                  source={{ html: (post as any).description }}
                  contentWidth={screenWidth - 80}
                  baseStyle={styles.descriptionText}
                  tagsStyles={{
                    p: styles.descriptionText,
                    span: styles.descriptionText,
                  }}
                />
              </View>
            </View>
          )}

          {/* Tags Section */}
          {(post as any).tags && (post as any).tags.length > 0 && (
            <View style={styles.tagsSection}>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionIcon}>
                  <Text style={styles.iconText}>🏷️</Text>
                </View>
                <Text style={styles.sectionTitle}>Thẻ bài viết</Text>
              </View>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tagsScrollView}>
                {(post as any).tags.map((tag: any, index: number) => (
                  <View key={tag.id || index} style={styles.tagItem}>
                    <Text style={styles.tagText}>#{tag.name}</Text>
                  </View>
                ))}
              </ScrollView>
            </View>
          )}

          {/* Article Content */}
          <View style={styles.articleContent}>
            {post.content ? (
              <HTML
                source={{ html: normalizeHtmlContent(post.content) }}
                contentWidth={screenWidth - 40}
                baseStyle={styles.baseTextStyle}
                tagsStyles={{
                  h1: styles.h1Style,
                  h2: styles.h2Style,
                  h3: styles.h3Style,
                  h4: styles.h4Style,
                  p: styles.paragraphStyle,
                  strong: styles.strongStyle,
                  em: styles.emStyle,
                  i: styles.italicStyle,
                  span: styles.spanStyle,
                  blockquote: styles.blockquoteStyle,
                  ul: styles.ulStyle,
                  li: styles.liStyle,
                }}
                renderers={renderers}
                ignoredStyles={['width', 'height', 'marginLeft'] as any}
                systemFonts={['System']}
              />
            ) : (
              <Text style={styles.noContentText}>Nội dung bài viết không có sẵn</Text>
            )}
          </View>

          {/* Table of Contents */}
          {post.indexOfContent && post.indexOfContent.length > 0 && (
            <View style={styles.tocSection}>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionIcon}>
                  <Text style={styles.iconText}>📋</Text>
                </View>
                <Text style={styles.sectionTitle}>Mục lục</Text>
              </View>

              <View style={styles.tocContent}>
                {post.indexOfContent.map((item: any, index: number) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.tocItem}
                    activeOpacity={0.7}
                  >
                    <View style={styles.tocText}>
                      {item.heading && (
                        <Text style={styles.tocHeading}>
                          {stripHtmlTags(item.heading)}
                        </Text>
                      )}
                      {item.content && (
                        <View style={styles.tocSummary}>
                          <Text style={styles.tocSummaryText}>
                            {cleanHtmlForToc(item.content)}
                          </Text>
                        </View>
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}

          {/* Related Posts */}
          {post.relatedPosts && post.relatedPosts.length > 0 && (
            <View style={styles.relatedSection}>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionIcon}>
                  <Text style={styles.iconText}>🔗</Text>
                </View>
                <Text style={styles.sectionTitle}>Bài viết liên quan</Text>
              </View>
              
              <View style={styles.relatedContent}>
                <PostCardList 
                  type="vertical" 
                  posts={post.relatedPosts} 
                  onPress={handlePostPress} 
                />
              </View>
            </View>
          )}

          {/* Topic and Series Info */}
          {((post as any).topic || (post as any).series) && (
            <View style={styles.metaInfoSection}>
              <View style={styles.sectionHeader}>
                <View style={styles.sectionIcon}>
                  <Text style={styles.iconText}>📚</Text>
                </View>
                <Text style={styles.sectionTitle}>Thông tin bổ sung</Text>
              </View>
              <View style={styles.metaInfoContent}>
                {(post as any).topic && (
                  <View style={styles.metaInfoItem}>
                    <View style={styles.metaInfoIcon}>
                      <Text style={styles.metaInfoIconText}>🎯</Text>
                    </View>
                    <View style={styles.metaInfoTextContainer}>
                      <Text style={styles.metaInfoLabel}>Chủ đề</Text>
                      <Text style={styles.metaInfoValue}>{(post as any).topic.name}</Text>
                    </View>
                  </View>
                )}
                {(post as any).series && (
                  <View style={styles.metaInfoItem}>
                    <View style={styles.metaInfoIcon}>
                      <Text style={styles.metaInfoIconText}>📖</Text>
                    </View>
                    <View style={styles.metaInfoTextContainer}>
                      <Text style={styles.metaInfoLabel}>Series</Text>
                      <Text style={styles.metaInfoValue}>{(post as any).series.title}</Text>
                    </View>
                  </View>
                )}
              </View>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  
  scrollView: {
    flex: 1,
  },
  
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  
  errorText: {
    fontSize: 16,
    color: '#E53E3E',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
  },

  retryButton: {
    backgroundColor: '#2E7D32',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },

  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },

  // Hero Section
  heroSection: {
    height: screenHeight * 0.4,
    position: 'relative',
  },
  
  heroImage: {
    width: '100%',
    height: '100%',
  },
  
  heroOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '60%',
  },
  
  heroContent: {
    position: 'absolute',
    bottom: 30,
    left: 20,
    right: 20,
  },
  
  heroTitle: {
    fontSize: 28,
    fontWeight: '800',
    color: '#FFFFFF',
    lineHeight: 36,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
  },

  // Content Container
  contentContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    marginTop: -20,
    paddingTop: 30,
    minHeight: screenHeight * 0.7,
  },

  // Article Header (for posts without hero image)
  articleHeader: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  
  articleTitle: {
    fontSize: 28,
    fontWeight: '800',
    color: '#1A202C',
    lineHeight: 36,
  },

  // Meta Section
  metaSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  
  authorCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F7FAFC',
    padding: 16,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#2E7D32',
  },
  
  authorAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#2E7D32',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  
  avatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  
  authorInfo: {
    flex: 1,
  },
  
  authorName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A202C',
    marginBottom: 4,
  },
  
  publishDate: {
    fontSize: 14,
    color: '#718096',
    fontWeight: '500',
    textAlign: 'left',
  },

  viewCount: {
    fontSize: 12,
    color: '#A0AEC0',
  },

  // Tags Section
  tagsSection: {
    marginHorizontal: 20,
    marginBottom: 24,
  },

  tagsScrollView: {
    paddingLeft: 0,
  },

  tagItem: {
    backgroundColor: '#E6FFFA',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#B2F5EA',
  },

  tagText: {
    fontSize: 13,
    color: '#2E7D32',
    fontWeight: '600',
  },

  // Article Content
  articleContent: {
    paddingHorizontal: 20,
    marginBottom: 40,
  },

  noContentText: {
    fontSize: 16,
    color: '#718096',
    textAlign: 'center',
    fontStyle: 'italic',
    padding: 20,
  },
  
  baseTextStyle: {
    fontSize: 17,
    lineHeight: 26,
    color: '#2D3748',
    fontFamily: 'System',
  },
  
  h1Style: {
    fontSize: 26,
    fontWeight: 'bold',
    color: '#1A202C',
    marginTop: 32,
    marginBottom: 16,
    lineHeight: 32,
  },
  
  h2Style: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1A202C',
    marginTop: 28,
    marginBottom: 14,
    lineHeight: 30,
  },
  
  h3Style: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1A202C',
    marginTop: 24,
    marginBottom: 12,
    lineHeight: 26,
  },
  
  h4Style: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A202C',
    marginTop: 20,
    marginBottom: 10,
    marginLeft: 0,
    lineHeight: 24,
    textAlign: 'justify',
  },
  
  paragraphStyle: {
    fontSize: 17,
    lineHeight: 26,
    color: '#2D3748',
    marginBottom: 14,
    textAlign: 'justify',
  },
  
  strongStyle: {
    fontWeight: '700',
    color: '#1A202C',
  },
  
  emStyle: {
    fontStyle: 'italic',
    color: '#4A5568',
  },
  
  italicStyle: {
    fontStyle: 'italic',
    color: '#4A5568',
  },
  
  spanStyle: {
    fontSize: 17,
    lineHeight: 26,
    color: '#2D3748',
  },
  
  blockquoteStyle: {
    borderLeftWidth: 4,
    borderLeftColor: '#2E7D32',
    paddingLeft: 16,
    paddingVertical: 12,
    marginVertical: 14,
    backgroundColor: '#F0FDF4',
    borderRadius: 8,
    fontStyle: 'italic',
  },

  ulStyle: {
    marginLeft: 0,
    paddingLeft: 20,
    marginVertical: 8,
  },

  liStyle: {
    marginLeft: 0,
    paddingLeft: 0,
    marginVertical: 2,
    textAlign: 'left',
  },

  // Image Styles
  imageContainer: {
    marginVertical: 20,
    alignItems: 'center',
  },
  
  articleImage: {
    borderRadius: 12,
    backgroundColor: '#F7FAFC',
  },
  
  imageCaption: {
    fontSize: 14,
    color: '#718096',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 10,
    paddingHorizontal: 20,
    lineHeight: 20,
  },
  
  figureWrapper: {
    marginVertical: 16,
    alignItems: 'center',
  },
  
  figureCaption: {
    fontSize: 14,
    color: '#718096',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 6,
    paddingHorizontal: 20,
    lineHeight: 20,
  },

  // Section Styles
  descriptionSection: {
    marginHorizontal: 20,
    marginBottom: 24,
  },

  descriptionContent: {
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#4299E1',
  },

  descriptionText: {
    fontSize: 15,
    lineHeight: 22,
    color: '#2D3748',
    fontStyle: 'italic',
  },

  tocSection: {
    marginHorizontal: 20,
    marginBottom: 32,
  },

  relatedSection: {
    marginHorizontal: 20,
    marginBottom: 32,
  },
  
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  
  sectionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E6FFFA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  
  iconText: {
    fontSize: 18,
  },

  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#1A202C',
    flex: 1,
  },

  // TOC Styles
  tocContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  
  tocItem: {
    marginBottom: 20,
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#FAFAFA',
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#2E7D32',
  },

  tocText: {
    flex: 1,
  },

  tocHeading: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1A202C',
    lineHeight: 24,
    marginBottom: 8,
  },

  tocSummary: {
    marginTop: 4,
  },

  tocSummaryText: {
    fontSize: 13,
    color: '#4A5568',
    lineHeight: 18,
    fontStyle: 'italic',
  },
  
  tocDescription: {
    fontSize: 14,
    color: '#4A5568',
    lineHeight: 18,
  },
  
  // Related Content
  relatedContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },

  // Meta Info Section
  metaInfoSection: {
    marginHorizontal: 20,
    marginBottom: 32,
  },

  metaInfoContent: {
    backgroundColor: '#F7FAFC',
    borderRadius: 12,
    padding: 16,
  },

  metaInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },

  metaInfoIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E6FFFA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },

  metaInfoIconText: {
    fontSize: 16,
  },

  metaInfoTextContainer: {
    flex: 1,
  },

  metaInfoLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#718096',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginBottom: 2,
  },

  metaInfoValue: {
    fontSize: 15,
    color: '#2D3748',
    fontWeight: '500',
  },
});

export default PostDetail;