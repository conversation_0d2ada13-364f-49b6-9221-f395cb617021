import { useState } from 'react';
import { changePassword } from 'api/users';

export const useChangePassword = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleChangePassword = async ({ body }: { body: { currentPassword: string; newPassword: string; confirmationPassword: string } }) => {
    try {
      setLoading(true);
      setError(null);
      const response = await changePassword({ body });
      console.log('useChangePassword Response:', response); // Debug log
      // If response is empty, assume success since status is 200
      return response || { success: true, message: 'Password changed successfully.' };
    } catch (err: any) {
      console.error('useChangePassword Error:', err); // Debug log
      const errorMessage = err?.message || err?.error || 'Failed to change password. Please try again.';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    changePassword: handleChangePassword,
  };
};