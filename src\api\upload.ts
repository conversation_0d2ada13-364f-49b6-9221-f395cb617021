import AsyncStorage from '@react-native-async-storage/async-storage';
import { apis } from 'utils/axios';
import { STORAGE } from 'constants/authentication';
import { AxiosError } from 'axios';

export const uploadService = async (formData: FormData): Promise<string> => {
  try {
    const token = await AsyncStorage.getItem(STORAGE.ACCESS_TOKEN);
    if (!token) {
      throw new Error('Không tìm thấy token. Vui lòng đăng nhập lại.');
    }

    console.log("in ra" + formData)
    const response = await apis().post('/files/upload', formData, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
      },
    });

    console.log('Upload response:', response?.data); // Debug response

    const uploadedUrl = response?.data?.links?.permalink;
    if (!uploadedUrl || typeof uploadedUrl !== 'string') {
      throw new Error('<PERSON><PERSON><PERSON>ng nhận được URL hợp lệ từ server');
    }

    return uploadedUrl;
  } catch (error) {
    const err = error as AxiosError;
    console.error('Lỗi upload file:', err.message, err.response?.data);
    throw new Error(
      err.response?.data?.message || 'Không thể upload file. Vui lòng thử lại.'
    );
  }
};