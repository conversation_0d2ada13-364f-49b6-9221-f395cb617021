import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { useForm } from 'react-hook-form';
import { map } from 'lodash';
import Toast from 'react-native-toast-message';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import CommonButton from 'components/CommonButton';
import {
  AUTHENTICATION_STEP,
  REGEX_INPUT,
} from 'constants/authentication';
import FormInput from 'components/FormInput';
import { forgotPassword } from 'api/users';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';

const schema = yup.object({
  phoneOrEmail: yup
    .string()
    .required('Vui lòng nhập email')
    .trim()
    .matches(REGEX_INPUT.EMAIL, 'Email chưa đúng định dạng'),
});

const ForgotPassForm = ({ setStep, setCurrentData, currentData }: any) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm({
    defaultValues: {
      phoneOrEmail: '',
    },
    resolver: yupResolver(schema),
  });

  const onSubmit = async (values: any) => {
    const response = await forgotPassword({ body: values });
    if (response?.code) {
      if (response?.validationErrors) {
        map(response?.validationErrors, (item): any => {
          setError(item?.field, {
            message: item.message,
          });
        });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Thông báo',
          text2: response?.message || 'Có lỗi xảy ra, vui lòng thử lại',
          position: 'top',
        });
      }
    } else {
      setCurrentData({ ...currentData, phoneOrEmail: values?.phoneOrEmail });
      setStep(AUTHENTICATION_STEP.FORGOT_PASS_CREATE_NEW_PASS);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View>
          <Text style={styles.subTitle}>Quên mật khẩu</Text>
        </View>
        <View style={styles.item}>
          <FormInput
            label="Email"
            control={control}
            required
            placeholder="Nhập email"
            name="phoneOrEmail"
            errors={errors?.phoneOrEmail}
          />
        </View>
        <View style={commonStyles.rowCenter}>
          <CommonButton
            onPress={handleSubmit(onSubmit)}
            customStyles={{ backgroundColor: colors['Moss/500'] }}
          >
            Xác nhận
          </CommonButton>
        </View>
      </View>
      <View style={commonStyles.rowCenter}>
        <TouchableOpacity onPress={() => setStep(AUTHENTICATION_STEP.LOGIN)}>
          <Text style={styles.text}>
            <Text style={styles.registerText}>Quay lại đăng nhập</Text>
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  content: {
    padding: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
    marginBottom: metrics.spacing4,
  },
  item: {
    marginBottom: metrics.spacing3, 
  },
  subTitle: {
    fontSize: fonts.size.h2,
    fontWeight: '600',
    color: colors['Grayiron/500'],
    marginBottom: metrics.spacing4,
  },
  title: {
    fontSize: fonts.size.h1,
    fontWeight: '600',
    color: colors['Moss/700'],
    marginBottom: metrics.spacing4,
  },
  text: {
    fontSize: fonts.size.input,
    fontWeight: '400',
    color: colors['Grayiron/400'],
    marginBottom: metrics.spacing4,
  },
  registerText: {
    fontSize: fonts.size.input,
    fontWeight: '500',
    color: colors['Moss/500'],
    marginBottom: metrics.spacing4,
  },
});

export default ForgotPassForm;