import React from 'react';
import { View, StyleSheet } from 'react-native';

import metrics from 'themes/metrics';
import PostCard from 'components/PostCard';
import { Divider, Text } from '@ui-kitten/components';
import fonts from 'themes/fonts';
import colors from 'themes/colors';

import { useNavigation } from '@react-navigation/native';
import { map } from 'lodash';

type PostBlock = {
  title?: string;
  posts?: any;
};

const PostBlock = ({ title, posts }: PostBlock) => {
  const navigation = useNavigation();

  return (
    <View style={styles.content}>
      <Text style={styles.subTitle}>{title}</Text>
      {map(posts, item => (
        <React.Fragment key={item?.id}>
          <PostCard
            key={item?.id}
            title={item.title}
            author={item.author}
            uri={item.thumbnail}
            avatarUri={item.thumbnail}
            topicName={item?.topic?.name}
            onPress={() => navigation.navigate('PostDetail', { id: item?.id })}
          />
          <Divider />
        </React.Fragment>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
    marginVertical: metrics.spacing2,
  },
  subTitle: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Moss/600'],
    marginBottom: metrics.spacing4,
  },
});

export default PostBlock;
