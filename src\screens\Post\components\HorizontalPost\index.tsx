import React from 'react';
import { StyleSheet, ScrollView } from 'react-native';
import { useNavigation } from '@react-navigation/native';

import metrics from 'themes/metrics';
import PostCard from 'components/PostCard';

import { usePostsByTopicId } from '../PostByTopic/hooks';
import { map } from 'lodash';

const HorizontalPost = () => {
  const navigation = useNavigation();
  const { posts } = usePostsByTopicId({ id: 2, page: 1, size: 5 });
  return (
    <ScrollView
      contentContainerStyle={styles.contentContainer}
      bounces={false}
      horizontal
      style={styles.container}
      showsHorizontalScrollIndicator={false}>
      {map(posts, (post: any) => (
        <PostCard
          key={post.id}
          type="full"
          customStyles={styles.item}
          title={post.title}
          author={post.author}
          uri={post.thumbnail}
          avatarUri={post.thumbnail}
          topicName={post?.topic?.name}
          createdAt={post?.createdAt}
          onPress={() => navigation.navigate('PostDetail', { id: post.id })}
        />
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: metrics.spacing4,
  },
  contentContainer: {
    paddingRight: metrics.spacing4,
  },
  item: {
    marginRight: metrics.spacing4,
    maxWidth: metrics.fullWidth - metrics.spacing4 * 3,
  },
});

export default HorizontalPost;
