import { find, forEach } from 'lodash';
import numeral from 'numeral';
import cities from 'assets/city.json';
import districts from 'assets/district.json';
import wards from 'assets/ward.json';

export const formatNumber = (value: any) => numeral(value).format('0,0');

export const flatTopics = (topics: any) => {
  const newTopics = [] as any;

  const flat = (childTopics: any) => {
    forEach(childTopics, item => {
      if (item?.childTopics) {
        flat(item?.childTopics);
      }
      newTopics.push(item);
    });
  };
  flat(topics);

  return newTopics;
};

export const flatCategories = (categories: any) => {
  const newCategories = [] as any;

  const flat = (childCategories: any) => {
    forEach(childCategories, item => {
      if (item?.childCategories) {
        flat(item?.childCategories);
      }
      newCategories.push(item);
    });
  };
  flat(categories);

  return newCategories;
};

export const safeParseJson = (value: string) => {
  try {
    if (!value) {
      return [];
    }
    return JSON.parse(value);
  } catch (error) {
    console.log(error);
    return [];
  }
};

export const getCityName = (id: string | number) => {
  const city: any = find(cities, { id });
  if (!city) {
    return '';
  }
  return `${city?.type_city} ${city?.name}`;
};

export const getDistrictName = (id: string | number) => {
  const district: any = find(districts, { id });
  if (!district) {
    return '';
  }
  return `${district?.type_district} ${district?.name}`;
};

export const getWardName = (id: string | number) => {
  const ward: any = find(wards, { id });
  if (!ward) {
    return '';
  }
  return `${ward?.type_ward} ${ward?.name}`;
};

export const getFullAddress = ({ cityId, districtId, wardId }: any) => {
  if (!cityId && !districtId && !wardId) {
    return null;
  }
  return `${getWardName(wardId)}, ${getDistrictName(districtId)}, ${getCityName(
    cityId,
  )}`;
};
