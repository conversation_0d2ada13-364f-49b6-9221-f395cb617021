/* eslint-disable react-hooks/exhaustive-deps */
import { getProductDetail } from 'api/products';
import { addToCart } from 'api/cart';
import { useEffect, useState } from 'react';

export const useProductDetail = ({ id }: any) => {
  const [loading, setLoading] = useState(false);
  const [product, setProduct] = useState({});
  const [error, setError] = useState<string | null>(null);

  const fetchProduct = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getProductDetail({ id });
      setProduct(response as any);
      console.log("in ra sản phẩm là : " ,  response);
    } catch (error: any) {
      console.error('Error fetching product:', error);
      setError(error?.message || 'Không thể tải thông tin sản phẩm');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProduct();
  }, []);

  return {
    loading,
    product,
    error,
  };
};
