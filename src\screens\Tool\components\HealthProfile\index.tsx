import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import MenuButton from 'components/MenuButton';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import commonStyles from 'themes/commonStyles';
import { useNavigation } from '@react-navigation/native';
import { Divider } from '@ui-kitten/components';

const Item = ({ label, value, iconName }: any) => (
  <View style={styles.item}>
    <Icon
      style={styles.icon}
      color={colors['Moss/400']}
      name={iconName}
      size={30}
    />
    <View>
      <Text style={commonStyles.mainText}>{label}</Text>
      <Text style={commonStyles.mainText}>{value}</Text>
    </View>
  </View>
);
const HealthProfile = () => {
  const navigation = useNavigation();
  return (
    <View style={styles.container}>
      <View style={commonStyles.rowSpaceBetween}>
        <Text style={styles.title}><PERSON><PERSON> sơ sức khỏe</Text>
        <TouchableOpacity onPress={() => navigation.navigate('HealthProfile')}>
          <Text style={styles.subTitle}>
            Cập nhật
            <Icon
              style={styles.icon}
              color={colors['Grayiron/600']}
              name={'chevron-right'}
              size={16}
            />
          </Text>
        </TouchableOpacity>
      </View>
      <View style={styles.content}>
        <View style={[commonStyles.rowSpaceBetween, styles.rowItem]}>
          <Item iconName="scale-bathroom" label="Cân nặng" value="65 kg" />
          <Item iconName="human-male-height" label="Chiều cao" value="165 cm" />
        </View>
        <View style={[commonStyles.rowSpaceBetween, styles.rowItem]}>
          <Item
            iconName="application-settings-outline"
            label="BMI"
            value="18.74"
          />
          <Item iconName="arm-flex-outline" label="BMR" value="1.427 kcal" />
        </View>
        <View style={styles.rowItem}>
          <Item
            iconName="heart-multiple"
            label="Cường độ hoạt động thể chất"
            value="Cường độ nhiều"
          />
        </View>
        <Divider style={styles.mb4} />
        <TouchableOpacity
          onPress={() =>
            navigation?.navigate('WebviewScreen', {
              uri: 'https://hathyo.com/apps/hathyo-tools?mode=mobile',
              title: 'Công cụ',
            })
          }
          style={commonStyles.rowSpaceBetween}>
          <View style={styles.item}>
            <Text style={commonStyles.mainText}>
              Tìm hiểu thêm các công cụ khác
            </Text>
            <Icon
              style={styles.icon}
              color={colors['Grayiron/600']}
              name={'chevron-right'}
              size={16}
            />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  content: {
    padding: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
  },
  mb4: {
    marginBottom: metrics.spacing4,
  },
  item: {
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',
  },
  rowItem: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    paddingBottom: metrics.spacing4,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  subTitle: {
    fontSize: fonts.size.input,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  icon: {
    marginRight: metrics.spacing3,
  },
});

export default HealthProfile;
