import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Text,
  FlatList,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import Product from 'components/Product';
import Tag from 'components/Tag';
import AddToCartModal from 'components/shared/AddToCartModal';
import { addRecentProducts } from 'utils/cart';
import { chunk } from 'lodash';

interface ProductGridProps {
  products: any[];
  loading?: boolean;
  loadingMore?: boolean;
  hasMore?: boolean;
  title?: string;
  hasBackground?: boolean;
  filter?: boolean;
  customStyles?: any;
  onLoadMore?: () => void;
  onRefresh?: () => void;
  refreshing?: boolean;
  onProductPress?: (product: any) => void;
  onAddToCart?: (product: any) => void;
}

const ProductGrid: React.FC<ProductGridProps> = ({
  products = [],
  loading = false,
  loadingMore = false,
  hasMore = false,
  title,
  hasBackground = false,
  filter = false,
  customStyles,
  onLoadMore,
  onRefresh,
  refreshing = false,
  onProductPress,
  onAddToCart,
}) => {
  const navigation = useNavigation<any>();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);

  // Chia mảng sản phẩm thành các nhóm 2 sản phẩm
  const productPairs = chunk(products, 2);

  const openModal = (product: any) => {
    setSelectedProduct(product);
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
    setSelectedProduct(null);
  };

  const handleProductPress = (product: any) => {
    if (onProductPress) {
      onProductPress(product);
    } else {
      addRecentProducts(product);
      navigation?.navigate('ProductDetail', { id: product?.id });
    }
  };

  const handleLoadMore = () => {
    if (hasMore && !loadingMore && onLoadMore) {
      onLoadMore();
    }
  };

  const renderProductPair = ({ item: pair }: { item: any[] }) => (
    <View style={styles.row}>
      {pair.map((product) => (
        <View key={product?.id} style={styles.item}>
          <Product
            customStyles={{ width: metrics.fullWidth / 2 - metrics.spacing6 }}
            title={product?.title}
            uri={product?.mainImageUrl}
            price={product?.price}
            anchoPrice={product?.anchoPrice}
            rating={product?.rating}
            discountPercent={product?.discountPercent}
            onPress={() => onProductPress ? onProductPress(product) : handleProductPress(product)}
            onAddToCart={() => onAddToCart ? onAddToCart(product) : openModal(product)}
          />
        </View>
      ))}
      {pair.length === 1 && <View style={styles.emptyItem} />}
    </View>
  );

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.footer}>
        <ActivityIndicator size="small" color={colors['Moss/700']} />
      </View>
    );
  };

  const renderFilterTags = () => {
    if (!filter) return null;
    
    return (
      <ScrollView
        horizontal
        style={styles.tags}
        bounces
        showsHorizontalScrollIndicator={false}
      >
        <Tag customStyles={styles.tag} size="small" type="success" title="Bán chạy" />
        <Tag customStyles={styles.tag} size="small" title="Giá thấp" />
        <Tag customStyles={styles.tag} size="small" title="Giá cao" />
      </ScrollView>
    );
  };

  const renderContent = () => {
    if (loading && !loadingMore && products.length === 0) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors['Moss/700']} />
          <Text style={styles.loadingText}>Đang tải sản phẩm...</Text>
        </View>
      );
    }

    if (products.length === 0 && !loading) {
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Không có sản phẩm nào</Text>
        </View>
      );
    }

    return (
      <FlatList
        data={productPairs}
        renderItem={renderProductPair}
        keyExtractor={(_, index) => `pair-${index}`}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.productsContainer}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        refreshing={refreshing}
        onRefresh={onRefresh}
      />
    );
  };

  return (
    <View style={[styles.container, hasBackground && styles.background, customStyles]}>
      {title && <Text style={styles.title}>{title}</Text>}
      {renderFilterTags()}
      {renderContent()}
      
      <AddToCartModal
        visible={modalVisible}
        product={selectedProduct}
        onClose={closeModal}
        onAddToCartSuccess={() => {
          // Optional: Add any success callback logic here
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
    marginTop: metrics.spacing4,
  },
  background: {
    backgroundColor: colors['Moss/50'],
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: metrics.spacing4,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
    paddingHorizontal: metrics.spacing4,
  },
  tag: {
    marginRight: metrics.spacing2,
  },
  tags: {
    marginBottom: metrics.spacing4,
    marginHorizontal: metrics.spacing4,
  },
  productsContainer: {
    paddingHorizontal: metrics.spacing4,
    paddingBottom: metrics.spacing4,
  },
  item: {
    width: '48%',
  },
  emptyItem: {
    width: '48%',
  },
  footer: {
    paddingVertical: metrics.spacing4,
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: metrics.spacing6,
  },
  loadingText: {
    fontSize: 16,
    color: colors['Gray/600'],
    marginTop: metrics.spacing2,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: metrics.spacing6,
  },
  emptyText: {
    fontSize: 16,
    color: colors['Gray/600'],
    textAlign: 'center',
  },
});

export default ProductGrid;
