import React from 'react';
import { View, StyleSheet } from 'react-native';

import metrics from 'themes/metrics';
import MenuButton from 'components/MenuButton';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import colors from 'themes/colors';
import { useNavigation } from '@react-navigation/native';

const itemWidth =
  (metrics.fullWidth - metrics.spacing4 * 2 - metrics.spacing2 * 3) / 4;
const Menu = () => {
  const navigation = useNavigation()
  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <MenuButton
          customStyles={{ width: itemWidth }}
          iconName="fast-food-outline"
          title="Dinh dưỡng"
          onPress={() =>
            navigation?.navigate('TopicDetail', {
              id: 2,
              treeId: '2',
              name: 'Dinh dưỡng',
            })
          }
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          iconName="heart-circle-outline"
          title="Tinh thần"
          onPress={() =>
            navigation?.navigate('TopicDetail', {
              id: 3,
              treeId: '3',
              name: '<PERSON><PERSON> thần',
            })
          }
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          imgIcon={
            <MaterialCommunityIcons
              name="run-fast"
              size={24}
              color={colors['Moss/400']}
            />
          }
          title="Vận động"
          onPress={() =>
            navigation?.navigate('TopicDetail', {
              id: 4,
              treeId: '4',
              name: 'Vận động',
            })
          }
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          imgIcon={
            <MaterialCommunityIcons
              name="virus-outline"
              size={24}
              color={colors['Moss/400']}
            />
          }
          title="Bệnh lí"
          onPress={() =>
            navigation?.navigate('TopicDetail', {
              id: 1,
              treeId: '1',
              name: 'Bệnh lí',
            })
          }
        />
      </View>
      {/* <View style={styles.row}>
        <MenuButton
          customStyles={{ width: itemWidth }}
          imgIcon={
            <MaterialCommunityIcons
              name="application-settings-outline"
              size={24}
              color={colors['Moss/400']}
            />
          }
          title="Tính BMI"
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          imgIcon={
            <MaterialIcons
              name="sports-score"
              size={24}
              color={colors['Moss/400']}
            />
          }
          title="Bài tập"
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          imgIcon={
            <MaterialCommunityIcons
              name="countertop-outline"
              size={24}
              color={colors['Moss/400']}
            />
          }
          title="Khuyến mãi"
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          imgIcon={
            <MaterialCommunityIcons
              name="chat-outline"
              size={24}
              color={colors['Moss/400']}
            />
          }
          title="Chat với AI"
        />
      </View> */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: metrics.spacing4,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  row: {
    justifyContent: 'space-around',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexDirection: 'row',
    gap: metrics.spacing2,
    marginBottom: metrics.spacing3,
  },
});

export default Menu;
