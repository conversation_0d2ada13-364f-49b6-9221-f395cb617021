import React from 'react';
import { StyleSheet } from 'react-native';

import metrics from 'themes/metrics';
import FastImage from 'react-native-fast-image';
import SwiperFlatList from 'react-native-swiper-flatlist';

import { Pagination, PaginationProps } from 'react-native-swiper-flatlist';
import colors from 'themes/colors';

export const CustomPagination = (
  props: JSX.IntrinsicAttributes & PaginationProps,
) => {
  return (
    <Pagination
      {...props}
      paginationStyle={styles.paginationContainer}
      paginationStyleItem={styles.pagination}
      paginationDefaultColor={colors['Grayiron/300']}
      paginationActiveColor={colors['Moss/400']}
      paginationStyleItemActive={styles.itemActive}
      paginationStyleItemInactive={styles.item}
    />
  );
};

const ProductImages = ({ uri }: any) => {
  return (
    <SwiperFlatList
      style={styles.marginBottom}
      autoplay
      showPagination
      PaginationComponent={CustomPagination}
      data={uri}
      renderItem={({ item }) => (
        <FastImage
          // fallback={require('assets/images/product-fallback-image.png')}
          style={styles.container}
          source={{ uri: item }}
        />
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    width: metrics.fullWidth - metrics.spacing4 * 4,
    height: metrics.fullWidth - metrics.spacing4 * 4,
    marginHorizontal: metrics.spacing4 * 2,
    borderRadius: metrics.radius4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationContainer: {
    top: metrics.fullWidth - metrics.spacing4 * 5,
  },
  pagination: {
    borderRadius: 2,
    marginHorizontal: metrics.spacing1,
  },
  item: {
    width: 6,
    height: 6,
    borderRadius: metrics.radius1,
  },
  itemActive: {
    width: 16,
    height: 6,
    borderRadius: metrics.radius1,
  },
  marginBottom: {
    marginVertical: metrics.spacing4,
  },
});

export default ProductImages;
