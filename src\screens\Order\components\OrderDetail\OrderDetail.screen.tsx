import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Text,
  ScrollView,
  ActivityIndicator,
  Image,
  TouchableOpacity,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import HomeHeader from 'components/Header/HomeHeader';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import useOrderDetail from './hooks';
import { Order, OrderItem } from 'types/order';
import { ORDER_STATUS } from 'themes/constant';
import CustomAlert from 'utils/widgets/CustomAlert';

const OrderDetailScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const orderId = route.params?.orderId as number;
  const { order, loading, error, cancelOrder, confirmOrderReceived, submitProductReview } = useOrderDetail(orderId);
  const [alert, setAlert] = useState<{
    visible: boolean;
    type?: 'success' | 'error' | 'warning' | 'info' | 'confirm' | 'radio';
    title: string;
    message?: string;
    buttons?: { text: string; onPress?: (selectedReason?: string) => void; style?: 'default' | 'cancel' | 'destructive' }[];
    radioOptions?: { label: string; value: string }[];
    showInput?: boolean;
    inputPlaceholder?: string;
    onInputChange?: (text: string) => void;
  }>({
    visible: false,
    title: '',
  });
  const [cancelReason, setCancelReason] = useState<string | null>(null);
  const [reviewRating, setReviewRating] = useState<string | null>(null);
  const [reviewComment, setReviewComment] = useState<string>('');

  const handleRefresh = async () => {
    const { fetchOrder } = useOrderDetail(orderId);
    await fetchOrder();
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.center]}>
        <ActivityIndicator size="large" color={colors['Primary/400']} />
      </View>
    );
  }

  if (error || !order) {
    return (
      <View style={[styles.container, styles.center]}>
        <Text style={commonStyles.errorText}>{error || 'Không tìm thấy đơn hàng'}</Text>
        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors['Primary/50'], borderColor: colors['Primary/200'] }]}
          onPress={handleRefresh}
          activeOpacity={0.8}
        >
          <MaterialCommunityIcons
            name="refresh"
            size={24}
            color={colors['Primary/500']}
            style={{ marginRight: 10 }}
          />
          <Text style={[styles.actionText, { color: colors['Primary/500'] }]}>Thử lại</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const statusInfo = ORDER_STATUS[order.status as keyof typeof ORDER_STATUS] || ORDER_STATUS.PENDING;
  const statusColor =
    order.status === ORDER_STATUS.PENDING.key || order.status === ORDER_STATUS.ACCEPTED.key
      ? colors['Danger/200']
      : order.status === ORDER_STATUS.IN_TRANSIT.key
      ? colors['Primary/400']
      : order.status === ORDER_STATUS.DELIVERED.key || order.status === ORDER_STATUS.RECEIVED.key
      ? colors['Moss/400']
      : colors['Danger/400'];

  const handleCancelConfirmation = () => {
    setCancelReason(null);
    setAlert({
      visible: true,
      type: 'radio',
      title: 'Hủy đơn hàng',
      message: 'Vui lòng chọn lý do hủy đơn hàng:',
      radioOptions: [
        { label: 'Thay đổi thông tin nhận hàng (địa chỉ, số điện thoại...)', value: 'change_info' },
        { label: 'Thêm/thay đổi mã giảm giá', value: 'change_discount' },
        { label: 'Thay đổi sản phẩm (kích thước, màu sắc, số lượng…)', value: 'change_product' },
        { label: 'Tìm thấy chỗ khác giá tốt hơn', value: 'better_price' },
        { label: 'Không còn nhu cầu mua', value: 'no_demand' },
        { label: 'Quá trình thanh toán gặp lỗi/rắc rối', value: 'payment_issue' },
        { label: 'Thời gian giao hàng lâu hơn mong đợi', value: 'long_delivery' },
        { label: 'Sản phẩm không còn hàng', value: 'out_of_stock' },
        { label: 'Khác', value: 'other' },
      ],
      buttons: [
        {
          text: 'Hủy bỏ',
          style: 'cancel',
          onPress: () => setAlert({ ...alert, visible: false }),
        },
        {
          text: 'Xác nhận',
          style: 'destructive',
          onPress: async (reason) => {
            if (!reason) {
              setAlert({
                visible: true,
                type: 'error',
                title: 'Lỗi',
                message: 'Vui lòng chọn lý do hủy đơn.',
                buttons: [{ text: 'Đóng', onPress: () => setAlert({ ...alert, visible: false }) }],
              });
              return;
            }
            setAlert({ ...alert, visible: false });
            const success = await cancelOrder(reason);
            setAlert({
              visible: true,
              type: success ? 'success' : 'error',
              title: success ? 'Thành công' : 'Lỗi',
              message: success ? 'Đơn hàng đã được hủy.' : 'Không thể hủy đơn hàng. Vui lòng thử lại.',
              buttons: [{ text: 'Đóng', onPress: () => setAlert({ ...alert, visible: false }) }],
            });
          },
        },
      ],
    });
  };

  const handleConfirmReceived = () => {
    setAlert({
      visible: true,
      type: 'confirm',
      title: 'Xác nhận nhận hàng',
      message: 'Bạn có chắc chắn đã nhận được đơn hàng này?',
      buttons: [
        {
          text: 'Hủy bỏ',
          style: 'cancel',
          onPress: () => setAlert({ ...alert, visible: false }),
        },
        {
          text: 'Xác nhận',
          onPress: async () => {
            setAlert({ ...alert, visible: false });
            const success = await confirmOrderReceived();
            setAlert({
              visible: true,
              type: success ? 'success' : 'error',
              title: success ? 'Thành công' : 'Lỗi',
              message: success ? 'Đã xác nhận nhận hàng.' : 'Không thể xác nhận nhận hàng. Vui lòng thử lại.',
              buttons: [
                {
                  text: 'Đóng',
                  onPress: () => {
                    setAlert({ ...alert, visible: false });
                    if (success) {
                      handleRefresh();
                    }
                  },
                },
              ],
            });
          },
        },
      ],
    });
  };

  const handleReview = (item: OrderItem) => {
  setAlert({
    visible: true,
    type: 'radio',
    title: `Đánh giá sản phẩm: ${item.productTitle}`,
    message: 'Vui lòng chọn số sao và nhập nhận xét (nếu có):',
    showInput: true,
    inputPlaceholder: 'Nhập nhận xét của bạn...',
    onInputChange: (text: string) => setReviewComment(text),
    radioOptions: [
      { label: '5 sao - Tuyệt vời', value: '5' },
      { label: '4 sao - Tốt', value: '4' },
      { label: '3 sao - Trung bình', value: '3' },
      { label: '2 sao - Tạm được', value: '2' },
      { label: '1 sao - Kém', value: '1' },
    ],
    buttons: [
      {
        text: 'Hủy bỏ',
        style: 'cancel',
        onPress: () => setAlert({ ...alert, visible: false }),
      },
      {
        text: 'Gửi đánh giá',
        onPress: async (rating) => {
          if (!rating) {
            setAlert({
              visible: true,
              type: 'error',
              title: 'Lỗi',
              message: 'Vui lòng chọn số sao.',
              buttons: [{ text: 'Đóng', onPress: () => setAlert({ ...alert, visible: false }) }],
            });
            return;
          }
          setAlert({ ...alert, visible: false });
          const success = await submitProductReview(item.productId, parseInt(rating), reviewComment.trim());
          setAlert({
            visible: true,
            type: success ? 'success' : 'error',
            title: success ? 'Thành công' : 'Lỗi',
            message: success ? 'Đánh giá đã được gửi.' : error || 'Không thể gửi đánh giá. Vui lòng thử lại.',
            buttons: [
              {
                text: 'Đóng',
                onPress: () => {
                  setAlert({ ...alert, visible: false });
                  if (success) {
                    handleRefresh();
                  }
                },
              },
            ],
          });
        },
      },
    ],
  });
};

  const formatDate = (date: string | Date) => {
    try {
      const parsedDate = new Date(date);
      return parsedDate.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return 'N/A';
    }
  };

  const formatCurrency = (value: number | undefined) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(value ?? 0);
  };

  return (
    <View style={styles.container}>
      <HomeHeader backButton title="Chi tiết đơn hàng" noStretch />
      <ScrollView style={styles.contentContainer} showsVerticalScrollIndicator={false}>
        <View style={[styles.statusContainer, { borderLeftWidth: 4, borderLeftColor: statusColor }]}>
          <View style={styles.statusHeader}>
            <View style={[styles.statusIconWrapper, { backgroundColor: `${statusColor}20` }]}>
              <MaterialCommunityIcons
                name={
                  order.status === ORDER_STATUS.PENDING.key || order.status === ORDER_STATUS.ACCEPTED.key
                    ? 'clock-outline'
                    : order.status === ORDER_STATUS.IN_TRANSIT.key
                    ? 'truck-delivery-outline'
                    : order.status === ORDER_STATUS.DELIVERED.key || order.status === ORDER_STATUS.RECEIVED.key
                    ? 'package-variant-closed'
                    : 'close-circle-outline'
                }
                size={28}
                color={statusColor}
              />
            </View>
            <View style={styles.statusTextContainer}>
              <Text style={styles.orderCode}>Mã đơn: {order.orderCode ?? 'N/A'}</Text>
              <Text style={[styles.statusText, { color: statusColor }]}>{statusInfo.name ?? 'Chờ xử lý'}</Text>
              <Text style={styles.orderDate}>{formatDate(order.createdAt)}</Text>
            </View>
          </View>
        </View>
        <View style={styles.detailsContainer}>
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons name="truck-delivery" size={24} color={colors['Primary/400']} />
              <Text style={styles.sectionTitle}>Thông tin giao hàng</Text>
            </View>
            <View style={styles.infoRow}>
              <MaterialCommunityIcons name="account-outline" size={20} color={colors['Grayiron/500']} />
              <Text style={styles.infoText}>{order.customerName ?? 'N/A'}</Text>
            </View>
            <View style={styles.infoRow}>
              <MaterialCommunityIcons name="phone-outline" size={20} color={colors['Grayiron/500']} />
              <Text style={styles.infoText}>{order.customerPhone ?? 'N/A'}</Text>
            </View>
            <View style={styles.infoRow}>
              <MaterialCommunityIcons name="map-marker-outline" size={20} color={colors['Grayiron/500']} />
              <Text style={styles.infoText}>{order.customerAddress ?? 'N/A'}</Text>
            </View>
            {order.userNote && (
              <View style={styles.infoRow}>
                <MaterialCommunityIcons name="note-text-outline" size={20} color={colors['Grayiron/500']} />
                <Text style={styles.infoText}>{order.userNote}</Text>
              </View>
            )}
          </View>
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons name="storefront-outline" size={24} color={colors['Primary/400']} />
              <Text style={styles.sectionTitle}>Cửa hàng</Text>
            </View>
            <TouchableOpacity
              style={styles.merchantRow}
              activeOpacity={0.8}
              onPress={() => {
                if (order.merchant?.id) {
                  navigation.navigate('Merchant', { merchantId: String(order.merchant.id) });
                }
              }}
            >
              <Image
                source={{ uri: order.merchant?.logo ?? 'https://via.placeholder.com/56' }}
                style={styles.merchantLogo}
              />
              <View style={styles.merchantInfo}>
                <Text style={styles.merchantName}>{order.merchant?.storeName ?? 'N/A'}</Text>
                <Text style={styles.merchantAddress} numberOfLines={2}>
                  {[
                    order.merchant?.address,
                    order.merchant?.ward,
                    order.merchant?.district,
                    order.merchant?.city,
                  ]
                    .filter(Boolean)
                    .join(', ') || 'N/A'}
                </Text>
              </View>
              <MaterialCommunityIcons name="chevron-right" size={28} color={colors['Grayiron/400']} />
            </TouchableOpacity>
          </View>
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons name="shopping-outline" size={24} color={colors['Primary/400']} />
              <Text style={styles.sectionTitle}>Sản phẩm</Text>
            </View>
            {order.orderItems?.length ? (
              order.orderItems.map((item: OrderItem) => (
                <View key={item.id} style={styles.productItem}>
                  <Image
                    source={{ uri: item.productVariantImage ?? 'https://via.placeholder.com/68' }}
                    style={styles.productImage}
                  />
                  <View style={styles.productDetails}>
                    <Text style={styles.productTitle} numberOfLines={2}>
                      {item.productTitle ?? 'N/A'}
                    </Text>
                    <Text style={styles.productVariant}>{item.productVariantTitle ?? 'N/A'}</Text>
                    <Text style={styles.productPrice}>{formatCurrency(item.totalPrice)}</Text>
                    {order.status === ORDER_STATUS.RECEIVED.key && !item.rated && (
                      <TouchableOpacity
                        style={styles.reviewButton}
                        onPress={() => handleReview(item)}
                        activeOpacity={0.8}
                      >
                        <Text style={styles.reviewButtonText}>Đánh giá</Text>
                      </TouchableOpacity>
                    )}
                    {order.status === ORDER_STATUS.RECEIVED.key && item.rated && (
                      <Text style={styles.ratedText}>Đã đánh giá</Text>
                    )}
                  </View>
                  <Text style={styles.productQuantity}>x{item.quantity ?? 0}</Text>
                </View>
              ))
            ) : (
              <Text style={styles.infoText}>Không có sản phẩm</Text>
            )}
          </View>
          <View style={[styles.section, { borderBottomWidth: 0 }]}>
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons name="credit-card-outline" size={24} color={colors['Primary/400']} />
              <Text style={styles.sectionTitle}>Chi tiết thanh toán</Text>
            </View>
            <View style={styles.paymentRow}>
              <Text style={styles.paymentLabel}>Tiền hàng</Text>
              <Text style={styles.paymentValue}>{formatCurrency(order.productsPrice)}</Text>
            </View>
            <View style={styles.paymentRow}>
              <Text style={styles.paymentLabel}>Phí vận chuyển</Text>
              <Text style={styles.paymentValue}>{formatCurrency(order.shippingFee)}</Text>
            </View>
            {order.discountProductsPrice > 0 && (
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Giảm giá sản phẩm</Text>
                <Text style={[styles.paymentValue, { color: colors['Danger/500'] }]}>
                  -{formatCurrency(order.discountProductsPrice)}
                </Text>
              </View>
            )}
            {order.discountShippingPrice > 0 && (
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Giảm giá vận chuyển</Text>
                <Text style={[styles.paymentValue, { color: colors['Danger/500'] }]}>
                  -{formatCurrency(order.discountShippingPrice)}
                </Text>
              </View>
            )}
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Tổng tiền</Text>
              <Text style={styles.totalValue}>{formatCurrency(order.totalPrice)}</Text>
            </View>
          </View>
        </View>
      </ScrollView>
      {(order.status === ORDER_STATUS.PENDING.key || order.status === ORDER_STATUS.DELIVERED.key) && (
        <View style={styles.actionContainer}>
          {order.status === ORDER_STATUS.PENDING.key && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors['Danger/50'], borderColor: colors['Danger/200'] }]}
              onPress={handleCancelConfirmation}
              activeOpacity={0.8}
            >
              <MaterialCommunityIcons
                name="close-circle-outline"
                size={24}
                color={colors['Danger/500']}
                style={{ marginRight: 10 }}
              />
              <Text style={[styles.actionText, { color: colors['Danger/500'] }]}>Hủy đơn hàng</Text>
            </TouchableOpacity>
          )}
          {order.status === ORDER_STATUS.DELIVERED.key && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors['Moss/50'], borderColor: colors['Moss/200'] }]}
              onPress={handleConfirmReceived}
              activeOpacity={0.8}
            >
              <MaterialCommunityIcons
                name="check-circle-outline"
                size={24}
                color={colors['Moss/500']}
                style={{ marginRight: 10 }}
              />
              <Text style={[styles.actionText, { color: colors['Moss/500'] }]}>Xác nhận đã nhận</Text>
            </TouchableOpacity>
          )}
        </View>
      )}
      <CustomAlert
        visible={alert.visible}
        type={alert.type}
        title={alert.title}
        message={alert.message}
        buttons={alert.buttons}
        radioOptions={alert.radioOptions}
        showInput={alert.showInput}
        inputPlaceholder={alert.inputPlaceholder}
        onInputChange={alert.onInputChange}
        onRadioSelect={(value) => {
          if (alert.type === 'radio' && alert.title.includes('Hủy đơn hàng')) {
            setCancelReason(value);
          } else {
            setReviewRating(value);
          }
        }}
        onDismiss={() => setAlert({ ...alert, visible: false })}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors['Grayiron/200'],
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    paddingBottom: metrics.spacing3,
  },
  statusContainer: {
    backgroundColor: colors.white,
    borderRadius: metrics.radius2,
    padding: metrics.spacing3,
    margin: metrics.spacing3,
    marginBottom: metrics.spacing2,
    shadowColor: colors['Grayiron/700'],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 2,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  statusIconWrapper: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: metrics.spacing2,
  },
  statusTextContainer: {
    flex: 1,
  },
  orderCode: {
    ...fonts.style.description,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    fontSize: 16,
  },
  statusText: {
    ...fonts.style.description,
    fontWeight: '600',
    marginVertical: 4,
    fontSize: 16,
  },
  orderDate: {
    ...fonts.style.description,
    color: colors['Grayiron/500'],
    fontSize: 14,
  },
  detailsContainer: {
    backgroundColor: colors.white,
    borderRadius: metrics.radius2,
    marginHorizontal: metrics.spacing3,
    marginBottom: metrics.spacing3,
    shadowColor: colors['Grayiron/700'],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 2,
  },
  section: {
    padding: metrics.spacing3,
    borderBottomWidth: 1,
    borderBottomColor: colors['Grayiron/200'],
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: metrics.spacing2,
  },
  sectionTitle: {
    ...fonts.style.h3,
    fontWeight: '600',
    color: colors['Grayiron/700'],
    marginLeft: 10,
    fontSize: 18,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: metrics.spacing2,
  },
  infoText: {
    ...fonts.style.description,
    color: colors['Grayiron/600'],
    marginLeft: metrics.spacing2,
    flex: 1,
    fontSize: 15,
  },
  merchantRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 6,
  },
  merchantLogo: {
    width: 56,
    height: 56,
    borderRadius: 10,
    marginRight: metrics.spacing2,
    backgroundColor: colors['Grayiron/200'],
  },
  merchantInfo: {
    flex: 1,
    marginRight: metrics.spacing2,
  },
  merchantName: {
    ...fonts.style.description,
    fontWeight: '600',
    color: colors['Grayiron/700'],
    marginBottom: 4,
    fontSize: 16,
  },
  merchantAddress: {
    ...fonts.style.description,
    color: colors['Grayiron/500'],
    fontSize: 14,
    lineHeight: 20,
  },
  productItem: {
    flexDirection: 'row',
    paddingVertical: metrics.spacing2,
    alignItems: 'center',
  },
  productImage: {
    width: 72,
    height: 72,
    borderRadius: 10,
    marginRight: metrics.spacing2,
    backgroundColor: colors['Grayiron/200'],
  },
  productDetails: {
    flex: 1,
    marginRight: metrics.spacing2,
  },
  productTitle: {
    ...fonts.style.description,
    color: colors['Grayiron/700'],
    marginBottom: 2,
    fontWeight: '500',
    fontSize: 14,
  },
  productVariant: {
    ...fonts.style.description,
    color: colors['Grayiron/500'],
    marginBottom: 6,
    fontSize: 12,
  },
  productPrice: {
    ...fonts.style.description,
    fontWeight: '600',
    color: colors['Primary/400'],
    fontSize: 15,
  },
  reviewButton: {
    marginTop: 8,
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: colors['Primary/50'],
    borderRadius: 6,
    borderWidth: 1,
    borderColor: colors['Primary/200'],
    alignSelf: 'flex-start',
  },
  reviewButtonText: {
    ...fonts.style.description,
    color: colors['Primary/500'],
    fontWeight: '600',
    fontSize: 14,
  },
  ratedText: {
    ...fonts.style.description,
    color: colors['Moss/500'],
    fontWeight: '600',
    marginTop: 8,
    fontSize: 14,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: metrics.spacing2,
    paddingHorizontal: 6,
  },
  paymentLabel: {
    ...fonts.style.normal,
    color: colors['Grayiron/500'],
    fontSize: 15,
  },
  paymentValue: {
    ...fonts.style.normal,
    color: colors['Grayiron/600'],
    fontSize: 15,
    fontWeight: '500',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: metrics.spacing2,
    paddingTop: metrics.spacing2,
    borderTopWidth: 1,
    borderTopColor: colors['Grayiron/200'],
    paddingHorizontal: 6,
  },
  totalLabel: {
    ...fonts.style.h3,
    fontWeight: '600',
    color: colors['Grayiron/700'],
    fontSize: 16,
  },
  totalValue: {
    ...fonts.style.h3,
    fontWeight: '600',
    color: colors['Primary/400'],
    fontSize: 16,
  },
  actionContainer: {
    padding: metrics.spacing3,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors['Grayiron/200'],
    shadowColor: colors['Grayiron/700'],
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  actionButton: {
    borderRadius: 10,
    padding: 14,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderWidth: 1,
    marginBottom: metrics.spacing2,
  },
  actionText: {
    ...fonts.style.normal,
    fontWeight: '600',
    fontSize: 16,
  },
});

export default OrderDetailScreen;