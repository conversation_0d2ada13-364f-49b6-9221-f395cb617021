import AsyncStorage from '@react-native-async-storage/async-storage';
import { safeParseJson } from '.';
import { filter, find, uniqBy } from 'lodash';
import { CART, RECENT_PRODUCTS } from 'constants/storage';

export const addToCart = async (item: any) => {
  const asyncCart: any = await AsyncStorage.getItem(CART);

  const cart = safeParseJson(asyncCart) || [];
  const existItem = find(cart, { id: item?.id });

  if (existItem) {
    await AsyncStorage.setItem(
      CART,
      JSON.stringify(
        uniqBy(
          [
            { ...item, quantity: existItem?.quantity + item?.quantity },
            ...cart,
          ],
          'id',
        ),
      ),
    );
    return;
  }

  await AsyncStorage.setItem(
    CART,
    JSON.stringify(
      uniqBy([...cart, { ...item, quantity: item?.quantity }], 'id'),
    ),
  );
};

export const removeItemCart = async (item: any) => {
  const asyncCart = await AsyncStorage.getItem(CART);
  const cart = safeParseJson(asyncCart || '');
  const newCart = filter(cart, product => product?.id !== item?.id);
  await AsyncStorage.setItem(CART, JSON.stringify(newCart));
};

export const increaseQty = async (item: any) => {
  const asyncCart = await AsyncStorage.getItem(CART);
  const cart = safeParseJson(asyncCart || '');
  const existItem = find(safeParseJson(cart), { id: item?.id });
  await AsyncStorage.setItem(
    CART,
    JSON.stringify(
      uniqBy([...cart, { ...item, quantity: existItem?.quantity + 1 }], 'id'),
    ),
  );
};

export const decreaseQty = async (item: any) => {
  const asyncCart = await AsyncStorage.getItem(CART);
  const cart = safeParseJson(asyncCart || '');
  const existItem = find(safeParseJson(cart), { id: item?.id });
  await AsyncStorage.setItem(
    CART,
    JSON.stringify(
      uniqBy([...cart, { ...item, quantity: existItem?.quantity - 1 }], 'id'),
    ),
  );
};

export const setQty = async (item: any) => {
  const asyncCart = await AsyncStorage.getItem(CART);
  const cart = safeParseJson(asyncCart || '');
  await AsyncStorage.setItem(
    CART,
    JSON.stringify(
      uniqBy([...cart, { ...item, quantity: item?.quantity }], 'id'),
    ),
  );
};

export const addRecentProducts = async (item: any) => {
  const asyncRecentProduct = await AsyncStorage.getItem(RECENT_PRODUCTS);

  await AsyncStorage.setItem(
    RECENT_PRODUCTS,
    JSON.stringify(
      uniqBy([item, ...safeParseJson(asyncRecentProduct || '')], 'id'),
    ),
  );
};

export const getRecentProducts = async () => {
  const asyncRecentProduct = await AsyncStorage.getItem(RECENT_PRODUCTS);

  return safeParseJson(asyncRecentProduct || '') || [];
};

export const getCarts = async () => {
  const asyncCart = await AsyncStorage.getItem(CART);

  return safeParseJson(asyncCart || '') || [];
};

export const setStorageCarts = async (values: any) => {
  await AsyncStorage.setItem(CART, JSON.stringify(values));
};
