import React, { ReactElement } from 'react';
import { StyleSheet, Text, TouchableOpacity, ViewStyle } from 'react-native';

import colors from 'themes/colors';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';

type Props = {
  size?: 'tiny' | 'small' | 'large' | 'default';
  title: string;
  imgIcon?: ReactElement;
  onPress?: any;
  customStyles?: ViewStyle;
  type?: 'success' | 'warning' | 'default';
};

const getContainerSizeStyle = (size: string | undefined) => {
  if (!size) {
    return {
      container: styles.defaultContainer,
      text: styles.defaultText,
    };
  }
  switch (size) {
    case 'tiny':
      return {
        container: styles.tinyContainer,
        text: styles.tinyText,
      };
    case 'small':
      return {
        container: styles.smallContainer,
        text: styles.smallText,
      };
    case 'default':
      return {
        container: styles.defaultContainer,
        text: styles.defaultText,
      };
    case 'large':
      return {
        container: styles.largeContainer,
        text: styles.largeText,
      };
    default:
      return {
        container: styles.defaultContainer,
        text: styles.defaultText,
      };
  }
};

const getTypeStyle = (type: string | undefined) => {
  if (!type) {
    return {
      container: styles.containerDefaultType,
      text: styles.textDefaultType,
    };
  }
  switch (type) {
    case 'success':
      return {
        container: styles.containerSuccessType,
        text: styles.textSuccessType,
      };
    case 'warning':
      return {
        container: styles.containerWarningType,
        text: styles.textWarningType,
      };
    default:
      return {
        container: styles.containerDefaultType,
        text: styles.textDefaultType,
      };
  }
};
function Tag({ size, type, title, onPress, customStyles }: Props) {
  const containerSizeStyle = getContainerSizeStyle(size)?.container;
  const textSizeStyle = getContainerSizeStyle(size)?.text;
  const textTypeStyle = getTypeStyle(type)?.text;
  const containerTypeStyle = getTypeStyle(type)?.container;

  return (
    <TouchableOpacity
      onPress={onPress}
      activeOpacity={!onPress ? 1 : 0.5}
      style={[
        containerSizeStyle as ViewStyle,
        containerTypeStyle as ViewStyle,
        customStyles,
      ]}>
      <Text style={[textSizeStyle, textTypeStyle]}>{title}</Text>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  tinyContainer: {
    paddingHorizontal: metrics.spacing1,
    paddingVertical: metrics.spacing1 / 4,
    borderRadius: metrics.radius4,
  },
  smallContainer: {
    paddingHorizontal: metrics.spacing2,
    paddingVertical: metrics.spacing1 / 2,
    borderRadius: metrics.radius4,
  },
  defaultContainer: {
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing1,
    borderRadius: metrics.radius4,
  },
  largeContainer: {
    paddingHorizontal: metrics.spacing6,
    paddingVertical: metrics.spacing2,
    borderRadius: metrics.radius4,
  },
  tinyText: {
    fontSize: fonts.size.tiny,
  },
  smallText: {
    fontSize: fonts.size.small,
  },
  defaultText: {
    fontSize: fonts.size.medium,
  },
  largeText: {
    fontSize: fonts.size.regular,
  },
  textSuccessType: {
    color: colors['Moss/700'],
  },
  textWarningType: {
    color: colors['Warning/500'],
  },
  textDefaultType: {
    color: colors['Grayiron/500'],
  },
  containerSuccessType: {
    backgroundColor: colors['Moss/100'],
    borderColor: colors['Moss/400'],
    borderWidth: 1,
  },
  containerWarningType: {
    backgroundColor: colors['Warning/100'],
    borderColor: colors['Warning/400'],
    borderWidth: 1,
  },
  containerDefaultType: {
    backgroundColor: 'white',
    borderColor: colors['Grayiron/300'],
    borderWidth: 1,
  },
});

export default Tag;
