import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import CommonButton from 'components/CommonButton';
import { AUTHENTICATION_STEP } from 'constants/authentication';
import FormInput from 'components/FormInput';
import { activate, createPassword, resendActivation } from 'api/users';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import { map } from 'lodash';
import Toast from 'react-native-toast-message';

const schema = yup.object({
  password: yup
    .string()
    .trim()
    .min(8, 'Mật khẩu ít nhất 8 ký tự')
    .required('Vui lòng nhập mật khẩu'),
  confirmPassword: yup
    .string()
    .trim()
    .min(8, '<PERSON><PERSON>t khẩu ít nhất 8 ký tự')
    .required('<PERSON>ui lòng nhập mật khẩu'),
});

const RegisterCreatePassForm = ({
  setStep,
  setCurrentData,
  currentData,
}: any) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm({
    defaultValues: {
      password: '',
      confirmPassword: '',
    },
    resolver: yupResolver(schema),
  });

  const onSubmit = async (values: any) => {
    try {
      if (values?.confirmPassword !== values?.password) {
        setError('confirmPassword', { message: 'Mật khẩu không khớp' });
        return;
      }
      const { phoneOrEmail, key } = currentData;
      const response = await createPassword({
        body: {
          phoneOrEmail,
          key,
          email: phoneOrEmail,
          password: values.password,
        },
      });
      console.log(response);

      if (response?.code) {
        if (response?.validationErrors) {
          map(response?.validationErrors, (item): any => {
            setError(item?.field, {
              message: item.message,
            });
          });
        } else {
          Toast.show({
            type: 'error',
            text1: 'Thông báo',
            text2: response?.message || 'Có lỗi xảy ra, vui lòng thử lại',
            position: 'top',
          });
        }
      } else {
        setCurrentData({});
        setStep(AUTHENTICATION_STEP.LOGIN);
        Toast.show({
          type: 'success',
          text1: 'Đăng ký thành công',
          position: 'top',
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View>
          <Text style={styles.subTitle}>Tạo mật khẩu mới</Text>
        </View>
        <View style={styles.item}>
          <FormInput
            label={'Mật khẩu mới'}
            control={control}
            required
            placeholder="Nhập mật khẩu mới"
            name="password"
            errors={errors?.password}
          />
        </View>
        <View style={styles.item}>
          <FormInput
            label={'Nhập lại mật khẩu'}
            control={control}
            required
            placeholder="Nhập lại mật khẩu mới"
            name="confirmPassword"
            errors={errors?.confirmPassword}
          />
        </View>
        <View style={commonStyles.rowCenter}>
          <CommonButton
            onPress={handleSubmit(onSubmit)}
            customStyles={{ backgroundColor: colors['Moss/500'] }}
          >
            Xác nhận
          </CommonButton>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  content: {
    padding: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
    marginBottom: metrics.spacing4,
  },
  item: {
    marginBottom: metrics.spacing3, 
  },
  subTitle: {
    fontSize: fonts.size.h2,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  text: {
    fontSize: fonts.size.input,
    fontWeight: '400',
    color: colors['Grayiron/400'],
    marginBottom: metrics.spacing4,
  },
  registerText: {
    fontSize: fonts.size.input,
    fontWeight: '500',
    color: colors['Moss/600'],
    marginBottom: metrics.spacing4,
  },
  center: {
    alignItems: 'center',
    marginTop: metrics.spacing2,
  },
});

export default RegisterCreatePassForm;