import { apis } from 'utils/axios';
import { Merchant } from 'types/order'; 
import { Product } from 'types/product'

interface ProductsResponse {
  products: Product[];
  totalElements: number;
  totalPages: number;
  currentPage: number;
}

export const getMerchantById = async (id: string): Promise<Merchant> => {
  try {
    const response = await apis().get(`/merchant/${id}`);
    console.log('Merchant details response:', response.data);
    return response?.data;
  } catch (e) {
    console.error('Lỗi khi lấy thông tin merchant:', e);
    throw e; // Propagate error to be handled by caller
  }
};

export const getMerchantProducts = async (
  id: string,
  page: number = 0,
  size: number = 10
): Promise<ProductsResponse> => {
  try {
    const response = await apis().get(`/merchant/${id}/products`, {
      params: { page, size },
    });
    console.log('Merchant products response:', response.data);
    return response?.data;
  } catch (e) {
    console.error('Lỗi khi lấy danh sách sản phẩm của merchant:', e);
    throw e; 
  }
};