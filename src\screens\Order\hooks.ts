import { useState, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';
import { getListOrder } from 'api/order';
import { Order } from 'types/order';

interface UseOrdersReturn {
  orders: Order[];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

const useOrders = (status: string): UseOrdersReturn => {
  const [state, setState] = useState<{
    orders: Order[];
    loading: boolean;
    error: string | null;
  }>({
    orders: [],
    loading: true,
    error: null,
  });

  const fetchOrders = useCallback(
    async (signal?: AbortSignal) => {
      try {
        setState((prev) => ({ ...prev, loading: true, error: null }));
        const response = await getListOrder(0, 10, status);
        const orders = Array.isArray(response?.orders) ? response.orders : [];
        setState({
          orders,
          loading: false,
          error: null,
        });
      } catch (err) {
        let errorMessage = 'Lỗi khi lấy danh sách đơn hàng';
        if (err instanceof Error) {
          errorMessage = err.message;
        }
        setState({
          orders: [],
          loading: false,
          error: errorMessage,
        });
      }
    },
    [status]
  );

  useEffect(() => {
    const controller = new AbortController();
    fetchOrders(controller.signal);
    return () => {
      controller.abort();
    };
  }, [fetchOrders]);

  const refresh = useCallback(
    debounce(async () => {
      await fetchOrders();
    }, 300),
    [fetchOrders]
  );

  return {
    orders: state.orders,
    loading: state.loading,
    error: state.error,
    refresh,
  };
};

export default useOrders;