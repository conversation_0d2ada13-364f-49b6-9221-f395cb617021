import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import MenuButton from 'components/MenuButton';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import { ORDER_STATUS } from 'themes/constant';

const itemWidth = (metrics.fullWidth - metrics.spacing3 * 2 - metrics.spacing2 * 3) / 4;

const MyOrders = () => {
  const navigation = useNavigation();

  const navigateToOrderTab = (tabKey: string) => {
    navigation.navigate('Order', { activeTab: tabKey });
  };

  return (
    <View style={styles.container}>
      <View style={commonStyles.rowSpaceBetween}>
        <Text style={styles.title}>Đơn hàng của Tôi</Text>
        <TouchableOpacity onPress={() => navigateToOrderTab(ORDER_STATUS.PENDING.key)}>
          <Text style={styles.subTitle}>
            Xem tất cả
            <MaterialCommunityIcons
              color={colors['Grayiron/600']}
              name={'chevron-right'}
              size={14}
            />
          </Text>
        </TouchableOpacity>
      </View>
      <View style={styles.row}>
        <MenuButton
          customStyles={{ width: itemWidth }}
          imgIcon={
            <MaterialCommunityIcons
              name="storefront-outline"
              size={30}
              color={colors['Moss/400']}
            />
          }
          title={ORDER_STATUS.PENDING.name}
          onPress={() => navigateToOrderTab(ORDER_STATUS.PENDING.key)}
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          imgIcon={
            <MaterialCommunityIcons
              name="truck-delivery-outline"
              size={30}
              color={colors['Moss/400']}
            />
          }
          title={ORDER_STATUS.IN_TRANSIT.name}
          onPress={() => navigateToOrderTab(ORDER_STATUS.IN_TRANSIT.key)}
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          imgIcon={
            <MaterialCommunityIcons
              name="gift-outline"
              size={30}
              color={colors['Moss/400']}
            />
          }
          title={ORDER_STATUS.DELIVERED.name}
          onPress={() => navigateToOrderTab(ORDER_STATUS.DELIVERED.key)}
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          imgIcon={
            <MaterialCommunityIcons
              name="close-circle-outline"
              size={30}
              color={colors['Moss/400']}
            />
          }
          title={ORDER_STATUS.CANCELLED.name}
          onPress={() => navigateToOrderTab(ORDER_STATUS.CANCELLED.key)}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingBottom: metrics.spacing2,
  },
  row: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexDirection: 'row',
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
    paddingVertical: metrics.spacing1,
  },
  title: {
    fontSize: fonts.size.medium, 
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing3,
  },
  subTitle: {
    fontSize: fonts.size.small, 
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing2, 
  },
});

export default MyOrders;
