import React from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import colors from 'themes/colors';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import Product from 'components/Product';
import { addRecentProducts, addToCart } from 'utils/cart';
import { useNavigation } from '@react-navigation/native';
import { useLoadMore } from './hooks';

type SearchResultsProps = {
  products: any[];
  loading: boolean;
  keyword: string;
};

const SearchResults: React.FC<SearchResultsProps> = ({ products, loading, keyword }) => {
  const navigation = useNavigation();
  
  const { displayedItems, hasMore, loadMore } = useLoadMore({
    initialItems: products || [],
    itemsPerPage: 10,
  });
  
  const onViewDetail = (product: any) => {
    if (product?.id) {
      addRecentProducts(product);
      navigation.navigate('ProductDetail', { id: product.id });
    }
  };
  
  if (!keyword) return null;
  
  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Đang tìm kiếm...</Text>
      </View>
    );
  }

  if (!products || products.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.resultTitle}>Kết quả tìm kiếm cho "{keyword}"</Text>
        <Text style={styles.emptyMessage}>Không tìm thấy sản phẩm nào phù hợp</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.resultTitle}>
        Kết quả tìm kiếm cho "{keyword}": {products.length} sản phẩm
      </Text>
      <View style={styles.row}>
        {Array.isArray(displayedItems) && displayedItems.map((product, index) => (
          <View 
            key={product?.id || `product-${index}`} 
            style={styles.productContainer}
          >
            <Product
              customStyles={styles.productItem}
              title={product?.title}
              uri={product?.mainImageUrl}
              price={product?.price}
              anchoPrice={product?.anchoPrice}
              rating={product?.rating}
              discountPercent={product?.discountPercent}
              onPress={() => onViewDetail(product)}
              onAddToCart={() => addToCart({ ...product, quantity: 1 })}
            />
          </View>
        ))}
      </View>
      {hasMore && (
        <TouchableOpacity style={styles.loadMoreButton} onPress={loadMore}>
          <Text style={styles.loadMoreText}>Tải thêm</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    marginTop: metrics.spacing2,
    marginHorizontal: metrics.spacing3,
    padding: metrics.spacing2,
    backgroundColor: colors.white,
    borderRadius: metrics.radius2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: metrics.spacing2,
  },
  loadingText: {
    fontSize: fonts.size.medium,
    color: colors['Grayiron/500'],
    textAlign: 'center',
    padding: metrics.spacing3,
  },
  emptyMessage: {
    fontSize: fonts.size.medium,
    color: colors['Grayiron/400'],
    textAlign: 'center',
    marginVertical: metrics.spacing3,
  },
  resultTitle: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing2,
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginHorizontal: -metrics.spacing1,
  },
  productContainer: {
    width: '50%',
    paddingHorizontal: metrics.spacing1,
    marginBottom: metrics.spacing2,
  },
  productItem: {
    width: '100%',
    borderRadius: metrics.radius1,
    overflow: 'hidden',
  },
  loadMoreButton: {
    backgroundColor: colors['Primary/500'],
    paddingVertical: metrics.spacing2,
    borderRadius: metrics.radius2,
    alignItems: 'center',
    marginTop: metrics.spacing2,
  },
  loadMoreText: {
    color: colors.white,
    fontSize: fonts.size.medium,
    fontWeight: '600',
  },
});

export default SearchResults;