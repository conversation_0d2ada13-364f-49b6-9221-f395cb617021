import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import metrics from 'themes/metrics';
import commonStyles from 'themes/commonStyles';
import { CheckBox } from '@ui-kitten/components';
import FastImage from 'react-native-fast-image';

type Props = {
  checked?: boolean;
  uri?: string;
  title?: string;
  onCheck?: (a: any) => void;
};

function MerchantItem({ checked, uri, title, onCheck }: Props) {
  return (
    <View style={styles.item}>
      <CheckBox status="basic" checked={checked} onChange={onCheck} />
      <View style={styles.main}>
        <FastImage
          // fallback={require('assets/images/product-fallback-image.png')}
          style={styles.merchantLogo}
          source={
            uri ? { uri } : require('assets/images/product-fallback-image.png')
          }
        />
        <Text style={commonStyles.mainText}>{title}</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  item: {
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',
    flexWrap: 'nowrap',
  },
  main: {
    flexDirection: 'row',
    marginLeft: metrics.spacing6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  merchantLogo: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: metrics.spacing2,
  },
});

export default MerchantItem;
