import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import metrics from 'themes/metrics';
import commonStyles from 'themes/commonStyles';
import colors from 'themes/colors';

type Props = {
  leftIconName: string;
  title: string;
  onPress: () => void;
};

function RowItem({ leftIconName, title, onPress }: Props) {
  return (
    <TouchableOpacity onPress={onPress} style={styles.item}>
      <View style={commonStyles.rowCenter}>
        <Icon
          style={styles.icon}
          color={colors['Grayiron/600']}
          name={leftIconName}
          size={25}
        />
        <Text style={commonStyles.mainText}>{title}</Text>
      </View>
      <Icon
        style={styles.icon}
        color={colors['Grayiron/600']}
        name="chevron-right"
        size={20}
      />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  item: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    marginVertical: metrics.spacing2
  },
  icon: {
    marginRight: metrics.spacing3,
  },
});

export default RowItem;
