import React from 'react';
import { TouchableOpacity, View, ViewStyle } from 'react-native';

import styles from 'components/CardPost/styles';
import FastImage from 'react-native-fast-image';
import { Avatar, Text } from '@ui-kitten/components';
import Tag from 'components/Tag';
import commonStyles from 'themes/commonStyles';
import dayjs from 'dayjs';

type Props = {
  border?: boolean;
  uri?: string;
  avatarUri?: string;
  title: string;
  author?: string;
  onPress?: any;
  customStyles?: ViewStyle;
  createdAt?: string;
};

function CardPost({
  uri,
  avatarUri,
  title,
  onPress,
  customStyles,
  border,
  author,
  createdAt,
}: Props) {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.container, customStyles, border && styles.border]}>
      <FastImage style={styles.postImage} source={{ uri }} />
      <View style={styles.content}>
        <View style={commonStyles.rowSpaceBetween}>
          <Tag size="small" type="warning" title="Dinh dưỡng" />
          <Text style={styles.hintTitle}>{dayjs(createdAt)?.fromNow?.()}</Text>
        </View>
        <Text style={styles.title} status="primary" numberOfLines={2}>
          {title}
        </Text>
        <View style={commonStyles.rowCenter}>
          <Avatar shape="round" size="small" source={{ uri: avatarUri }} />
          <Text style={styles.hintTitle}>{author}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
}

export default CardPost;
