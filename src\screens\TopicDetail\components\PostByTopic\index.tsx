import React from 'react';
import { View, StyleSheet } from 'react-native';

import metrics from 'themes/metrics';
import PostCard from 'components/PostCard';
import { Text } from '@ui-kitten/components';
import fonts from 'themes/fonts';
import colors from 'themes/colors';

// import post from 'utils/post2.json';
import { useNavigation } from '@react-navigation/native';
import { usePostsByTopicId } from './hooks';
import { map } from 'lodash';

const PostByTopic = ({ title, id, size }: any) => {
  const navigation = useNavigation();
  const { posts } = usePostsByTopicId({ id, page: 0, size });

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>{title}</Text>
        {map(posts, (post: any, index) =>
          index === 0 ? (
            <PostCard
              key={post.id}
              customStyles={styles.postFull}
              type="full"
              title={post?.title}
              author={post?.author}
              uri={post?.thumbnail}
              avatarUri={post?.thumbnail}
              topicName={post?.topic?.name}
              createdAt={post?.createdAt}
              onPress={() =>
                navigation.navigate('PostDetail', { id: post?.id })
              }
            />
          ) : (
            <PostCard
              key={post.id}
              title={post.title}
              author={post.author}
              uri={post.thumbnail}
              avatarUri={post.thumbnail}
              topicName={post?.topic?.name}
              createdAt={post?.createdAt}
              onPress={() =>
                navigation.navigate('PostDetail', { id: post?.id })
              }
            />
          ),
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // marginTop: metrics.spacing4,
  },
  content: {
    paddingHorizontal: metrics.spacing1,
    paddingVertical: metrics.spacing4,
    backgroundColor: 'white',
  },
  row: {
    justifyContent: 'space-around',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexDirection: 'row',
    gap: metrics.spacing2,
    marginBottom: metrics.spacing3,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
    paddingHorizontal: metrics.spacing3,
  },
  tag: {
    marginRight: metrics.spacing2,
  },
  tags: {
    marginBottom: metrics.spacing4,
  },
  postFull: {
    paddingHorizontal: metrics.spacing3,
    marginBottom: metrics.spacing2,
  },
});

export default PostByTopic;
