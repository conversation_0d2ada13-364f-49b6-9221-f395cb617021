import React, { ReactNode } from 'react';
import { Text, TouchableWithoutFeedback } from 'react-native';
import {
  Datepicker,
  Input,
  Select,
  SelectItem,
  Toggle,
} from '@ui-kitten/components';
import { Controller, FieldError } from 'react-hook-form';
import Ionicons from 'react-native-vector-icons/Ionicons';
import commonStyles from 'themes/commonStyles';
import { get, map } from 'lodash';
import { EvaSize } from '@ui-kitten/components/devsupport';

type Props = {
  errors?: { message: string } | undefined | FieldError;
  control: any;
  label?: string;
  placeholder?: string;
  name: string;
  required?: boolean;
  disabled?: boolean;
  password?: boolean;
  inputType?: 'input' | 'datepicker' | 'select' | 'toggle';
  options?: any[];
  accessoryRight?: Iterable<ReactNode> | any;
  size?: EvaSize;
  dateValue?: Date; // Thêm prop để hỗ trợ giá trị khởi tạo
  max?: Date; // Thêm prop để giới hạn ngày tối đa
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad' | 'number-pad';
  secureTextEntry?: boolean;
  rightIcon?: ReactNode;
};

const Icon = ({ toggleSecureEntry, secureTextEntry }: any) => (
  <TouchableWithoutFeedback onPress={toggleSecureEntry}>
    <Ionicons
      name={secureTextEntry ? 'eye-outline' : 'eye-off-outline'}
      size={24}
    />
  </TouchableWithoutFeedback>
);

const FormInput = ({
  errors,
  control,
  label,
  required,
  placeholder,
  name,
  password,
  inputType,
  options,
  accessoryRight,
  disabled,
  size = 'default',
  dateValue,
  max,
  keyboardType,
  secureTextEntry: secureTextEntryProp,
  rightIcon,
}: Props) => {
  const [secureTextEntry, setSecureTextEntry] = React.useState(password);

  const toggleSecureEntry = (): void => {
    setSecureTextEntry(!secureTextEntry);
  };

  // Use secureTextEntryProp if provided, otherwise use internal state
  const isSecureTextEntry = secureTextEntryProp !== undefined ? secureTextEntryProp : secureTextEntry;

  const renderForm = ({ field: { onChange, onBlur, value } }: any) => {
    switch (inputType) {
      case 'input':
        return (
          <Input
            disabled={disabled}
            placeholder={placeholder}
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            secureTextEntry={isSecureTextEntry}
            keyboardType={keyboardType}
            accessoryRight={
              rightIcon ? rightIcon :
              password ? (
                <Icon {...{ toggleSecureEntry, secureTextEntry }} />
              ) : (
                accessoryRight
              )
            }
          />
        );
      case 'toggle':
        return (
          <Toggle
            status="primary"
            onBlur={onBlur}
            disabled={disabled}
            checked={!!value}
            onChange={onChange}
          />
        );
      case 'select':
        return (
          <Select
            disabled={disabled}
            size={size}
            style={commonStyles.datePicker}
            placeholder={placeholder}
            onBlur={onBlur}
            onSelect={(index: any) => {
              const selectedIndex = index.row;
              onChange(options?.[selectedIndex]?.value);
            }}
            value={get(options, `${options?.findIndex((opt: any) => opt.value === value)}.label`, '')}
            accessoryRight={accessoryRight}
          >
            {map(options, (item) => (
              <SelectItem key={item?.value} title={item?.label} />
            ))}
          </Select>
        );
      case 'datepicker':
        return (
          <Datepicker
            disabled={disabled}
            size={size}
            placeholder={placeholder}
            onBlur={onBlur}
            onSelect={(nextDate: Date) => {
              // Chuyển Date thành chuỗi YYYY-MM-DD
              const formattedDate = nextDate.toISOString().split('T')[0];
              console.log(`Datepicker chọn: ${formattedDate}`);
              onChange(formattedDate);
            }}
            date={
              value
                ? new Date(value)
                : dateValue && !isNaN(dateValue.getTime())
                ? dateValue
                : undefined
            }
            max={max}
            controlStyle={commonStyles.datePicker}
          />
        );
      default:
        return (
          <Input
            disabled={disabled}
            size={size}
            style={commonStyles.datePicker}
            placeholder={placeholder}
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            secureTextEntry={isSecureTextEntry}
            keyboardType={keyboardType}
            accessoryRight={
              rightIcon ? rightIcon :
              password ? (
                <Icon {...{ toggleSecureEntry, secureTextEntry }} />
              ) : (
                accessoryRight
              )
            }
          />
        );
    }
  };

  return (
    <>
      {label && (
        <Text style={commonStyles.label}>
          {label}
          {required && <Text style={{ color: 'red' }}> *</Text>}
        </Text>
      )}
      <Controller
        control={control}
        rules={{
          required: required ? 'Trường này là bắt buộc' : false,
        }}
        render={renderForm}
        name={name}
      />
      {errors && <Text style={commonStyles.errorText}>{errors?.message}</Text>}
    </>
  );
};

export default FormInput;