//example api request: replace with your API request here in folder API

import { apis } from 'utils/axios';

export const getHomePagePosts = async ({ since }: { since?: string }) => {
  try {
    const response = await apis().get('posts/homepage', {
      method: 'GET',
      params: {
        ...(since ? { since } : {}),
      },
    });
    return response?.data;
  } catch (e) {
    return e;
  }
};

export const getPostDetail = async ({ id }: { id: string | number }) => {
  try {
    const response = await apis().get(`posts/homepage/${id}`, {
      method: 'GET',
    });
    return response?.data;
  } catch (e) {
    return e;
  }
};

export const getPostsByTopicId = async ({
  id,
  page = 0,
  size = 10,
}: {
  id: string | number;
  page?: string | number;
  size?: string | number;
}) => {
  try {
    const response = await apis().get(`/posts/homepage/topic/${id}`, {
      method: 'GET',
      params: {
        page,
        size,
      },
    });
    return response?.data;
  } catch (e) {
    return e;
  }
};
