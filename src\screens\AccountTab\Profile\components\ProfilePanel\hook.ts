import { useState, useCallback } from 'react';
import { launchImageLibrary } from 'react-native-image-picker';
import { uploadService } from 'api/upload';
import { getUser, updateUser } from 'api/users';

interface UpdateUserRequest {
  firstname: string;
  lastname: string;
  avatar?: string | null;
  gender: 'MALE' | 'FEMALE';
  birthday: string;
  ward: string;
  wardId: number;
  district: string;
  districtId: number;
  city: string;
  provinceId: number;
  address: string;
}

type ProfileUpdateHook = {
  avatarUri: string | null;
  uploading: boolean;
  handleChoosePhoto: () => void;
  setAvatarUri: (uri: string | null) => void;
  error: string | null;
  uploadAvatar: () => Promise<string | null>;
  getUser: () => Promise<any>;
  updateUser: (data: UpdateUserRequest) => Promise<any>;
};

const useProfileUpdate = (
  initialAvatar: string | null,
  onUploadSuccess?: (uri: string) => void
): ProfileUpdateHook => {
  const [avatarUri, setAvatarUri] = useState<string | null>(initialAvatar);
  const [uploading, setUploading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const uploadAvatar = useCallback(async (): Promise<string | null> => {
    if (!avatarUri || avatarUri === initialAvatar || avatarUri.startsWith('http')) {
      return avatarUri;
    }

    if (!avatarUri.startsWith('file://') && !avatarUri.startsWith('content://')) {
      setError('Invalid image path.');
      return null;
    }

    const formData = new FormData();
    formData.append('file', {
      uri: avatarUri,
      type: 'image/jpeg',
      name: 'avatar.jpg',
    } as any);

    try {
      setUploading(true);
      setError(null);
      const uploadedUrl = await uploadService(formData);
      if (!uploadedUrl || typeof uploadedUrl !== 'string') {
        throw new Error('Invalid URL received from upload service.');
      }
      setAvatarUri(uploadedUrl);
      if (onUploadSuccess) {
        onUploadSuccess(uploadedUrl);
      }
      return uploadedUrl;
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to upload image. Please try again.';
      setError(errorMessage);
      return null;
    } finally {
      setUploading(false);
    }
  }, [avatarUri, initialAvatar, onUploadSuccess]);

  const handleChoosePhoto = useCallback(() => {
    const options = {
      mediaType: 'photo' as const,
      quality: 1,
      includeBase64: false,
    };
    launchImageLibrary(options, (response) => {
      if (response.didCancel) {
        console.log('User cancelled image selection');
      } else if (response.errorCode) {
        setError('Failed to select image. Please try again.');
      } else if (response.assets && response.assets.length > 0) {
        const asset = response.assets[0];
        if (asset.fileSize && asset.fileSize > 5 * 1024 * 1024) {
          setError('Image size exceeds 5MB.');
          return;
        }
        if (asset.type && !['image/jpeg', 'image/png'].includes(asset.type)) {
          setError('Only JPEG or PNG formats are supported.');
          return;
        }
        setError(null);
        setAvatarUri(asset.uri || null);
      }
    });
  }, []);

  const fetchUser = useCallback(async () => {
    try {
      setError(null);
      const userData = await getUser();
      if (!userData) {
        throw new Error('No user data received.');
      }
      setAvatarUri(userData.avatar || null);
      return userData;
    } catch (err: any) {
      const errorMessage = err?.message || 'Failed to fetch user data.';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const updateUserProfile = useCallback(async (data: UpdateUserRequest) => {
    try {
      setError(null);
      const cleanData: UpdateUserRequest = {
        firstname: data.firstname?.trim() || '',
        lastname: data.lastname?.trim() || '',
        avatar: data.avatar || null,
        gender: data.gender === 'MALE' ? 'MALE' : 'FEMALE',
        birthday: data.birthday || '',
        ward: data.ward?.trim() || '',
        wardId: Number(data.wardId) || 0,
        district: data.district?.trim() || '',
        districtId: Number(data.districtId) || 0,
        city: data.city?.trim() || '',
        provinceId: Number(data.provinceId) || 0,
        address: data.address?.trim() || '',
      };

      const today = new Date();
      const birthdayDate = new Date(cleanData.birthday);
      if (birthdayDate > today) {
        throw new Error('Birthday cannot be in the future.');
      }

      const response = await updateUser(cleanData);
      if (!response) {
        throw new Error('Update failed: No response from API.');
      }

      if (response.avatar) {
        setAvatarUri(response.avatar);
      }

      return response.data || response.user || (await getUser());
    } catch (err: any) {
      const errorMessage =
        err?.response?.data?.message ||
        err?.response?.data?.errors?.join(', ') ||
        err?.message ||
        'Failed to update user information.';
      setError(errorMessage);
      throw err;
    }
  }, []);

  return {
    avatarUri,
    uploading,
    handleChoosePhoto,
    setAvatarUri,
    error,
    uploadAvatar,
    getUser: fetchUser,
    updateUser: updateUserProfile,
  };
};

export default useProfileUpdate;