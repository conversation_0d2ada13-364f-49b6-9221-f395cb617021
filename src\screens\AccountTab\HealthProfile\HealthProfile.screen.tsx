/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect } from 'react';
import { SafeAreaView, StyleSheet } from 'react-native';

import HomeHeader from 'components/Header/HomeHeader';
import HealthProfilePanel from './components/HealthProfilePanel';
import { useUserStore } from 'stores/user';

const HealthProfile = () => {
  const fetchUserFitness = useUserStore((state: any) => state.fetchUserFitness);
  
  useEffect(() => {
    fetchUserFitness();
  }, []);

  return (
    <>
      <HomeHeader
        rightComponent={null}
        title="<PERSON><PERSON> sơ sức khỏe"
        backButton
        noStretch
      />
      <SafeAreaView style={styles.safeAreaView}>
        <HealthProfilePanel />
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
    backgroundColor: 'transparent',
  },
});

export default HealthProfile;
