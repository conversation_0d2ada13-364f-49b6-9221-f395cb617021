export const REGEX_INPUT = Object.freeze({
  EMAIL: /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/,
  PHONE: /(0[35789])+(\d{8})\b/,
});

export const STORAGE = Object.freeze({
  ACCESS_TOKEN: 'ACCESS_TOKEN',
  REFRESH_TOKEN: 'REFRESH_TOKEN',
  EMAIL: 'EMAIL'
});

export const AUTHENTICATION_STEP = Object.freeze({
  LOGIN: 'LOGIN',
  REGISTER: 'REGISTER',
  REGISTER_OTP: 'REGISTER_OTP',
  REGISTER_CREATE_PASS: 'REGISTER_CREATE_PASS',
  FORGOT_PASS: 'FORGOT_PASS',
  FORGOT_PASS_CREATE_NEW_PASS: 'FORGOT_PASS_CREATE_NEW_PASS',
});
