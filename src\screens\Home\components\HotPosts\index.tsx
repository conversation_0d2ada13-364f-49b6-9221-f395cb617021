import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { find } from 'lodash';
import { Text } from '@ui-kitten/components';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import Tag from 'components/Tag';
import PostBlock from 'components/PostBlock';
import { useHomePosts } from './hooks';

const HotPost = () => {
  const { since, setSince, homePosts } = useHomePosts();

  // Get posts for each topic
  const postOfFirstTopic = find(homePosts?.data, { topic: 'van_dong' });
  const postOfSecondTopic = find(homePosts?.data, { topic: 'dinh_duong' });
  const postOfThirdTopic = find(homePosts?.data, { topic: 'benh_li' });
  const postOfFourthTopic = find(homePosts?.data, { topic: 'tinh_than' });

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Bài viết cho bạn</Text>
      <ScrollView
        horizontal
        style={styles.tags}
        bounces
        showsHorizontalScrollIndicator={false}
      >
        <Tag
          customStyles={styles.tag}
          size="large"
          type={since === '' ? 'success' : undefined}
          title="Bài viết nổi bật"
          onPress={() => setSince('')}
        />
        <Tag
          customStyles={styles.tag}
          size="large"
          type={since === 'newest' ? 'success' : undefined}
          title="Bài viết hot"
          onPress={() => setSince('newest')}
        />
      </ScrollView>
      {postOfFirstTopic?.posts?.length > 0 && (
        <PostBlock title="Vận động" posts={postOfFirstTopic.posts} />
      )}
      {postOfSecondTopic?.posts?.length > 0 && (
        <PostBlock title="Dinh dưỡng" posts={postOfSecondTopic.posts} />
      )}
      {postOfThirdTopic?.posts?.length > 0 && (
        <PostBlock title="Bệnh lí" posts={postOfThirdTopic.posts} />
      )}
      {postOfFourthTopic?.posts?.length > 0 && (
        <PostBlock title="Tinh thần" posts={postOfFourthTopic.posts} />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop:metrics.spacing4,
    paddingHorizontal: metrics.spacing4,
  },
  content: {
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
    marginVertical: metrics.spacing2,
  },
  row: {
    justifyContent: 'space-around',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexDirection: 'row',
    gap: metrics.spacing2,
    marginBottom: metrics.spacing3,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '700',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  tag: {
    marginRight: metrics.spacing2,
  },
  tags: {
    marginBottom: metrics.spacing2,
  },
});

export default HotPost;