import 'react-native-gesture-handler';
import React, { useEffect } from 'react';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import MainNavigation from './navigation/navigation';
import ToastWrapper from './components/ToastWrapper';
import { CartProvider } from './hooks/contexts/CartContext';

const App = () => {
  return (
    <SafeAreaProvider>
      <CartProvider>
        <MainNavigation />
        <ToastWrapper />
      </CartProvider>
    </SafeAreaProvider>
  );
};

export default App;
