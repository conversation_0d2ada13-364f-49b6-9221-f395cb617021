import React from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Image,
} from 'react-native';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import { Order } from 'types/order';
import { ORDER_STATUS } from 'themes/constant';

interface OrderItemsProps {
  orders: Order[];
  loading: boolean;
  onItemPress: (order: Order) => void;
}

interface ItemProps extends Order {
  onPress: () => void;
  userNote?: string;
}

const Item = React.memo(
  ({
    onPress,
    customerName,
    customerPhone,
    customerAddress,
    totalPrice,
    orderItems,
    status,
    userNote,
  }: ItemProps) => {
    const firstItem = orderItems[0];
    const statusInfo =
      ORDER_STATUS[status as keyof typeof ORDER_STATUS] || ORDER_STATUS.PENDING;

    const statusColor =
      status === ORDER_STATUS.PENDING.key || status === ORDER_STATUS.ACCEPTED.key
        ? colors['Secondary/400']
        : status === ORDER_STATUS.IN_TRANSIT.key
        ? colors['Primary/400']
        : status === ORDER_STATUS.DELIVERED.key || status === ORDER_STATUS.RECEIVED.key
        ? colors['Moss/400']
        : colors['Danger/400'];

    return (
      <TouchableOpacity
        onPress={onPress}
        activeOpacity={0.9}
        style={styles.cardContainer}
      >
        <View style={styles.cardContent}>
          <View style={styles.headerRow}>
            <View style={{ flex: 1 }} />
            {/* <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
              <Text style={styles.statusText}>{statusInfo.name}</Text>
            </View> */}
          </View>

          <View style={styles.itemRow}>
            <Image
              source={{ uri: firstItem.productVariantImage }}
              style={styles.itemImage}
            />
            <View style={styles.itemDetails}>
              <Text style={styles.itemTitle} numberOfLines={2}>
                {firstItem.productTitle}
              </Text>
              <Text style={styles.itemQuantity}>
                Số lượng: {firstItem.quantity}
              </Text>
              <Text style={styles.itemPrice}>
                {new Intl.NumberFormat('vi-VN', {
                  style: 'currency',
                  currency: 'VND',
                }).format(totalPrice)}
              </Text>
            </View>
          </View>

          <View style={styles.customerRow}>
            <Text style={styles.customerInfo} numberOfLines={1}>
              {customerName} | {customerPhone}
            </Text>
            <Text style={styles.customerAddress} numberOfLines={1}>
              {customerAddress}
            </Text>
            {userNote ? (
              <Text style={styles.userNote} numberOfLines={2}>
                Ghi chú: {userNote}
              </Text>
            ) : null}
          </View>
        </View>
      </TouchableOpacity>
    );
  }
);

const OrderItems = ({ orders = [], loading, onItemPress }: OrderItemsProps) => {
  return (
    <View style={styles.content}>
      {loading && orders.length > 0 && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors['Primary/400']} />
        </View>
      )}
      {orders.length > 0 ? (
        orders.map((item) => (
          <Item key={item.id} {...item} onPress={() => onItemPress(item)} />
        ))
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Không có đơn hàng nào</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    paddingHorizontal: metrics.spacing3,
    backgroundColor: colors.white,
    paddingBottom: metrics.spacing2,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: metrics.spacing3,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: metrics.spacing4,
  },
  emptyText: {
    ...commonStyles.subText,
    fontSize: fonts.size.h3,
    color: colors['Grayiron/500'],
  },
  cardContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: metrics.spacing2,
    marginBottom: metrics.spacing2,
    borderWidth: 1,
    borderColor: colors['Grayiron/200'],
  },
  cardContent: {
    flexDirection: 'column',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusBadge: {
    paddingHorizontal: 10,
    borderRadius: 20,
  },
  statusText: {
    fontSize: fonts.size.small,
    fontWeight: '600',
    color: colors.white,
  },
  itemRow: {
    flexDirection: 'row',
    marginBottom: metrics.spacing2,
  },
  itemImage: {
    width: 64,
    height: 68,
    borderRadius: 6,
    marginRight: metrics.spacing2,
  },
  itemDetails: {
    flex: 1,
    justifyContent: 'space-between',
  },
  itemTitle: {
    fontSize: fonts.size.medium,
    fontWeight: '500',
    color: colors['Grayiron/700'],
    marginBottom: 2,
  },
  itemQuantity: {
    fontSize: fonts.size.small,
    color: colors['Grayiron/500'],
    marginBottom: 2,
  },
  itemPrice: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Primary/500'],
  },
  customerInfo: {
    fontSize: fonts.size.medium,
    color: colors['Grayiron/600'],
  },
  customerAddress: {
    fontSize: fonts.size.small,
    color: colors['Grayiron/500'],
    marginTop: 2,
  },
  userNote: {
    fontSize: fonts.size.small,
    color: colors['Grayiron/500'],
    marginTop: 2,
  },
});

export default OrderItems;
