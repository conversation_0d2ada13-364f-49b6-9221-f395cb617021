import { apis } from 'utils/axios';

export const previewPayment = async (
  addressId: string,
  couponId: number = 0,
  couponHathyoCode: string = ''
) => {
  try {
    const payload: any = { addressId: parseInt(addressId) };
    if (couponId !== 0 || couponHathyoCode !== '') {
      payload.couponId = couponId;
      payload.couponHathyoCode = couponHathyoCode;
    }
    console.log('Calling previewPayment with:', payload);
    const response = await apis().post('/checkout/preview', payload);
    console.log('Preview payment response:', response.data);
    return response?.data;
  } catch (e: any) {
    const error = {
      message: e?.response?.data?.message || e?.message || 'Không thể tải dữ liệu giỏ hàng',
      status: e?.response?.status || 'UNKNOWN',
      data: e?.response?.data || null,
    };
    console.error('Lỗi khi lấy giỏ hàng:', error);
    throw error;
  }
};

interface UserNote {
  cartId: number;
  userNote: string;
}

interface CheckoutConfirmParams {
  addressId: number;
  couponId: number;
  couponHathyoCode: string;
  userNote: UserNote[];
}

export const checkoutConfirm = async (data: CheckoutConfirmParams) => {
  try {
    console.log('Calling checkoutConfirm with:', data);
    const response = await apis().post('/checkout/confirm', {
      addressId: data.addressId,
      couponId: data.couponId || null,
      couponHathyoCode: data.couponHathyoCode || null,
      userNote: data.userNote,
    });
    console.log('Checkout confirm response:', response.data);
    return response?.data;
  } catch (e: any) {
    console.error('Lỗi khi xác nhận thanh toán:', e);
    throw e;
  }
};
