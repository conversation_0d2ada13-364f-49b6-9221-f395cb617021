/* eslint-disable react/no-unstable-nested-components */
import React from 'react';
import { Text, TouchableWithoutFeedback } from 'react-native';
import { Input, Select, SelectItem } from '@ui-kitten/components';
import { Controller, FieldError } from 'react-hook-form';
import Ionicons from 'react-native-vector-icons/Ionicons';
import commonStyles from 'themes/commonStyles';
import { get, map } from 'lodash';

type Props = {
  errors: { message: string } | undefined | FieldError;
  control: any;
  label: string;
  placeholder: string;
  name: string;
  required?: boolean;
  accessoryRight?: any;
  options?: string[];
};

const FormSelect = ({
  errors,
  control,
  label,
  required,
  placeholder,
  name,
  accessoryRight,
  options,
}: Props) => {
  return (
    <>
      <Text style={commonStyles.label}>{label}</Text>
      <Controller
        control={control}
        rules={{
          required,
        }}
        render={({ field: { onChange, onBlur, value: index } }) => (
          <Select
            placeholder={placeholder}
            onBlur={onBlur}
            onSelect={onChange}
            value={get(options, index)}
            accessoryRight={accessoryRight}>
            {map(options, item => (
              <SelectItem key={item} title={item} />
            ))}
          </Select>
        )}
        name={name}
      />
      {errors && <Text style={commonStyles.errorText}>{errors?.message}</Text>}
    </>
  );
};

export default FormSelect;
