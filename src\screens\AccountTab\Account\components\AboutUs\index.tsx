import React, { useState, useRef, useEffect } from 'react';
import { View, StyleSheet, Text, ScrollView, TouchableOpacity, Animated, Alert, Dimensions } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import RowItem from 'components/RowItem';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const DropdownSection = ({ title, children, iconName, defaultOpen = false }) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const [animation] = useState(new Animated.Value(defaultOpen ? 1 : 0));
  const [rotateAnimation] = useState(new Animated.Value(defaultOpen ? 1 : 0));
  const [contentHeight, setContentHeight] = useState(0);

  const onLayout = (event) => {
    const { height } = event.nativeEvent.layout;
    setContentHeight(height);
  };

  const toggleSection = () => {
    const toValue = isOpen ? 0 : 1;
    
    Animated.parallel([
      Animated.timing(animation, {
        toValue,
        duration: 300,
        useNativeDriver: false,
      }),
      Animated.timing(rotateAnimation, {
        toValue,
        duration: 300,
        useNativeDriver: true,
      })
    ]).start();
    
    setIsOpen(!isOpen);
  };

  const maxHeight = animation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, contentHeight || 1000], // Dynamic height or fallback
  });

  const rotate = rotateAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  return (
    <View style={styles.section}>
      <TouchableOpacity
        style={styles.header}
        onPress={toggleSection}
        activeOpacity={0.8}
        accessibilityRole="button"
        accessibilityLabel={title}
      >
        <View style={styles.headerLeft}>
          <View style={styles.iconContainer}>
            <Icon 
              name={iconName} 
              size={24} 
              color={colors['Grayiron/600']}
            />
          </View>
          <Text style={styles.headerText}>
            {title}
          </Text>
        </View>
        <Animated.View style={{ transform: [{ rotate }] }}>
          <Icon
            name="chevron-down"
            size={24}
            color={colors['Grayiron/600']}
          />
        </Animated.View>
      </TouchableOpacity>
      
      <Animated.View 
        style={[
          styles.sectionContent, 
          { 
            maxHeight,
            opacity: animation,
          }
        ]}
      >
        <View onLayout={onLayout}>
          {children}
        </View>
      </Animated.View>
    </View>
  );
};

const ModernRowItem = ({ title, leftIconName, onPress, isLast = false }) => (
  <View style={[styles.modernRowItem, isLast && styles.lastRowItem]}>
    <RowItem
      title={title}
      leftIconName={leftIconName}
      onPress={onPress}
      iconSize={20}
    />
  </View>
);

const AboutUs = () => {
  const navigation = useNavigation();
  
  const navigateToWebView = (title, uri) => {
    try {
      const fullUri = `${uri}?mode=mobile`;
      console.log(`Navigating to WebviewScreen with title: ${title}, uri: ${fullUri}`);
      
      if (!uri.startsWith('http')) {
        throw new Error('Invalid URL format');
      }
      
      if (!navigation) {
        throw new Error('Navigation object is undefined');
      }
      
      navigation.navigate('WebviewScreen', { uri: fullUri, title });
    } catch (error) {
      console.error('Navigation error:', error);
      Alert.alert(
        'Lỗi',
        `Không thể mở trang "${title}". Vui lòng kiểm tra kết nối mạng hoặc liên hệ hỗ trợ.`,
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <View style={styles.container}>
      <View style={commonStyles.rowSpaceBetween}>
        <Text style={styles.title}>Về Hathyo</Text>
      </View>
      
      <ScrollView 
        style={styles.scrollView} 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          <DropdownSection 
            title="Giới thiệu Hathyo" 
            iconName="information" 
            defaultOpen={true}
          >
            <View style={styles.itemContainer}>
              <ModernRowItem
                title="Giới thiệu"
                leftIconName="account-circle-outline"
                onPress={() => navigateToWebView('Giới thiệu', 'https://hathyo.com/info/about')}
              />
              <ModernRowItem
                title="Hành trình vui khỏe"
                leftIconName="heart-outline"
                onPress={() => navigateToWebView('Hành trình vui khỏe', 'https://hathyo.com/user/wellness-transformations')}
                isLast={true}
              />
            </View>
          </DropdownSection>

          <DropdownSection title="Hỗ trợ" iconName="help-circle-outline">
            <View style={styles.itemContainer}>
              <ModernRowItem
                title="Câu hỏi thường gặp"
                leftIconName="comment-question-outline"
                onPress={() => navigateToWebView('Câu hỏi thường gặp', 'https://hathyo.com/user/faqs')}
              />
              <ModernRowItem
                title="Trung tâm trợ giúp"
                leftIconName="help-circle-outline"
                onPress={() => navigateToWebView('Trung tâm trợ giúp', 'https://hathyo.com/helps/help-center')}
              />
              <ModernRowItem
                title="Tra cứu xử lý liên hệ"
                leftIconName="magnify"
                onPress={() => navigateToWebView('Tra cứu xử lý liên hệ', 'https://hathyo.com/helps/feedback/list')}
              />
              <ModernRowItem
                title="Liên hệ Hathyo"
                leftIconName="phone-outline"
                onPress={() => navigateToWebView('Liên hệ Hathyo', 'https://hathyo.com/helps/feedback')}
              />
              <ModernRowItem
                title="Tiếp nhận phản ánh của TCXH"
                leftIconName="phone-outline"
                onPress={() => navigateToWebView('Tiếp nhận phản ánh của TCXH', 'https://hathyo.com/tcxh')}
              />
              <ModernRowItem
                title="Danh sách phản ánh của TCXH"
                leftIconName="format-list-bulleted"
                onPress={() => navigateToWebView('Danh sách phản ánh của TCXH', 'https://hathyo.com/tcxh/list')}
                isLast={true}
              />
            </View>
          </DropdownSection>

          <DropdownSection title="Chính sách & Quy định" iconName="shield-check-outline">
            <View style={styles.itemContainer}>
              <ModernRowItem
                title="Quy chế hoạt động"
                leftIconName="book-open-outline"
                onPress={() => navigateToWebView('Quy chế hoạt động', 'https://hathyo.com/static-content/terms-of-use')}
              />
              <ModernRowItem
                title="Chính sách bảo mật"
                leftIconName="lock-outline"
                onPress={() => navigateToWebView('Chính sách bảo mật', 'https://hathyo.com/static-content/privacy-policy')}
              />
              <ModernRowItem
                title="Cơ chế giải quyết tranh chấp"
                leftIconName="gavel"
                onPress={() => navigateToWebView('Cơ chế giải quyết tranh chấp', 'https://hathyo.com/static-content/dispute-resolution')}
              />
              <ModernRowItem
                title="Lưu ý quan trọng"
                leftIconName="alert-circle-outline"
                onPress={() => navigateToWebView('Lưu ý quan trọng', 'https://hathyo.com/static-content/disclaimers')}
                isLast={true}
              />
            </View>
          </DropdownSection>

          <DropdownSection title="Thông tin công ty" iconName="office-building-outline">
            <View style={styles.companyDetails}>
              <View style={styles.companyItem}>
                <Icon name="domain" size={16} color={colors['Grayiron/600']} />
                <Text style={styles.companyText}>Công ty TNHH Cuộc Sống Vui Khỏe</Text>
              </View>
              
              <View style={styles.companyItem}>
                <Icon name="map-marker-outline" size={16} color={colors['Grayiron/600']} />
                <Text style={styles.companyText}>82 Phan Đăng Lưu, P.05, Q.Phú Nhuận, TP.HCM</Text>
              </View>
              
              <View style={styles.companyItem}>
                <Icon name="phone-outline" size={16} color={colors['Grayiron/600']} />
                <Text style={styles.companyText}>0084-*********</Text>
              </View>
              
              <View style={styles.companyItem}>
                <Icon name="email-outline" size={16} color={colors['Grayiron/600']} />
                <Text style={styles.companyText}><EMAIL></Text>
              </View>
              
              <View style={styles.companyItem}>
                <Icon name="account-tie" size={16} color={colors['Grayiron/600']} />
                <Text style={styles.companyText}>GĐ: Hoàng Minh Phụng | **********</Text>
              </View>
              
              <View style={styles.companyItem}>
                <Icon name="certificate-outline" size={16} color={colors['Grayiron/600']} />
                <Text style={styles.companyText}>ĐKKD: **********, cấp 17/11/2023</Text>
              </View>
              
              <View style={styles.copyrightContainer}>
                <Text style={styles.copyrightText}>© 2023-2025 Công ty TNHH Cuộc Sống Vui Khỏe</Text>
              </View>
            </View>
          </DropdownSection>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingTop: metrics.spacing4,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: metrics.spacing4,
  },
  content: {
    paddingVertical: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
  },
  section: {
    marginBottom: metrics.spacing2,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: metrics.spacing2,
    paddingHorizontal: metrics.spacing6,
    backgroundColor: 'white',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: metrics.spacing2,
  },
  headerText: {
    fontSize: fonts.size.regular,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    flex: 1,
  },
  sectionContent: {
    overflow: 'hidden',
    backgroundColor: 'white',
  },
  itemContainer: {
    paddingHorizontal: metrics.spacing6,
  },
  modernRowItem: {
    backgroundColor: 'white',
    marginLeft: metrics.spacing5,
  },
  lastRowItem: {
    // Keep for potential future use
  },
  companyDetails: {
    paddingHorizontal: metrics.spacing6,
    paddingBottom: metrics.spacing2,
  },
  companyItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: metrics.spacing2,
  },
  companyText: {
    fontSize: fonts.size.medium,
    color: colors['Grayiron/600'],
    lineHeight: fonts.size.medium * 1.4,
    marginLeft: metrics.spacing2,
    flex: 1,
  },
  copyrightContainer: {
    paddingTop: metrics.spacing2,
    marginTop: metrics.spacing2,
    borderTopWidth: 1,
    borderTopColor: colors['Grayiron/200'],
    alignItems: 'center',
  },
  copyrightText: {
    fontSize: fonts.size.medium,
    color: colors['Grayiron/500'],
    textAlign: 'center',
  },
});

export default AboutUs;