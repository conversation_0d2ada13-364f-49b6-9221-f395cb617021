import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
} from 'react-native';
import { getKeytoDeleteAccount } from 'api/users';
import colors from 'themes/colors';
import fonts from 'themes/fonts';
import metrics from 'themes/metrics';

const DebugDeleteKey: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testAPI = async () => {
    try {
      setLoading(true);
      setError(null);
      setResponse(null);
      
      console.log('DebugDeleteKey - Testing API call');
      const result = await getKeytoDeleteAccount();
      console.log('DebugDeleteKey - API result:', result);
      
      setResponse(result);
      Alert.alert('Success', 'API call successful! Check console for details.');
    } catch (err: any) {
      console.error('DebugDeleteKey - API error:', err);
      setError(err?.message || 'Unknown error');
      Alert.alert('Error', `API call failed: ${err?.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Debug Delete Key API</Text>
      
      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={testAPI}
        disabled={loading}
      >
        <Text style={styles.buttonText}>
          {loading ? 'Testing...' : 'Test API Call'}
        </Text>
      </TouchableOpacity>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Error:</Text>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {response && (
        <View style={styles.responseContainer}>
          <Text style={styles.responseTitle}>Response:</Text>
          <Text style={styles.responseText}>
            {JSON.stringify(response, null, 2)}
          </Text>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: metrics.spacing4,
    backgroundColor: colors['Gray/50'],
  },
  title: {
    ...fonts.style.h2,
    color: colors['Gray/900'],
    textAlign: 'center',
    marginBottom: metrics.spacing4,
  },
  button: {
    backgroundColor: colors['Primary/500'],
    padding: metrics.spacing3,
    borderRadius: metrics.radius3,
    alignItems: 'center',
    marginBottom: metrics.spacing4,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    ...fonts.style.normal,
    color: colors.white,
    fontWeight: '600',
  },
  errorContainer: {
    backgroundColor: colors['Danger/50'],
    padding: metrics.spacing3,
    borderRadius: metrics.radius3,
    marginBottom: metrics.spacing4,
    borderWidth: 1,
    borderColor: colors['Danger/200'],
  },
  errorTitle: {
    ...fonts.style.normal,
    color: colors['Danger/700'],
    fontWeight: '600',
    marginBottom: metrics.spacing1,
  },
  errorText: {
    ...fonts.style.description,
    color: colors['Danger/600'],
  },
  responseContainer: {
    backgroundColor: colors['Success/50'],
    padding: metrics.spacing3,
    borderRadius: metrics.radius3,
    borderWidth: 1,
    borderColor: colors['Success/200'],
  },
  responseTitle: {
    ...fonts.style.normal,
    color: colors['Success/700'],
    fontWeight: '600',
    marginBottom: metrics.spacing1,
  },
  responseText: {
    ...fonts.style.description,
    color: colors['Success/600'],
    fontFamily: 'monospace',
  },
});

export default DebugDeleteKey;
