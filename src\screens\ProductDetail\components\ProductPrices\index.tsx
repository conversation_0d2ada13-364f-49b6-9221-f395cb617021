import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Text } from '@ui-kitten/components';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import Rating from 'components/Rating';
import { formatNumber } from 'utils/index';

const { width } = Dimensions.get('window');

const ProductPrices = ({
  price,
  anchoPrice,
  discountPercent,
  title,
  countNumOfRating,
}: any) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title} numberOfLines={2} ellipsizeMode="tail">
        {title}
      </Text>
      
      <View style={styles.priceSection}>
        <View style={styles.priceRow}>
          <Text style={styles.price}>{formatNumber(price)} đ</Text>
          {anchoPrice && anchoPrice > price && (
            <Text style={styles.originalPrice}>{formatNumber(anchoPrice)} đ</Text>
          )}
        </View>
        
        {discountPercent ? (
          <View style={styles.discountContainer}>
            <Text style={styles.discountText}>-{discountPercent}%</Text>
          </View>
        ) : null}
      </View>

      <View style={styles.ratingContainer}>
        <Rating size="medium" />
        <Text style={styles.rateText}>
          {countNumOfRating > 0 ? `(${countNumOfRating})` : '(Chưa có đánh giá)'}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: metrics.spacing3,
  },
  title: {
    fontSize: fonts.size.h2,
    fontWeight: '700',
    color: colors['Gray/800'],
    marginBottom: metrics.spacing3,
    lineHeight: 22,
  },
  priceSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: metrics.spacing3,
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  price: {
    fontSize: 20,
    fontWeight: '700',
    color: colors['Warning/500'],
    marginRight: metrics.spacing2,
  },
  originalPrice: {
    fontSize: fonts.size.h3,
    color: colors['Gray/400'],
    textDecorationLine: 'line-through',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rateText: {
    fontSize: fonts.size.regular,
    color: colors['Gray/500'],
    marginLeft: metrics.spacing2,
  },
  discountContainer: {
    backgroundColor: colors['Danger/50'],
    borderRadius: 8,
    paddingHorizontal: metrics.spacing2,
    paddingVertical: 2,
    height: 20,
  },
  discountText: {
    fontSize: fonts.size.small,
    fontWeight: '700',
    color: colors['Danger/500'],
  },
});

export default ProductPrices;