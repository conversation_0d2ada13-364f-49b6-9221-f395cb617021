import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Text, ScrollView, Dimensions, TouchableOpacity } from 'react-native';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import Product from 'components/Product';
import Tag from 'components/Tag';
import { useNavigation } from '@react-navigation/native';
import { map } from 'lodash';
import { PRODUCT_SORT } from 'themes/constant';
import { addRecentProducts } from 'utils/cart';
import { useLoadMore } from './hook';
import AddToCartModal from 'components/AddToCardModel';
import { useCart } from 'hooks/contexts/CartContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE } from 'constants/authentication';


type Props = {
  sort?: any;
  products?: any;
  setSort?: any;
};

const VerticalProducts = ({ sort, products, setSort }: Props) => {
  const navigation = useNavigation();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<any>(null);
  const { fetchCartCount } = useCart();
  
  const { displayedItems, hasMore, loadMore } = useLoadMore({
    initialItems: products || [],
    itemsPerPage: 10,
  });

  useEffect(() => {
    console.log('Products data:', products);
  }, [products]);

  const handleAddToCart = async (product: any, quantity: number, variant: any) => {
    const token = await AsyncStorage.getItem(STORAGE.ACCESS_TOKEN);
    if (!token) {
      Toast.show({
        type: 'error',
        text1: 'Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng',
        position: 'top',
      });
      return false;
    }

    try {
      const cartItem = {
        id: product.id,
        merchantId: product.merchantId,
        quantity: quantity,
        price: variant ? variant.price : product.price,
        ...(variant?.id && { variantId: variant.id }),
      };

      // Gọi API thêm vào giỏ hàng
      // const response = await addToCart(cartItem);
      // Giả lập thành công
      const response = { success: true };

      if (response?.success) {
        await fetchCartCount();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Add to cart error:', error);
      Toast.show({
        type: 'error',
        text1: 'Thêm vào giỏ hàng thất bại',
        position: 'top',
      });
      return false;
    }
  };

  const onViewDetail = (p: any) => {
    addRecentProducts(p);
    navigation?.navigate('ProductDetail', { id: p.id });
  };

  const openModal = (product: any) => {
    setSelectedProduct(product);
    setModalVisible(true);
  };

  if (!products || products.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Danh sách sản phẩm</Text>
        {sort && (
          <ScrollView
            horizontal
            style={styles.tags}
            bounces
            showsHorizontalScrollIndicator={false}>
            {map(PRODUCT_SORT, item => (
              <Tag
                key={item?.key}
                customStyles={styles.tag}
                size="large"
                type={sort === item?.key ? 'success' : 'default'}
                title={item?.name}
                onPress={() => setSort(item?.key)}
              />
            ))}
          </ScrollView>
        )}
        <Text style={styles.emptyMessage}>Không có sản phẩm nào</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Danh sách sản phẩm</Text>
      {sort && (
        <ScrollView
          horizontal
          style={styles.tags}
          bounces
          showsHorizontalScrollIndicator={false}>
          {map(PRODUCT_SORT, item => (
            <Tag
              key={item?.key}
              customStyles={styles.tag}
              size="large"
              type={sort === item?.key ? 'success' : 'default'}
              title={item?.name}
              onPress={() => setSort(item?.key)}
            />
          ))}
        </ScrollView>
      )}
      <View style={styles.row}>
        {Array.isArray(displayedItems) && displayedItems.map((product, index) => (
          <View 
            key={`${product?.id || index}`} 
            style={styles.productContainer}
          >
            <Product
              customStyles={styles.productItem}
              title={product?.title}
              uri={product?.mainImageUrl}
              price={product?.price}
              anchoPrice={product?.anchoPrice}
              rating={product?.rating}
              discountPercent={product?.discountPercent}
              onPress={() => onViewDetail(product)}
              onAddToCart={() => openModal(product)}
            />
          </View>
        ))}
      </View>
      {hasMore && (
        <TouchableOpacity style={styles.loadMoreButton} onPress={loadMore}>
          <Text style={styles.loadMoreText}>Tải thêm</Text>
        </TouchableOpacity>
      )}

      <AddToCartModal
        visible={modalVisible}
        product={selectedProduct}
        onClose={() => setModalVisible(false)}
        onAddToCart={handleAddToCart}
      />
    </View>
  );
};

const { width } = Dimensions.get('window');
const productWidth = (width - 24 - 16 - 16) / 2;

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginHorizontal: -8,
  },
  productContainer: {
    width: '50%',
    paddingHorizontal: 8,
    marginBottom: 16,
  },
  productItem: {
    width: '100%',
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  tag: {
    marginRight: metrics.spacing2,
  },
  tags: {
    marginBottom: metrics.spacing4,
  },
  emptyMessage: {
    fontSize: fonts.size.medium,
    color: colors['Grayiron/400'],
    textAlign: 'center',
    marginTop: 20,
  },
  loadMoreButton: {
    backgroundColor: colors['Moss/400'],
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: metrics.spacing2,
    marginBottom: metrics.spacing4,
  },
  loadMoreText: {
    color: colors.white,
    fontSize: fonts.size.medium,
    fontWeight: '600',
  },
});

export default VerticalProducts;