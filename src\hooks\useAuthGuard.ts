import { useState, useEffect } from 'react';
import { useNavigation } from '@react-navigation/native';
import { checkAuthentication } from 'utils/authUtils';

interface UseAuthGuardOptions {
  requireAuth?: boolean;
  redirectOnAuth?: boolean;
  customMessage?: string;
  onAuthRequired?: () => void;
  onAuthSuccess?: () => void;
}

interface UseAuthGuardReturn {
  isAuthenticated: boolean | null;
  isLoading: boolean;
  showLoginAlert: (customMessage?: string) => void;
  requireAuth: (action: () => void | Promise<void>, customMessage?: string) => Promise<boolean>;
}

export const useAuthGuard = (options: UseAuthGuardOptions = {}): UseAuthGuardReturn => {
  const {
    requireAuth = true,
    redirectOnAuth = true,
    customMessage,
    onAuthRequired,
    onAuthSuccess,
  } = options;

  const navigation = useNavigation<any>();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!requireAuth) {
      setIsAuthenticated(true);
      setIsLoading(false);
      return;
    }

    const checkAuth = async () => {
      try {
        const { isAuthenticated: authStatus } = await checkAuthentication();
        setIsAuthenticated(authStatus);
        
        if (authStatus && onAuthSuccess) {
          onAuthSuccess();
        } else if (!authStatus && onAuthRequired) {
          onAuthRequired();
        }
      } catch (error) {
        console.error('Auth check error:', error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [requireAuth, onAuthRequired, onAuthSuccess]);

  const showLoginAlert = (alertMessage?: string) => {
    // This would typically be handled by the component using this hook
    // by showing a CustomAlert
    console.log('Login required:', alertMessage || customMessage || 'Please login to continue');
    
    if (redirectOnAuth) {
      navigation.navigate('Authentication');
    }
  };

  const requireAuthAction = async (
    action: () => void | Promise<void>,
    actionMessage?: string
  ): Promise<boolean> => {
    const { isAuthenticated: currentAuthStatus } = await checkAuthentication();
    
    if (!currentAuthStatus) {
      showLoginAlert(actionMessage || customMessage);
      return false;
    }
    
    try {
      await action();
      return true;
    } catch (error) {
      console.error('Error executing authenticated action:', error);
      return false;
    }
  };

  return {
    isAuthenticated,
    isLoading,
    showLoginAlert,
    requireAuth: requireAuthAction,
  };
};

export default useAuthGuard;
