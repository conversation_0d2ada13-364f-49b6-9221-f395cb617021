/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useRef, useState } from 'react';
import { getCollectedCoupons, getCouponDetail } from 'api/coupons';
import { Coupon, CollectedCoupon } from 'types/Coupon';

export const useCoupons = (type = 'HATHYO', page = 0, size = 10) => {
  const [activeCoupons, setActiveCoupons] = useState<Coupon[]>([]);
  const [collectedCoupons, setCollectedCoupons] = useState<CollectedCoupon[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(page);
  const isMounted = useRef(true);

  const transformCoupon = (coupon: any): Coupon | null => {
    try {
      if (!coupon.id || !coupon.code) {
        console.error('Invalid coupon missing id or code:', coupon);
        return null;
      }
      return {
        id: coupon.id,
        code: coupon.code,
        type: coupon.type || 'HATHYO',
        discountType: coupon.discountType || 'PERCENT',
        discountPercent:
          coupon.discountPercent !== null && !isNaN(parseFloat(coupon.discountPercent))
            ? parseFloat(coupon.discountPercent)
            : null,
        discountValue:
          coupon.discountValue !== null && !isNaN(parseFloat(coupon.discountValue))
            ? parseFloat(coupon.discountValue)
            : null,
        minimumPriceApply:
          coupon.minimumPriceApply !== null ? parseFloat(coupon.minimumPriceApply) : 0,
        maxDiscountPrice:
          coupon.maxDiscountPrice !== null ? parseFloat(coupon.maxDiscountPrice) : null,
        title: coupon.title || '',
        description: coupon.description || '',
        image: coupon.image || '',
        expiredAt: coupon.expiredAt || '',
        applyStatus: coupon.applyStatus !== undefined ? !!coupon.applyStatus : null,
      };
    } catch (err) {
      console.warn('Invalid coupon data:', coupon, err);
      return null;
    }
  };

  const fetchCollectedCoupons = useCallback(async () => {
    if (!isMounted.current) return;
    try {
      setLoading(true);
      setError(null);
      console.log('Fetching collected coupons:', { type, page: currentPage, size });
      const response = await getCollectedCoupons(0, type, currentPage, size);
      console.log('Collected coupons response:', response);
      if (!isMounted.current) return;
      if (!response || !response.coupons) {
        setCollectedCoupons([]);
        setActiveCoupons([]);
        return;
      }
      const validCoupons = response.coupons
        .map((item: any) => ({
          couponId: item.id,
          coupon: transformCoupon(item),
        }))
        .filter((item: any): item is CollectedCoupon => item.coupon !== null);
      setCollectedCoupons(validCoupons);
      setActiveCoupons(response.coupons.map(transformCoupon).filter((coupon): coupon is Coupon => coupon !== null));
      setTotalPages(response.totalPages || 1);
    } catch (err: any) {
      if (!isMounted.current) return;
      const errorMessage =
        err.response?.data?.message ||
        (err.message.includes('Access denied')
          ? 'Không có quyền truy cập coupon. Vui lòng đăng nhập lại.'
          : 'Không thể tải danh sách coupon đã thu thập. Vui lòng thử lại.');
      console.error('Error fetching collected coupons:', err.message, err.response?.data);
      setError(errorMessage);
    } finally {
      if (isMounted.current) setLoading(false);
    }
  }, [type, currentPage, size]);

  const fetchCouponDetail = useCallback(async (id: number): Promise<Coupon | null> => {
    if (!isMounted.current) return null;
    try {
      setLoading(true);
      setError(null);
      console.log('Fetching coupon detail:', { id });
      const coupon = await getCouponDetail(id);
      const transformedCoupon = transformCoupon(coupon);
      console.log('Coupon detail response:', transformedCoupon);
      return transformedCoupon;
    } catch (err: any) {
      if (!isMounted.current) return null;
      const errorMessage =
        err.response?.data?.message ||
        'Không thể tải chi tiết coupon. Vui lòng thử lại.';
      console.error('Error fetching coupon detail:', err.message, err.response?.data);
      setError(errorMessage);
      return null;
    } finally {
      if (isMounted.current) setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCollectedCoupons();
    return () => {
      isMounted.current = false;
    };
  }, [fetchCollectedCoupons]);

  return {
    activeCoupons,
    collectedCoupons,
    loading,
    error,
    totalPages,
    currentPage,
    setCurrentPage,
    fetchCollectedCoupons,
    fetchCouponDetail,
  };
};