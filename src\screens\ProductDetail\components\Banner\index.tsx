import React from 'react';
import { View, ImageBackground, StyleSheet } from 'react-native';
import { Text } from '@ui-kitten/components';

import commonStyles from 'themes/commonStyles';
import metrics from 'themes/metrics';
import Icon from 'react-native-vector-icons/FontAwesome6';
import { BlurView } from '@react-native-community/blur';

const maxWidth = metrics.fullWidth - metrics.spacing4 * 2;
const maxHeight = (metrics.fullWidth - metrics.spacing4 * 2) / 2;
const Banner = () => {
  return (
    <ImageBackground
      style={styles.container}
      imageStyle={styles.imageStyle}
      resizeMode="cover"
      source={require('assets/images/banner.png')}>
      <View style={styles.border}>
        <BlurView
          style={styles.hintBackground}
          blurType="light"
          blurAmount={8}
          reducedTransparencyFallbackColor="white">
          <Icon name="quote-left" size={30} color="white" />
          <Text style={styles.title}>
            <PERSON><PERSON><PERSON> khỏe là tài sản vô giá, là chìa khóa mở cánh cửa cho cuộc sống
            trọn vẹn.
          </Text>
          <Text style={styles.title}>- Sưu tầm -</Text>
        </BlurView>
      </View>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    width: maxWidth,
    height: maxHeight,
    paddingHorizontal: metrics.spacing4,
    borderRadius: metrics.radius4,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  imageStyle: {
    width: maxWidth,
    height: maxHeight,
    borderRadius: metrics.radius4,
  },
  hintBackground: {
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing2,
    backgroundColor: 'rgba(30, 37, 25, 0.4)',
  },
  border: {
    borderTopLeftRadius: metrics.radius8,
    borderTopRightRadius: metrics.radius2,
    borderBottomLeftRadius: metrics.radius2,
    borderBottomRightRadius: metrics.radius8,
    overflow: 'hidden',
  },
  title: {
    ...commonStyles.whiteTitle,
    textAlign: 'center',
    marginVertical: metrics.spacing2,
  },
});

export default Banner;
