import { getCategories } from 'api/products';
import { PRODUCT_SORT } from 'themes/constant';
import { create } from 'zustand';

export const useProductsStore = create((set) => ({
  categories: [],
  productsFilter: [],
  filter: {
    categoryId: null,
    categoryName: '',
    sort: PRODUCT_SORT.createdAsc.key,
     query: '',
  },
  setCategories: (categories: any) => set({ categories }),
  setProductsFilter: (productsFilter: any) => set({ productsFilter }),
  setFilter: (filter: any) => set({ filter }),
  fetchCategories: async () => {
    try {
      const response = await getCategories();
      console.log('Fetched Categories:', response?.categories);
      set({ categories: response?.categories || [] });
    } catch (error) {
      console.error('Error fetching categories:', error);
      set({ categories: [] });
    }
  },
}));