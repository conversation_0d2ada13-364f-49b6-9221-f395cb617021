import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { CheckBox, Divider, Text } from '@ui-kitten/components';
import { useNavigation } from '@react-navigation/native';
import ProductCard from 'components/ProductCard';
import MerchantItem from 'components/MerchantItem';
import commonStyles from 'themes/commonStyles';
import { filter, find, get, groupBy, map } from 'lodash';
import { setStorageCarts } from 'utils/cart';
import metrics from 'themes/metrics';

const VerticalProductsItems = ({ 
  cart, 
  setCart, 
  selected, 
  setSelected,
  handleSelectAll,
  handleSelectByMerchant,
  handleSelectByCartItemId,
  handleDeleteCartItem,
  handleUpdateCartItem,
  updatingItemId 
}) => {
  const navigation = useNavigation();
  const [deletingId, setDeletingId] = useState(null);
  const groupByMerchant = groupBy(cart, 'merchantId');

  const getMerchantInfo = (merchantId) => {
    const item = find(cart, item => item.merchantId === merchantId);
    return item?.merchantResponse || { storeName: 'Cửa hàng không xác định', logo: '' };
  };

  const handleCheckMerchant = (checked: boolean, merchantId: string) => {
    console.log('Handle check merchant:', merchantId, checked);
    handleSelectByMerchant(merchantId, checked);
  };

  const handleCheckProduct = (checked: boolean, product: any) => {
    console.log('Handle check product:', product.id, checked);
    handleSelectByCartItemId(product.id, checked);
  };

  const isMerchantChecked = (merchantId: any) => {
    const merchantProducts = get(groupByMerchant, merchantId, []);
    const selectedCount = selected.filter(
      (item: any) => item.merchantId === merchantId
    ).length;
    return merchantProducts.length > 0 && merchantProducts.length === selectedCount;
  };

  const handleDelete = async (product: any) => {
    try {
      setDeletingId(product.id);
      await handleDeleteCartItem(product.id);
    } catch (error) {
      console.error('Error deleting item:', error);
    } finally {
      setDeletingId(null);
    }
  };

  const handleUpdateQuantity = (product: any, newQuantity: number) => {
    handleUpdateCartItem(product.id, newQuantity);
  };

  const navigateToProductDetail = (productId: any) => {
    if (productId) {
      (navigation as any).navigate('ProductDetail', { id: productId });
    } else {
      console.warn('Cannot navigate: productId is missing');
    }
  };

  if (!cart || cart.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={commonStyles.mainText}>Giỏ hàng của bạn đang trống</Text>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.continueShopping}>Tiếp tục mua sắm</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <CheckBox
          onChange={(checked) => handleSelectAll(checked)}
          checked={selected.length === cart.length && cart.length > 0}
          status="basic"
        >
          <Text style={commonStyles.mainText}>Chọn tất cả</Text>
        </CheckBox>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.shopMoreText}>Tiếp tục mua sắm</Text>
        </TouchableOpacity>
      </View>

      {map(groupByMerchant, (products, merchantId) => (
        <View key={merchantId} style={styles.merchantCard}>
          <MerchantItem
            checked={isMerchantChecked(merchantId)}
            uri={getMerchantInfo(merchantId).logo}
            title={getMerchantInfo(merchantId).storeName}
            onCheck={(checked) => handleCheckMerchant(checked, merchantId)}
          />
          {map(products, (product, index) => (
            <React.Fragment key={product.id}>
              <ProductCard
                anchoPrice={product.anchoPrice || product.price}
                price={product.price}
                checked={selected.some((item: any) => item.id === product.id)}
                uri={product.mainImageUrl || ''}
                qty={product.quantity}
                title={product.title}
                setChecked={(checked) => handleCheckProduct(checked, product)}
                onDelete={() => handleDelete(product)}
                variant={product.variants?.[0]?.variantName || ''}
                setQty={(qty) => handleUpdateQuantity(product, qty)}
                onPress={() => navigateToProductDetail(product.productId)}
              />
              {index < products.length - 1 && <Divider style={styles.divider} />}
            </React.Fragment>
          ))}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: metrics.spacing2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: metrics.spacing2,
    marginBottom: metrics.spacing2,
  },
  merchantCard: {
    backgroundColor: 'white',
    borderRadius: metrics.radius2,
    marginBottom: metrics.spacing2,
    overflow: 'hidden',
  },
  divider: {
    marginHorizontal: metrics.spacing2,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: metrics.spacing2 * 2,
  },
  continueShopping: {
    marginTop: metrics.spacing2,
    color: '#007AFF',
    fontWeight: '500',
  },
  shopMoreText: {
    color: '#007AFF',
    fontWeight: '500',
  },
});

export default VerticalProductsItems;