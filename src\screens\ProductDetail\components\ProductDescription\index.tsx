import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, useWindowDimensions, Linking } from 'react-native';
import RenderHtml, { MixedStyleRecord, RenderersProps } from 'react-native-render-html';
import { Text } from '@ui-kitten/components';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';

interface ProductDescriptionProps {
  fullDescription: string;
  shortDescription: string;
}

const ProductDescription: React.FC<ProductDescriptionProps> = ({
  fullDescription,
  shortDescription,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { width } = useWindowDimensions();

  // Custom styling for HTML tags with proper typing
  const tagsStyles: MixedStyleRecord = {
    body: {
      fontSize: fonts.size.regular,
      color: colors['Grayiron/600'],
      lineHeight: 22,
      textAlign: 'left',
    },
    p: {
      marginBottom: metrics.spacing4,
      marginTop: 0,
    },
    strong: {
      fontWeight: '800', // Use string literals for fontWeight
      color: colors['Grayiron/600'],
    },
    span: {
      fontSize: fonts.size.regular,
      color: colors['Grayiron/200'],
    },
    a: {
      color: colors['Moss/600'],
      textDecorationLine: 'underline',
    },
    // Handle specific HTML attributes
    'p[style*="text-align:justify"]': {
      textAlign: 'justify',
    },
    'p[style*="margin-left"]': {
      paddingLeft: metrics.spacing4,
    },
  };

  // Typed renderers props
  const renderersProps: RenderersProps = {
    a: {
      onPress: (event, href) => {
        Linking.openURL(href).catch(err => 
          console.error("Failed to open URL:", err)
        );
      },
    },
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Mô tả sản phẩm</Text>
      <View style={styles.content}>
        <RenderHtml
          contentWidth={width - metrics.spacing4 * 2}
          source={{ html: isExpanded ? fullDescription : shortDescription }}
          tagsStyles={tagsStyles}
          renderersProps={renderersProps}
          baseStyle={styles.htmlBaseStyle}
        />
        <TouchableOpacity
          onPress={() => setIsExpanded(!isExpanded)}
          style={commonStyles.rowCenter}
          activeOpacity={0.7}
        >
          <Text style={styles.moreText}>
            {isExpanded ? 'Thu gọn' : 'Xem thêm'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: metrics.spacing4,
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing4,
    backgroundColor: 'white',
  },
  content: {
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,

  },
  moreText: {
    fontSize: fonts.size.regular,
    fontWeight: '600',
    color: colors['Moss/600'],
    marginTop: metrics.spacing4,
    textAlign: 'left',
  },
  htmlBaseStyle: {
    fontSize: fonts.size.regular,
    color: colors['Grayiron/600'],
    lineHeight: 22,
  },
});

export default ProductDescription;