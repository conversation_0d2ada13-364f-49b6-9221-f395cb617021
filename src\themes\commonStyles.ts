// import Fonts from './fonts';
import Metrics from './metrics';
import Colors from './colors';
import { StyleSheet } from 'react-native';
import fonts from './fonts';
import colors from './colors';
import metrics from './metrics';

// This file is for a reusable grouping of Theme items.
// Similar to an XML fragment layout in Android

const commonStyles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: Colors.transparent,
  },
  container: {
    flex: 1,
    marginTop: Metrics.spacing3,
    backgroundColor: Colors.transparent,
  },
  row: {
    flexDirection: 'row',
  },
  col: {
    flexDirection: 'column',
  },
  colCenter: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  rowCenter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  rowSpaceBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    alignItems: 'center',
  },
  rowSpaceAround: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  whiteTitle: {
    fontSize: fonts.size.h3,
    fontWeight: 'bold',
    color: 'white',
  },
  errorText: {
    fontSize: fonts.size.regular,
    fontWeight: '400',
    color: 'red',
    paddingVertical: metrics.spacing1,
  },
  mainText: {
    fontSize: fonts.size.regular,
    fontWeight: '500',
    color: colors['Grayiron/600'],
  },
  secondText: {
    fontSize: fonts.size.regular,
    fontWeight: '500',
    color: colors['Grayiron/400'],
  },
  subText: {
    fontSize: fonts.size.medium,
    fontWeight: '500',
    color: colors['Grayiron/400'],
  },
  priceText: {
    fontSize: fonts.size.regular,
    fontWeight: '500',
    color: colors['Moss/500'],
  },
  hintPriceText: {
    fontSize: fonts.size.regular,
    fontWeight: '400',
    color: colors['Grayiron/400'],
    textDecorationLine: 'line-through',
    marginRight: metrics.spacing1,
  },
  label: {
    fontSize: fonts.size.input,
    fontWeight: '500',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing2,
  },
  flex1: {
    flex: 1,
    backgroundColor: 'white',
  },
  gap4: {
    gap: metrics.spacing4
  },
  datePicker: {
    backgroundColor: 'white',
  },
});

export default commonStyles;
