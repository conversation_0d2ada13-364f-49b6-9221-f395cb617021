module.exports = {
  presets: ['module:@react-native/babel-preset'],
  plugins: [
    [
      'module-resolver',
      {
        root: ['./src'],
        alias: {
          components: './src/components',
          api: './src/api',
          assets: './src/assets',
          hooks: './src/hooks',
          navigation: './src/navigation',
          screens: './src/screens',
          utils: './src/utils',
          themes: './src/themes',
          stores: './src/stores',
          constants: './src/constants',
          contexts: './src/contexts', 
          types: './src/types'
        },
      },
    ],
    'react-native-reanimated/plugin',
  ],
};
