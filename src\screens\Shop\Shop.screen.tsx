import React, { useState, useEffect } from 'react';
import { SafeAreaView, StyleSheet, ScrollView, View, Text } from 'react-native';
import HomeHeader from 'components/Header/HomeHeader';
import ShopMenu from './components/ShopMenu';
import metrics from 'themes/metrics';
import Banner from './components/Banner';
import VerticalProducts from './components/VerticalProducts';
import { useProducts } from './hooks';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import SearchBar from '../Shop/components/SearchBar';

const Shop = () => {
  const { sort, products, setSortProducts, loading, searchProducts, keyword } = useProducts();
  const [isSearching, setIsSearching] = useState(false);

  // Đồng bộ isSearching với keyword
  useEffect(() => {
    setIsSearching(!!keyword);
  }, [keyword]);

  const handleSearch = (searchText: string) => {
    if (typeof searchProducts === 'function') {
      searchProducts(searchText.trim());
    } else {
      console.error('searchProducts is not a function:', searchProducts);
    }
  };

  return (
    <>
      <HomeHeader noStretch />
      <SafeAreaView style={styles.safeAreaView}>
        <ScrollView style={styles.outerWrapper}>
          <SearchBar 
            onSearch={handleSearch} 
            loading={loading}
            keyword={keyword}
            products={products}
            isSearching={isSearching}
          />
          
          {isSearching ? (
            loading ? (
              <Text style={styles.loadingText}>Đang tìm kiếm...</Text>
            ) : products.length === 0 ? (
              <Text style={styles.emptyText}>Không tìm thấy sản phẩm cho "{keyword}"</Text>
            ) : null
          ) : (
            <>
              <Banner products={products} />
              <ShopMenu id={null} />
              {loading ? (
                <Text style={styles.loadingText}>Đang tải sản phẩm...</Text>
              ) : (
                <VerticalProducts
                  sort={sort}
                  setSort={setSortProducts}
                  products={products}
                />
              )}
            </>
          )}
          
          <View style={{ height: 60 }} />
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: { flex: 1, backgroundColor: colors['Grayiron/50'] },
  outerWrapper: {
    flex: 1,
  },
  loadingText: {
    fontSize: fonts.size.h3,
    color: colors['Grayiron/600'],
    textAlign: 'center',
    marginTop: metrics.spacing4,
  },
  emptyText: {
    fontSize: fonts.size.h3,
    color: colors['Grayiron/600'],
    textAlign: 'center',
    marginTop: metrics.spacing4,
  },
});

export default Shop;