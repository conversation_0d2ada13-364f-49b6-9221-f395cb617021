import { useEffect, useState, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigation } from '@react-navigation/native';
import { patchAddress, createAddress, getAddressDetail } from 'api/addressBooks';
import cities from 'assets/city.json';

export const useAddressForm = (address, addressId) => {
  const navigation = useNavigation();
  const [fetchError, setFetchError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isNewAddress, setIsNewAddress] = useState(!address && !addressId); // Xác định tạo mới hay chỉnh sửa

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isDirty },
  } = useForm({
    defaultValues: {
      receiverName: '',
      receiverPhoneNumber: '',
      provinceId: 0,
      customerProvince: '',
      districtId: 0,
      customerDistrict: '',
      wardId: 0,
      customerWard: '',
      customerStreetAddress: '',
      isDefault: false,
    },
    mode: 'onChange',
  });

  useEffect(() => {
    const fetchAddress = async () => {
  
      const defaultCity = cities.find(city => Number(city.id) === 50);
      if (!address && !addressId && !defaultCity) {
        //setFetchError('Không tìm thấy tỉnh/thành phố với ID 79.');
        return;
      }

      if (addressId && !address) {
        // Lấy chi tiết địa chỉ để chỉnh sửa
        setIsLoading(true);
        try {
          const fetchedAddress = await getAddressDetail(addressId);
          console.log('Địa chỉ được lấy:', fetchedAddress);
          const formData = {
            ...fetchedAddress,
            provinceId: Number(fetchedAddress.provinceId) || 0,
            districtId: Number(fetchedAddress.districtId) || 0,
            wardId: Number(fetchedAddress.wardId) || 0,
            customerProvince: fetchedAddress.customerProvince || '',
            customerDistrict: fetchedAddress.customerDistrict || '',
            customerWard: fetchedAddress.customerWard || '',
          };
          reset(formData);
          console.log('Form state after reset (fetched):', formData);
          setIsNewAddress(false);
        } catch (error) {
          console.error('Fetch address error:', error);
          setFetchError(error instanceof Error ? error.message : 'Không thể tải địa chỉ. Vui lòng thử lại.');
        } finally {
          setIsLoading(false);
        }
      } else if (address) {
        // Khởi tạo form với dữ liệu địa chỉ cung cấp
        const formData = {
          ...address,
          provinceId: Number(address.provinceId) || 0,
          districtId: Number(address.districtId) || 0,
          wardId: Number(address.wardId) || 0,
          customerProvince: address.customerProvince || '',
          customerDistrict: address.customerDistrict || '',
          customerWard: address.customerWard || '',
        };
        reset(formData);
        console.log('Form state after reset (address prop):', formData);
        setIsNewAddress(false);
      } else {
        // Khởi tạo địa chỉ mới với tỉnh ID 79
        const defaultCityId = 50;
        const formData = {
          receiverName: '',
          receiverPhoneNumber: '',
          provinceId: defaultCityId,
          customerProvince: defaultCity?.name || '',
          districtId: 0,
          customerDistrict: '',
          wardId: 0,
          customerWard: '',
          customerStreetAddress: '',
          isDefault: false,
        };
        reset(formData);
        console.log('Form state after reset (default province 79):', formData);
        setIsNewAddress(true);
      }
    };

    fetchAddress();
  }, [addressId, address, reset]);

  // Đảm bảo giá trị được theo dõi là số
  const selectedProvince = Number(watch('provinceId'));
  const selectedDistrict = Number(watch('districtId'));

  // Reset các trường phụ thuộc khi parent thay đổi
  useEffect(() => {
    const prevProvinceRef = { current: selectedProvince };
    if (
      isDirty &&
      selectedProvince !== Number(address?.provinceId || 0) &&
      prevProvinceRef.current !== selectedProvince
    ) {
      prevProvinceRef.current = selectedProvince;
      setValue('districtId', 0);
      setValue('customerDistrict', '');
      setValue('wardId', 0);
      setValue('customerWard', '');
      console.log('Reset district and ward due to province change:', selectedProvince);
    }
  }, [selectedProvince, setValue, isDirty, address]);

  useEffect(() => {
    const prevDistrictRef = { current: selectedDistrict };
    if (
      isDirty &&
      selectedDistrict !== Number(address?.districtId || 0) &&
      prevDistrictRef.current !== selectedDistrict
    ) {
      prevDistrictRef.current = selectedDistrict;
      setValue('wardId', 0);
      setValue('customerWard', '');
      console.log('Reset ward due to district change:', selectedDistrict);
    }
  }, [selectedDistrict, setValue, isDirty, address]);

  const validateForm = useCallback((data) => {
    const errors = {};
    if (!data.receiverName?.trim()) {
      errors.receiverName = 'Vui lòng nhập họ và tên';
    }
    if (!data.receiverPhoneNumber || !/^\d{10}$/.test(data.receiverPhoneNumber)) {
      errors.receiverPhoneNumber = 'Số điện thoại không hợp lệ';
    }
    if (!data.provinceId) {
      errors.customerProvince = 'Vui lòng chọn tỉnh/thành phố';
    }
    if (!data.districtId) {
      errors.customerDistrict = 'Vui lòng chọn quận/huyện';
    }
    if (!data.wardId) {
      errors.customerWard = 'Vui lòng chọn phường/xã';
    }
    if (!data.customerStreetAddress?.trim()) {
      errors.customerStreetAddress = 'Vui lòng nhập địa chỉ cụ thể';
    }
    return errors;
  }, []);

  const onSubmit = useCallback(
    async (data) => {
      const formErrors = validateForm(data);
      if (Object.keys(formErrors).length > 0) {
        return { errors: formErrors };
      }

      try {
        const response = address?.id || addressId
          ? await patchAddress({ ...data, id: address?.id || addressId })
          : await createAddress(data);
        console.log('Address saved successfully:', response);
        navigation.goBack();
        return { success: true };
      } catch (error) {
        console.error('Submit address error:', error);
        return {
          error: error instanceof Error ? error.message : 'Đã có lỗi xảy ra. Vui lòng thử lại.',
        };
      }
    },
    [address, addressId, navigation, validateForm]
  );

  return {
    control,
    handleSubmit,
    watch,
    errors,
    selectedProvince,
    selectedDistrict,
    onSubmit: handleSubmit(onSubmit),
    isDirty,
    setValue,
    fetchError,
    isLoading,
    isNewAddress, // Trả về trạng thái mới
  };
};