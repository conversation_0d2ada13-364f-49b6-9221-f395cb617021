import React from 'react';
import { 
  StyleSheet, 
  Dimensions, 
  TouchableOpacity, 
  View, 
  Text,
  Animated 
} from 'react-native';
import FastImage from 'react-native-fast-image';
import SwiperFlatList from 'react-native-swiper-flatlist';
import LinearGradient from 'react-native-linear-gradient';
import colors from 'themes/colors';
import { useNavigation } from '@react-navigation/native';
import fonts from 'themes/fonts';
import metrics from 'themes/metrics';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const Banner = ({ products, autoplayDelay = 5000, autoplayLoop = true }) => {
  const navigation = useNavigation();
  const scrollX = React.useRef(new Animated.Value(0)).current;

  const handleProductPress = (product) => {
    navigation.navigate('ProductDetail', { id: product.id });
  };

  // Lấy 3 sản phẩm có discount cao nhất
  const getTopDiscountProducts = () => {
    if (!products || products.length === 0) return [];
    
    // Sắp xếp sản phẩm theo discount giảm dần
    const sortedProducts = [...products].sort((a, b) => {
      const discountA = a.discountPercent || 0;
      const discountB = b.discountPercent || 0;
      return discountB - discountA;
    });
    
    // Lấy 3 sản phẩm đầu tiên (có discount cao nhất)
    return sortedProducts.slice(0, 3);
  };

  const bannerProducts = getTopDiscountProducts();

  // Custom pagination dot (giữ nguyên phần này)
  const renderPagination = (index, total) => {
    return (
      <View style={styles.paginationContainer}>
        {Array.from({ length: total }).map((_, i) => {
          const opacity = scrollX.interpolate({
            inputRange: [
              (i - 1) * screenWidth,
              i * screenWidth,
              (i + 1) * screenWidth,
            ],
            outputRange: [0.3, 1, 0.3],
            extrapolate: 'clamp',
          });
          
          return (
            <Animated.View
              key={i}
              style={[
                styles.paginationDot,
                { opacity },
                index === i && styles.activeDot,
              ]}
            />
          );
        })}
      </View>
    );
  };

  // Phần render giữ nguyên
  return (
    <View style={styles.container}>
      <SwiperFlatList
        autoplay={autoplayLoop}
        autoplayDelay={autoplayDelay}
        autoplayLoopKeepAnimation
        showPagination
        PaginationComponent={({ index, total }) => renderPagination(index, total)}
        data={bannerProducts}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { x: scrollX } } }],
          { useNativeDriver: false }
        )}
        renderItem={({ item }) => (
          <TouchableOpacity 
            activeOpacity={0.9} 
            onPress={() => handleProductPress(item)}
            style={styles.slideContainer}
          >
            <FastImage
              style={styles.image}
              source={{ uri: item.mainImageUrl }}
              resizeMode={FastImage.resizeMode.cover}
            />
            
            {/* Gradient overlay */}
            <LinearGradient
              colors={['transparent', 'rgba(0,0,0,0.7)']}
              style={styles.gradient}
            />
            
            {/* Thông tin sản phẩm */}
            <View style={styles.productInfo}>
              <View style={styles.priceContainer}>
                {item.discountPercent > 0 && (
                  <View style={styles.originalPriceContainer}>
                    <Text style={styles.originalPrice}>
                      {item.price?.toLocaleString()}đ
                    </Text>
                    <View style={styles.discountBadge}>
                      <Text style={styles.discountText}>
                        -{item.discountPercent}%
                      </Text>
                    </View>
                  </View>
                )}
                <Text style={styles.finalPrice}>
                  {item.discountPercent > 0 
                    ? Math.round(item.price * (1 - item.discountPercent/100)).toLocaleString()
                    : item.price?.toLocaleString()
                  }đ
                </Text>
              </View>
              
              <Text style={styles.productName} numberOfLines={1}>
                {item.title}
              </Text>
            </View>
          </TouchableOpacity>
        )}
        keyExtractor={(item) => item.id.toString()}
      />
    </View>
  );
};

// Phần styles giữ nguyên
const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  slideContainer: {
    position: 'relative',
  },
  image: {
    width: screenWidth,
    height: screenHeight * 0.28,
  },
  gradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '40%',
  },
  productInfo: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 4,
  },
  finalPrice: {
    fontSize: fonts.size.h1,
    fontWeight: 'bold',
    color: colors.white,
    marginRight: 10,
  },
  originalPriceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 10,
  },
  originalPrice: {
    fontSize: fonts.size.medium,
    color: colors['Grayiron/300'],
    textDecorationLine: 'line-through',
    marginRight: 6,
  },
  discountBadge: {
    backgroundColor: colors['Danger/300'],
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  discountText: {
    fontSize: fonts.size.small,
    color: colors.white,
    fontWeight: 'bold',
  },
  productName: {
    fontSize: fonts.size.large,
    color: colors.white,
    fontWeight: '600',
  },
  paginationContainer: {
    position: 'absolute',
    bottom: 30,
    flexDirection: 'row',
    alignSelf: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.white,
    marginHorizontal: 4,
  },
  activeDot: {
    width: 20,
    backgroundColor: colors['Moss/400'],
  },
});

export default Banner;