import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import commonStyles from 'themes/commonStyles';
import colors from 'themes/colors';
import fonts from 'themes/fonts';
import metrics from 'themes/metrics';
import FastImage from 'react-native-fast-image';
import CommonButton from 'components/CommonButton';

function MerchantRow({ logo, title }: any) {
  return (
    <View style={styles.container}>
      <View style={commonStyles.row}>
        <FastImage style={styles.avatar} source={{ uri: logo }} />
        <View style={styles.col}>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.subTitle}>Online 8 phút trước</Text>
        </View>
      </View>
      <View style={styles.buttonContainer}>
        <CommonButton customStyles={styles.button} size="small">
          Xem shop
        </CommonButton>
        <CommonButton
          customStyles={styles.button}
          appearance="outline"
          size="small">
          Chat với shop
        </CommonButton>
      </View>
      {/* <View style={styles.buttonContainer}>

      </View> */}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: metrics.spacing4,
    marginBottom: metrics.spacing4,
  },
  buttonContainer: {
    ...commonStyles.rowSpaceBetween,
    gap: metrics.spacing4,
    marginTop: metrics.spacing2,
  },
  button: {
    flex: 1,
  },
  avatar: {
    marginRight: metrics.spacing2,
    width: 46,
    height: 46,
    borderRadius: 23,
    backgroundColor: 'red',
  },
  col: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  title: {
    color: colors['Grayiron/600'],
    fontSize: fonts.size.h2,
    fontWeight: '600',
  },
  subTitle: {
    color: colors['Grayiron/400'],
    fontSize: fonts.size.regular,
    fontWeight: '400',
  },
});

export default MerchantRow;
