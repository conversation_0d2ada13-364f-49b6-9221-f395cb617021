import { apis } from 'utils/axios';

/// Lấy giỏ hàng người dùng theo page & size
export const getUserCart = async (page: number, size: number) => {
  try {
    const response = await apis().get('/carts/user-carts', {
      params: { page, size },
    });
    console.log("in ra giá trị khi lấy giỏ hàng: ", response.data)
    return response?.data;
  } catch (e) {
    console.error('Lỗi khi lấy giỏ hàng:', e);
    return null;
  }
};

/// Lấy số lượng sản phẩm trong giỏ hàng
export const getCartItemCount = async () => {
  try {
    const response = await apis().get('/carts/item-count');
    console.log("Kết quả số lượng giỏ hàng", response.data);
    return response?.data;
  } catch (e) {
    console.error('Lỗi khi lấy giỏ hàng:', e);
    return null;
  }
};

/// Thêm sản phẩm vào giỏ
export const addToCart = async (item: any) => {
  try {
    const body = {
      productId: item?.id,
      merchantId: item?.merchantId,
      mainAttributeValueId: item?.mainAttributeValueId,
      secondAttributeValueId: item?.secondAttributeValueId,
      quantity: item?.quantity,
      ...(item?.variantId && { variantId: item.variantId }),
    };

    const response = await apis().post('/carts/add-item', body);
    console.log('Kết quả addToCart:', response.data);
    return response?.data;
  } catch (e) {
    console.error('Lỗi khi thêm sản phẩm vào giỏ:', e);
    throw e; // Throw error để component có thể catch và xử lý
  }
};

/// Cập nhật sản phẩm trong giỏ hàng
export const updateCartItem = async (body: {
  cartItemId: number;
  mainAttributeId: number;
  mainAttributeValueId: number;
  secondAttributeId: number;
  secondAttributeValueId: number;
  quantity: number;
}) => {
  try {
    const response = await apis().put('/carts/update-item', body);
    console.log("Kết quả updateCartItem:", response.data);
    return response?.data;
  } catch (e) {
    console.error('Lỗi khi cập nhật sản phẩm trong giỏ:', e);
    return null;
  }
};

/// Xoá sản phẩm khỏi giỏ hàng
export const deleteItemFromCart = async (cartItemId: number) => {
  try {
    const response = await apis().delete(`/carts/remove-item/${cartItemId}`);
    console.log("Kết quả deleteItemFromCart:", response.data);
    return response?.data;
  } catch (e) {
    console.error('Lỗi khi xoá sản phẩm khỏi giỏ:', e);
    return null;
  }
};

/// Chọn hoặc bỏ chọn tất cả sản phẩm trong giỏ
export const chooseAllItem = async (isChoose: boolean) => {
  try {
    const response = await apis().put(`/carts/update-choose/all/${isChoose}`);
    console.log("Kết quả chooseAllItem:", response.data);
    return response?.data;
  } catch (e) {
    console.error("Lỗi khi cập nhật trạng thái chọn tất cả:", e);
    return null;
  }
};

/// Chọn hoặc bỏ chọn sản phẩm theo merchantId
export const chooseItemByMerchantId = async (merchantId: number, isChoose: boolean) => {
  try {
    const response = await apis().put(`/carts/update-choose/by-merchant-id/${merchantId}/${isChoose}`);
    console.log("Kết quả chooseItemByMerchantId:", response.data);
    return response?.data;
  } catch (e) {
    console.error("Lỗi khi chọn sản phẩm theo merchant:", e);
    return null;
  }
};

/// Chọn hoặc bỏ chọn sản phẩm theo cartItemId
export const chooseItemByCartItemId = async (cartItemId: number, isChoose: boolean) => {
  try {
    const response = await apis().put(`/carts/update-choose/by-item-id/${cartItemId}/${isChoose}`);
    console.log("Kết quả chooseItemByCartItemId:", response.data);
    return response?.data;
  } catch (e) {
    console.error("Lỗi khi chọn sản phẩm theo cart item:", e);
    return null;
  }
};

/// Chọn hoặc bỏ chọn sản phẩm theo productId
export const chooseItemByProductId = async (productId: number, isChoose: boolean) => {
  try {
    const response = await apis().put(`/carts/update-choose/by-product-id/${productId}/${isChoose}`);
    console.log("Kết quả chooseItemByProductId:", response.data);
    return response?.data;
  } catch (e) {
    console.error("Lỗi khi chọn sản phẩm theo product:", e);
    return null;
  }
};
