/* eslint-disable react-hooks/exhaustive-deps */
import { getPostsByTopicId } from 'api/posts';
import { useEffect, useState } from 'react';

export const usePostsByTopicId = ({
  id,
  page = 0,
  size = 10,
}: {
  id: string | number;
  page?: string | number;
  size?: string | number;
}) => {
  const [loading, setLoading] = useState(false);
  const [posts, setPosts] = useState([]);

  const fetchPosts = async () => {
    try {
      setLoading(true);
      const response = await getPostsByTopicId({ id, page, size });
      setPosts(response?.posts as any);
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPosts();
  }, [id]);

  return {
    loading,
    posts,
    setPosts,
  };
};
