import React, { useEffect } from 'react';
import { SafeAreaView, StyleSheet, ScrollView, View, Text, ActivityIndicator } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import HomeHeader from 'components/Header/HomeHeader';
import metrics from 'themes/metrics';
import colors from 'themes/colors';
import fonts from 'themes/fonts';
import { useNavigation } from '@react-navigation/native';
import VerticalProducts from 'screens/Shop/components/VerticalProducts';
import { useFilterProducts } from './hooks';
import { useCategoriesMenu } from './components/ShopMenu/hooks';


const ProductFilter = () => {
  const navigation = useNavigation();
  const { products, filter, setFilter, loading, refetch } = useFilterProducts();
  const { categories: childCategories } = useCategoriesMenu({ id: filter.categoryId });
  const [dropdownValue, setDropdownValue] = React.useState<string | null>(
    filter.categoryId ? filter.categoryId.toString() : null
  );

  // Refresh data when screen comes into focus
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      refetch && refetch();
    });
    return unsubscribe;
  }, [navigation, refetch]);

  // Handle dropdown change for child categories
  const handleDropdownChange = (value: string) => {
    setDropdownValue(value);
    const categoryId = parseInt(value, 10);
    const selectedChild = childCategories.find((c: any) => c.id === categoryId);
    if (selectedChild) {
      setFilter({
        ...filter,
        categoryId: selectedChild.id,
        categoryName: selectedChild.name,
        keywords: '',
      });
      refetch && refetch();
    }
  };

  return (
    <View style={styles.container}>
      <HomeHeader
        noStretch
        backButton
        onBackPress={() => navigation?.goBack()}
        title={`Danh mục: ${filter.categoryName || 'Không xác định'}`}
      />
      
      <SafeAreaView style={styles.safeAreaView}>
        {/* Child Category Dropdown */}
        {filter.categoryId && childCategories?.length > 0 && (
          <View style={styles.dropdownContainer}>
            <Picker
              selectedValue={dropdownValue}
              onValueChange={handleDropdownChange}
              style={styles.picker}
            >
              <Picker.Item
                label={filter.categoryName || 'Chọn danh mục'}
                value={filter.categoryId.toString()}
              />
              {childCategories.map((child: any) => (
                <Picker.Item
                  key={`${child.id}-${child.name}`}
                  label={child.name}
                  value={child.id.toString()}
                />
              ))}
            </Picker>
          </View>
        )}

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors['Primary/200']} />
            <Text style={styles.loadingText}>Đang tải sản phẩm...</Text>
          </View>
        ) : (
          <ScrollView 
            style={styles.outerWrapper}
            contentContainerStyle={styles.contentContainer}
            showsVerticalScrollIndicator={false}
          >
            <VerticalProducts
              sort={filter?.sort}
              products={products}
              setSort={(sort) => setFilter({ ...filter, sort })}
            />
            
            {/* Bottom spacing */}
            <View style={styles.bottomSpace} />
          </ScrollView>
        )}
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white || '#FFFFFF',
  },
  safeAreaView: { 
    flex: 1 
  },
  outerWrapper: {
    flex: 1,
  },
  contentContainer: {
    paddingTop: metrics.spacing4,
  },
  dropdownContainer: {
    paddingHorizontal: metrics.spacing3,
    paddingVertical: metrics.spacing2,
    backgroundColor: colors['Gray/100'] || '#F5F5F5',
  },
  picker: {
    height: 50,
    backgroundColor: colors.white || '#FFFFFF',
    borderRadius: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: metrics.spacing3,
    fontSize: fonts.size.medium || 14,
    color: colors['Grayiron/500'] || '#666666',
  },
  bottomSpace: {
    height: 80,
  },
});

export default ProductFilter;