import React from 'react';
import { View, StyleSheet, Text, ScrollView } from 'react-native';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import Product from 'components/Product';
// import product from 'utils/product.json';
import { isEmpty, map } from 'lodash';
import { useNavigation } from '@react-navigation/native';
import { addRecentProducts, addToCart, setStorageCarts } from 'utils/cart';

type Props = {
  customStyles?: any;
  products?: any;
  cart: any;
  setCart: any;
};

const RecentProducts = ({ customStyles, products }: Props) => {
  // const { products } = useHomeProducts();
  const navigation = useNavigation();

  if (isEmpty(products)) {
    return <View />;
  }

  return (
    <View style={[styles.container, customStyles]}>
      <Text style={styles.title}>Sản phẩm vừa xem</Text>
      <ScrollView
        style={styles.products}
        horizontal
        bounces
        showsHorizontalScrollIndicator={false}>
        {map(products, (product: any) => (
          <View key={product?.id} style={styles.item}>
            <Product
              customStyles={{ maxWidth: metrics.fullWidth / 2 }}
              title={product?.title}
              uri={product?.mainImageUrl}
              price={product?.price}
              anchoPrice={product?.anchoPrice}
              rating={product?.rating}
              discountPercent={product?.discountPercent}
              onPress={() => {
                addRecentProducts(product);
                navigation?.navigate('ProductDetail', { id: product.id });
              }}
              onAddToCart={() => onAddToCart(product)}
            />
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
    marginTop: metrics.spacing4,
  },
  row: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexDirection: 'row',
    gap: metrics.spacing4,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
    paddingHorizontal: metrics.spacing4,
  },
  tag: {
    marginRight: metrics.spacing2,
  },
  tags: {
    marginBottom: metrics.spacing4,
    marginHorizontal: metrics.spacing4,
  },
  products: {
    paddingHorizontal: metrics.spacing4,
    paddingBottom: metrics.spacing4,
  },
  item: {
    marginRight: metrics.spacing4,
  },
});

export default RecentProducts;
