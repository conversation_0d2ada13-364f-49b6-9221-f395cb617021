import React from 'react';
import {
  ImageBackground,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import AntDesignIcon from 'react-native-vector-icons/AntDesign';
import SimpleLineIcon from 'react-native-vector-icons/SimpleLineIcons';
import { Text } from '@ui-kitten/components';
import FastImage from 'react-native-fast-image';

import CommonButton from 'components/CommonButton';
import { formatNumber } from 'utils/index';

import styles from './styles';
import Rating from 'components/Rating';

type Props = {
  border?: boolean;
  uri?: string;
  title: string;
  price?: string | number;
  anchoPrice?: string | number;
  author?: string;
  rating?: number;
  onPress?: any;
  customStyles?: ViewStyle;
  type?: 'horizontal' | 'full';
  onAddToCart?: any;
  discountPercent?: any
};

function Product({
  anchoPrice,
  price,
  uri,
  title,
  onPress,
  customStyles,
  border,
  rating,
  onAddToCart,
  discountPercent,
}: Props) {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.container, customStyles, border && styles.border]}>
      <FastImage
        // fallback={require('assets/images/product-fallback-image.png')}
        style={styles.postImage}
        source={
          uri ? { uri } : require('assets/images/product-fallback-image.png')
        }
      />
      <ImageBackground
        imageStyle={styles.badgeImage}
        style={styles.badgeContainer}
        source={require('assets/images/label.png')}>
        <Text style={styles.badgeTitle}>
          <SimpleLineIcon name="fire" size={10} color="white" /> {`${formatNumber(discountPercent)}%`}
        </Text>
      </ImageBackground>
      <View style={styles.content}>
        <Text style={styles.title} status="primary" numberOfLines={2}>
          {title}
        </Text>
        <Rating customStyles={styles.marginBottom} value={rating} />
        <View style={styles.rowCenter}>
          <Text style={styles.price} status="primary" numberOfLines={2}>
            {`${formatNumber(price)}đ`}
          </Text>
          <Text style={styles.anchoPrice} status="primary" numberOfLines={2}>
            {`${formatNumber(anchoPrice)}đ`}
          </Text>
        </View>

        <CommonButton
          onPress={onAddToCart}
          customStyles={styles.button}
          size="small"
          rounded
          accessoryRight={
            <AntDesignIcon name="shoppingcart" size={16} color="white" />
          }>
          Thêm vào giỏ
        </CommonButton>
      </View>
    </TouchableOpacity>
  );
}

export default Product;
