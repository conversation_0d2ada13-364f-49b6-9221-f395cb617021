import React from 'react';
import { ViewStyle } from 'react-native';
import { Button, ButtonProps } from '@ui-kitten/components';

import styles from 'components/CommonButton/styles';
import metrics from 'themes/metrics';

type Props = {
  disabled?: boolean;
  loading?: boolean;
  onPress?: any;
  customStyles?: ViewStyle;
  rounded?: boolean;
};

function CommonButton({
  rounded,
  disabled,
  loading,
  onPress,
  children,
  customStyles,
  ...props
}: Props & ButtonProps) {
  return (
    <Button
      {...(props as ButtonProps)}
      // appearance="outline"
      style={[
        { marginVertical: metrics.spacing1 },
        rounded && styles.rounded,
        customStyles,
      ]}
      disabled={loading}
      onPress={!disabled && !loading && onPress}>
      {children}
    </Button>
  );
}

export default CommonButton;
