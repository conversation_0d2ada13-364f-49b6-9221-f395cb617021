import React from 'react';
import { SafeAreaView, StyleSheet, ScrollView } from 'react-native';
import HTML from 'components/HTML';
import metrics from 'themes/metrics';
import HomeHeader from 'components/Header/HomeHeader';

interface WebviewScreenProps {
  route: { params?: { uri?: string; title?: string } };
}

const WebviewScreen: React.FC<WebviewScreenProps> = ({ route }) => {
  const uri = route?.params?.uri || 'https://hathyo.com'; // URL dự phòng
  const title = route?.params?.title || 'Tiêu đề mặc định';
  console.log('WebviewScreen params:', { uri, title });

  return (
    <>
      <HomeHeader rightComponent={null} title={title} backButton noStretch />
      <SafeAreaView style={styles.safeAreaView}>
        <ScrollView style={styles.outerWrapper}>
          <HTML uri={uri} />
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: { flex: 1 },
  outerWrapper: {
    flex: 1,
  },
});

export default WebviewScreen;