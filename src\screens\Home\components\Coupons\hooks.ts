import { useEffect, useState } from 'react';
import { getCollectedCoupons } from 'api/coupons';
import { Coupon } from 'types/coupon';

export const useCoupons = (
  priceCart: number = 0,
  type: string = 'HATHYO',
  page: number = 0,
  size: number = 5
) => {
  const [loading, setLoading] = useState(false);
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [error, setError] = useState<string | null>(null);

  const fetchCoupons = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getCollectedCoupons(priceCart, type, page, size);
      console.log('Parsed response data:', response); // Log the full response
      const fetchedCoupons = response?.coupons || []; // Adjust based on actual response structure
      console.log('Fetched coupons:', fetchedCoupons); // Log the coupons being set
      setCoupons(fetchedCoupons);
    } catch (err: any) {
      const errorMessage = err.message || 'Không thể tải danh sách coupon.';
      console.error('Error in fetchCoupons:', errorMessage);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCoupons();
  }, [priceCart, type, page, size]);

  return {
    loading,
    coupons,
    error,
    fetchCoupons,
  };
};