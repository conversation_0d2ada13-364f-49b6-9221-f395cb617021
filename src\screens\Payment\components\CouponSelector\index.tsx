import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Image, ActivityIndicator } from 'react-native';
import { Text, Button, Divider } from '@ui-kitten/components';
import { useNavigation } from '@react-navigation/native';
import metrics from 'themes/metrics';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import { Coupon } from '../../type/Coupon';

interface CouponSelectorProps {
  coupons: Coupon[];
  selectedCoupon: Coupon | null;
  onSelectCoupon: (coupon: Coupon | null) => void;
  totalPrice?: number;
}

const CouponSelector: React.FC<CouponSelectorProps> = ({ coupons, selectedCoupon, onSelectCoupon, totalPrice = 0 }) => {
  const navigation = useNavigation();
  const [loadingCouponId, setLoadingCouponId] = useState<number | null>(null);

  if (coupons.length === 0) {
    return null;
  }

  const sortedCoupons = [...coupons].sort((a, b) => {
    const aPercent = a.discountType === 'PERCENT' ? a.discountPercent ?? 0 : 0;
    const bPercent = b.discountType === 'PERCENT' ? b.discountPercent ?? 0 : 0;
    const aApplicable = a.minimumPriceApply <= totalPrice;
    const bApplicable = b.minimumPriceApply <= totalPrice;

    if (aApplicable && !bApplicable) return -1;
    if (!aApplicable && bApplicable) return 1;
    return bPercent - aPercent;
  });

  const handleCouponPress = async (coupon: Coupon) => {
    setLoadingCouponId(coupon.id);
    try {
      const newCoupon = selectedCoupon?.id === coupon.id ? null : coupon;
      await onSelectCoupon(newCoupon);
    } finally {
      setLoadingCouponId(null);
    }
  };

  const navigateToCoupons = () => {
    navigation.navigate('CouponsScreen');
  };

  const formatDiscount = (coupon: Coupon) => {
    if (coupon.discountType === 'PERCENT' && coupon.discountPercent) {
      return `${coupon.discountPercent}%`;
    }
    if (coupon.discountType === 'FIXED' && coupon.discountValue) {
      return `${coupon.discountValue.toLocaleString('vi-VN')} VNĐ`;
    }
    return 'N/A';
  };

  const formatExpiration = (expiredAt: string) => {
    const date = new Date(expiredAt);
    return date.toLocaleDateString('vi-VN');
  };

  const getButtonText = (coupon: Coupon) => {
    const isSelected = selectedCoupon?.id === coupon.id;
    const isApplicable = coupon.minimumPriceApply <= totalPrice;
    const isApplied = coupon.applyStatus === true;

    if (isSelected) return 'Hủy';
    if (!isApplicable) return 'Chưa đạt yêu cầu';
    if (isApplied) return 'Đã áp dụng';
    return 'Áp dụng';
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text category="s1" style={[commonStyles.mainText, styles.title]}>
          Mã giảm giá
        </Text>
        <TouchableOpacity onPress={navigateToCoupons}>
          <Text style={styles.couponLink}>Tìm mã giảm giá</Text>
        </TouchableOpacity>
      </View>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.couponList}>
        {sortedCoupons.map((coupon) => {
          const isSelected = selectedCoupon?.id === coupon.id;
          const isApplicable = coupon.minimumPriceApply <= totalPrice;
          const isApplied = coupon.applyStatus === true;

          return (
            <TouchableOpacity
              key={coupon.id}
              style={[styles.couponItem, isSelected && styles.selectedCoupon, !isApplicable && styles.disabledCoupon]}
              onPress={() => isApplicable && !isApplied && handleCouponPress(coupon)}
              disabled={!isApplicable || isApplied || loadingCouponId !== null}
            >
              <View style={styles.couponContent}>
                <Image
                  source={{ uri: coupon.image || 'https://via.placeholder.com/40' }}
                  style={styles.couponImage}
                  resizeMode="contain"
                />
                <View style={styles.couponDetails}>
                  <Text style={styles.couponTitle} numberOfLines={1}>
                    {coupon.title}
                  </Text>
                  <Text style={styles.couponDescription} numberOfLines={1}>
                    {coupon.description}
                  </Text>
                  <View style={styles.couponInfo}>
                    <Text style={styles.couponDiscount}>Giảm: {formatDiscount(coupon)}</Text>
                    <Text style={styles.couponExpiration}>
                      Hết hạn: {formatExpiration(coupon.expiredAt)}
                    </Text>
                  </View>
                </View>
              </View>
              {loadingCouponId === coupon.id ? (
                <ActivityIndicator size="small" color={colors['Primary/300']} style={styles.couponButton} />
              ) : (
                <Button
                  style={[
                    styles.couponButton,
                    isSelected && styles.deselectButton,
                    (!isApplicable || isApplied) && styles.disabledButton,
                  ]}
                  size="tiny"
                  onPress={() => isApplicable && !isApplied && handleCouponPress(coupon)}
                  disabled={!isApplicable || isApplied || loadingCouponId !== null}
                >
                  {getButtonText(coupon)}
                </Button>
              )}
            </TouchableOpacity>
          );
        })}
      </ScrollView>
      <Divider style={styles.divider} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    padding: metrics.spacing2,
    borderRadius: metrics.radius2,
    marginHorizontal: metrics.spacing2,
    marginVertical: metrics.spacing1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: metrics.spacing1,
  },
  title: {
    fontWeight: '600',
    fontSize: 16,
  },
  couponLink: {
    color: colors['Primary/300'],
    fontSize: 13,
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  couponList: {
    flexGrow: 0,
  },
  divider: {
    marginVertical: metrics.spacing1,
    backgroundColor: colors['Grayiron/200'],
  },
  couponItem: {
    backgroundColor: 'white',
    borderRadius: metrics.radius2,
    marginHorizontal: metrics.spacing1,
    padding: metrics.spacing1,
    width: 240,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  disabledCoupon: {
    opacity: 0.6,
  },
  selectedCoupon: {
    borderWidth: 1.5,
    borderColor: colors['Primary/300'],
  },
  couponContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  couponImage: {
    width: 40,
    height: 40,
    borderRadius: metrics.radius1,
    marginRight: metrics.spacing1,
  },
  couponDetails: {
    flex: 1,
  },
  couponTitle: {
    fontSize: 13,
    fontWeight: '600',
    color: colors['Grayiron/700'],
    marginBottom: metrics.spacing0_5,
  },
  couponDescription: {
    fontSize: 11,
    color: colors['Grayiron/500'],
    marginBottom: metrics.spacing0_5,
  },
  couponInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  couponDiscount: {
    fontSize: 11,
    color: colors['Primary/300'],
    fontWeight: '600',
  },
  couponExpiration: {
    fontSize: 10,
    color: colors['Grayiron/400'],
  },
  couponButton: {
    backgroundColor: colors['Primary/300'],
    borderColor: colors['Primary/300'],
    borderRadius: metrics.radius2,
    marginTop: metrics.spacing1,
    paddingHorizontal: metrics.spacing1,
  },
  deselectButton: {
    backgroundColor: colors['Danger/300'],
    borderColor: colors['Danger/300'],
  },
  disabledButton: {
    backgroundColor: colors['Grayiron/300'],
    borderColor: colors['Grayiron/300'],
  },
});

export default CouponSelector;