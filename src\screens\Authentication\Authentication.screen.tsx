import React from 'react';
import { Safe<PERSON>reaView, StyleSheet, ScrollView, View } from 'react-native';

import metrics from 'themes/metrics';
import LoginForm from './components/LoginForm';
import HomeHeader from 'components/Header/HomeHeader';
import FastImage from 'react-native-fast-image';
import commonStyles from 'themes/commonStyles';
import HeaderIcon from 'components/Header/HeaderIcon';
import { useNavigation } from '@react-navigation/native';
import { useAuthentication } from './hooks';
import { AUTHENTICATION_STEP } from '../../constants/authentication';
import RegisterForm from './components/RegisterForm';
import RegisterOTPForm from './components/RegisterOTPForm';
import RegisterCreatePassForm from './components/RegisterCreatePassForm';
import ForgotPassForm from './components/ForgotPassForm';
import ResetPasswordForm from './components/ResetPasswordForm';

const Content = () => {
  const { step, currentData, setCurrentData, setStep } = useAuthentication();

  switch (step) {
    case AUTHENTICATION_STEP.LOGIN:
      return <LoginForm {...{ setStep }} />;

    case AUTHENTICATION_STEP.REGISTER:
      return <RegisterForm {...{ setStep, setCurrentData, currentData }} />;
    case AUTHENTICATION_STEP.REGISTER_OTP:
      return <RegisterOTPForm {...{ setStep, setCurrentData, currentData }} />;
    case AUTHENTICATION_STEP.REGISTER_CREATE_PASS:
      return (
        <RegisterCreatePassForm {...{ setStep, setCurrentData, currentData }} />
      );
    case AUTHENTICATION_STEP.FORGOT_PASS:
      return <ForgotPassForm {...{ setStep, setCurrentData, currentData }} />;
    case AUTHENTICATION_STEP.FORGOT_PASS_CREATE_NEW_PASS:
      return (
        <ResetPasswordForm {...{ setStep, setCurrentData, currentData }} />
      );

    default:
      return <LoginForm {...{ setStep }} />;
  }
};

const Authentication = () => {
  const navigation = useNavigation();

  return (
    <>
      <HomeHeader
        rightComponent={
          <HeaderIcon iconName="close" onPress={() => navigation?.goBack()} />
        }
      />
      <SafeAreaView style={styles.safeAreaView}>
        <ScrollView style={styles.outerWrapper}>
          <View style={commonStyles.rowCenter}>
            <View style={styles.logoContainer}>
              <FastImage
                style={styles.image}
                source={require('assets/images/logo.png')}
                resizeMode={FastImage.resizeMode.contain} // Ensure image is not cropped
              />
            </View>
          </View>
          <Content />
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: { flex: 1 },
  outerWrapper: {
    flex: 1,
    paddingTop: metrics.spacing4,
    marginTop: -200,
  },
  logoContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)', 
    borderRadius: metrics.radius2 + 8, 
    padding: 8, 
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.7,
    shadowRadius: 5,
    elevation: 2,
  },
  image: {
    width: metrics.fullWidth / 2, 
    height: metrics.fullWidth / 6, 
    borderRadius: metrics.radius2,
  },
});

export default Authentication;