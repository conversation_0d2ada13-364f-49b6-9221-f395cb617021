/* eslint-disable react-hooks/exhaustive-deps */
import { useCallback, useEffect, useRef, useState } from 'react';
import { getDataSystem } from 'api/system';

export type SystemData = {
  soLuongTruyCap: number;
  soNguoiBan: number;
  soNguoiBanMoi: number;
  tongSoSanPham: number;
  soSanPhamMoi: number;
  soLuongGiaoDich: number;
  tongSoDonHangThanhCong: number;
  tongSoDonHangKhongThanhCong: number;
  tongGiaTriGiaoDich: number;
};

export const useSystemReport = () => {
  const [systemData, setSystemData] = useState<SystemData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const isMounted = useRef(true);

  const transformSystemData = (data: any): SystemData | null => {
    try {
      if (
        !data ||
        typeof data.soLuongTruyCap !== 'number' ||
        typeof data.soNguoiBan !== 'number' ||
        typeof data.soNguoiBanMoi !== 'number' ||
        typeof data.tongSoSanPham !== 'number' ||
        typeof data.soSanPhamMoi !== 'number' ||
        typeof data.soLuongGiaoDich !== 'number' ||
        typeof data.tongSoDonHangThanhCong !== 'number' ||
        typeof data.tongSoDonHangKhongThanhCong !== 'number' ||
        typeof data.tongGiaTriGiaoDich !== 'number'
      ) {
        console.error('Invalid system data:', data);
        return null;
      }
      return {
        soLuongTruyCap: data.soLuongTruyCap,
        soNguoiBan: data.soNguoiBan,
        soNguoiBanMoi: data.soNguoiBanMoi,
        tongSoSanPham: data.tongSoSanPham,
        soSanPhamMoi: data.soSanPhamMoi,
        soLuongGiaoDich: data.soLuongGiaoDich,
        tongSoDonHangThanhCong: data.tongSoDonHangThanhCong,
        tongSoDonHangKhongThanhCong: data.tongSoDonHangKhongThanhCong,
        tongGiaTriGiaoDich: data.tongGiaTriGiaoDich,
      };
    } catch (err) {
      console.warn('Invalid system data:', data, err);
      return null;
    }
  };

  const fetchSystemData = useCallback(async () => {
    if (!isMounted.current) return;
    try {
      setLoading(true);
      setError(null);
      console.log('Fetching system report data');
      const response = await getDataSystem();
      if (!isMounted.current) return;
      if (response instanceof Error) {
        throw response;
      }
      const transformedData = transformSystemData(response);
      if (!transformedData) {
        throw new Error('Dữ liệu hệ thống không hợp lệ.');
      }
      setSystemData(transformedData);
    } catch (err: any) {
      if (!isMounted.current) return;
      const errorMessage =
        err.response?.data?.message ||
        (err.message.includes('Access denied')
          ? 'Không có quyền truy cập báo cáo. Vui lòng đăng nhập lại.'
          : 'Không thể tải dữ liệu báo cáo hệ thống. Vui lòng thử lại.');
      console.error('Error fetching system data:', err.message, err.response?.data);
      setError(errorMessage);
    } finally {
      if (isMounted.current) setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchSystemData();
    return () => {
      isMounted.current = false;
    };
  }, [fetchSystemData]);

  return {
    systemData,
    loading,
    error,
    fetchSystemData,
  };
};