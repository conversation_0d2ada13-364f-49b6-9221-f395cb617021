// ./hooks/useProducts.js
import { getFilterProducts } from 'api/products';
import { useEffect, useState, useCallback } from 'react';
import { useProductsStore } from 'stores/products';
import { PRODUCT_SORT } from 'themes/constant';

export const useProducts = () => {
  const [loading, setLoading] = useState(false);
  const [sort, setSort] = useState(PRODUCT_SORT.createdAsc.key);
  const [products, setProducts] = useState([]);
  const [keyword, setKeyword] = useState('');

  const filter = useProductsStore((state) => state?.filter);
  const setFilter = useProductsStore((state) => state?.setFilter);
  
  const setSortProducts = (value) => {
    setSort(value);
    setFilter({ ...filter, sort: value });
  };

  // Sử dụng useCallback để đảm bảo hàm không bị tạo lại mỗi khi render
  const searchProducts = useCallback((searchKeyword) => {
    console.log('Searching for:', searchKeyword);
    setKeyword(searchKeyword);
    setFilter({ ...filter, keywords: searchKeyword });
  }, [filter, setFilter]);

  const fetchPosts = async () => {
    try {
      setLoading(true);
      const response = await getFilterProducts({
        keywords: keyword,
        categoryId: null,
        sort,
      });
      console.log('API response:', response);
      const productList = response?.products || [];
      if (Array.isArray(productList)) {
        setProducts(productList);
      } else {
        console.warn('Products is not an array:', productList);
        setProducts([]);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPosts();
  }, [sort, keyword]);

  return {
    loading,
    products,
    sort,
    keyword,  // Đảm bảo trả về keyword
    setProducts,
    setSortProducts,
    searchProducts,  // Trả về searchProducts để sử dụng trong Shop component
  };
};