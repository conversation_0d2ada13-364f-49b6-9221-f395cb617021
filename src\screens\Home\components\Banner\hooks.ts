/* eslint-disable react-hooks/exhaustive-deps */
import { getQuotes } from 'api/quotes';
import { useEffect, useState, useCallback } from 'react';

export const useQuotes = () => {
  const [loading, setLoading] = useState(false);
  const [quotes, setQuotes] = useState({});

  const fetchQuotes = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getQuotes();
      setQuotes(response as any);
    } catch (error) {
      console.error('Error fetching quotes:', error);
      // Set fallback quote on error
      setQuotes({
        quote: 'The only way to do great work is to love what you do.',
        author: '<PERSON> Jobs'
      });
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshQuote = useCallback(async () => {
    await fetchQuotes();
  }, [fetchQuotes]);

  useEffect(() => {
    fetchQuotes();
  }, [fetchQuotes]);

  return {
    loading,
    setQuotes,
    quotes,
    refreshQuote, // New method to refresh quotes
  };
};