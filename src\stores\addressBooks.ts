import AsyncStorage from '@react-native-async-storage/async-storage';
import { createAddress, listAddress, patchAddress } from 'api/addressBooks';
import { getUser, patchUser } from 'api/users';
import { STORAGE } from 'constants/authentication';
import Toast from 'react-native-toast-message';
import { create } from 'zustand';

export const useAddressBooks = create(set => ({
  addressBooks: [],
  loading: false,
  fetchAddresses: async () => {
    set({ loading: true });
    try {
      const token = await AsyncStorage.getItem(STORAGE.ACCESS_TOKEN);
      if (token) {
        try {
          const response = await listAddress();
          set({ user: response });
        } catch (error) {
          console.log(error);
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      set({ loading: false });
    }
  },
  updateAddress: async (body: any) => {
    try {
      set({ loading: true });
      const response = await patchAddress(body);
      if (response?.code) {
        Toast.show({
          type: 'error',
          text1: response?.message || 'Có lỗi xảy ra, vui lòng thử lại',
        });
      } else {
        Toast.show({
          type: 'success',
          text1: 'Cập nhật thông tin thành công',
        });
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: error?.message || 'Có lỗi xảy ra, vui lòng thử lại',
      });
    } finally {
      set({ loading: false });
    }
  },
  createAddress: async (body: any) => {
    try {
      set({ loading: true });
      const response = await createAddress(body);
      if (response?.code) {
        Toast.show({
          type: 'error',
          text1: response?.message || 'Có lỗi xảy ra, vui lòng thử lại',
        });
      } else {
        Toast.show({
          type: 'success',
          text1: 'Cập nhật thông tin thành công',
        });
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: error?.message || 'Có lỗi xảy ra, vui lòng thử lại',
      });
    } finally {
      set({ loading: false });
    }
  },
}));
