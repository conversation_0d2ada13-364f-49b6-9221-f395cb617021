import { useEffect, useState } from 'react';
import { getProducts } from 'api/products';

export const useGetProducts = ({ size = 6 }: { size?: number } = {}) => {
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState([]);
  const [error, setError] = useState(null);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getProducts();
      setProducts(response.products || response);
    } catch (err) {
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, [size]);

  return {
    loading,
    products,
    error,
    refetch: fetchProducts,
  };
};