import React from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import Tag from 'components/Tag';
import CommonButton from 'components/CommonButton';
import { Address } from 'types/address';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface AddressItemsProps {
  addresses: Address[];
  loading: boolean;
  onItemPress: (address: Address) => void;
  onAddNewPress: () => void;
  onDeletePress: (id: string) => void;
}

interface ItemProps extends Address {
  onPress: () => void;
  onDelete: () => void;
}

const Item = React.memo(
  ({
    onPress,
    onDelete,
    receiverName,
    receiverPhoneNumber,
    customerStreetAddress,
    customerWard,
    customerDistrict,
    customerProvince,
    isDefault,
  }: ItemProps) => {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.9} style={styles.cardContainer}>
        <View style={styles.cardContent}>
          <View style={styles.headerRow}>
            <View style={styles.namePhoneContainer}>
              <View style={styles.nameTagRow}>
                <Text style={styles.nameText}>{receiverName}</Text>
                {isDefault && (
                  <View style={styles.inlineTag}>
                    <Tag size="small" title="Mặc định" type="warning" />
                  </View>
                )}
              </View>
              <Text style={styles.phoneText}>{receiverPhoneNumber}</Text>
            </View>
            <TouchableOpacity onPress={onDelete} style={styles.deleteWrapper}>
              <Icon name="delete" size={22} color={colors.red} />
            </TouchableOpacity>
          </View>

          <Text style={styles.addressText} numberOfLines={2}>
            {`${customerStreetAddress}, ${customerWard}, ${customerDistrict}, ${customerProvince}`}
          </Text>
        </View>
      </TouchableOpacity>
    );
  }
);

const AddressItems = ({
  addresses = [],
  loading,
  onItemPress,
  onAddNewPress,
  onDeletePress,
}: AddressItemsProps) => {
  return (
   
      <View style={styles.content}>
        {loading && addresses.length > 0 && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" />
          </View>
        )}
        {addresses.length > 0 ? (
          addresses.map((item) => (
            <Item
              key={item.id}
              {...item}
              onPress={() => onItemPress(item)}
              onDelete={() => onDeletePress(item.id)}
            />
          ))
        ) : (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>Bạn chưa có địa chỉ nào</Text>
          </View>
        )}

        <CommonButton onPress={onAddNewPress} 
        customStyles={{
            backgroundColor: colors['Moss/500'] || '#4CAF50',
            borderColor: colors['Moss/500'] || '#4CAF50',
          }}
           style={styles.addButton}>
          
          Thêm địa chỉ mới
        </CommonButton>
      </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: metrics.spacing4 + 2,
    paddingBottom: metrics.spacing2 + 2,
  },
  title: {
    fontSize: fonts.size.h2,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing2 + 1,
    marginLeft: metrics.spacing4,
  },
  content: {
    paddingVertical: metrics.spacing2 + 2,
    backgroundColor: colors.white,
    paddingHorizontal: metrics.spacing2 + 2,
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: metrics.spacing2 + 2,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: metrics.spacing4 + 2,
  },
  emptyText: {
    ...commonStyles.subText,
    marginBottom: metrics.spacing3 + 1,
  },
  addButton: {
    marginTop: metrics.spacing2 + 2,
  },
  cardContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: metrics.spacing2 + 2,
    marginBottom: metrics.spacing2 + 2,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cardContent: {
    flexDirection: 'column',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: metrics.spacing1 + 1,
  },
  namePhoneContainer: {
    flex: 1,
  },
  nameTagRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  nameText: {
    fontSize: fonts.size.medium + 3,
    fontWeight: '600',
    color: colors['Primary/400'],
    marginRight: 8,
  },
  phoneText: {
    fontSize: fonts.size.medium + 2,
    color: colors['Primary/300'],
  },
  addressText: {
    fontSize: fonts.size.small + 3,
    color: colors['Primary/300'],
    marginTop: metrics.spacing1,
    marginBottom: metrics.spacing1,
  },
  inlineTag: {
    paddingLeft: 4,
  },
  deleteWrapper: {
    padding: 6,
    borderRadius: 20,
    backgroundColor: colors['Grayiron/200'],
  },
});

export default AddressItems;
