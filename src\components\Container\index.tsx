import React from 'react';
import {
  ImageBackground,
  StatusBar,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import Header from 'components/Header';

type Props = {
  children?: React.ReactElement;
};

function Container({ children }: Props) {
  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor="red" />
      <ImageBackground source={require('assets/images/header-background.png')}>
        <SafeAreaView style={styles.SafeAreaView2}>{children}</SafeAreaView>
        <Header backButton />
      </ImageBackground>
      <SafeAreaView style={styles.SafeAreaView2}>{children}</SafeAreaView>
    </>
  );
}

const styles = StyleSheet.create({
  SafeAreaView2: { flex: 1 },
  SafeAreaView1: { flex: 0 },
});

export default Container;
