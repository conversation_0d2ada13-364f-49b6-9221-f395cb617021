import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { useForm } from 'react-hook-form';
import { map } from 'lodash';
import Toast from 'react-native-toast-message';
import { yupResolver } from '@hookform/resolvers/yup';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import * as yup from 'yup';

import CommonButton from 'components/CommonButton';
import {
  AUTHENTICATION_STEP,
  REGEX_INPUT,
  STORAGE,
} from 'constants/authentication';
import FormInput from 'components/FormInput';
import { signIn } from 'api/users';
import { useUserStore } from 'stores/user';
import { useCart } from 'hooks/contexts/CartContext';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';

type RootStackParamList = {
  AppTab: undefined;
  SystemReport: undefined;
  Authentication: undefined;
};

type NavigationProps = NativeStackNavigationProp<RootStackParamList>;

const schema = yup.object({
  email: yup
    .string()
    .required('Vui lòng nhập email')
    .trim()
    .matches(REGEX_INPUT.EMAIL, 'Email chưa đúng định dạng'),
  password: yup.string().required('Vui lòng nhập mật khẩu'),
});

const LoginForm: React.FC<{ setStep: (step: string) => void }> = ({ setStep }) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm({
    defaultValues: {
      email: '<EMAIL>',
      password: '12345678Aa',
    },
    resolver: yupResolver(schema),
  });
  const fetchUser = useUserStore((state: any) => state?.fetchUser);
  const navigation = useNavigation<NavigationProps>();
  const { fetchCartCount } = useCart();

  const onSubmit = async (values: any) => {
    const response = await signIn({ body: values });

    
    await AsyncStorage.multiRemove([
      STORAGE.ACCESS_TOKEN,
      STORAGE.REFRESH_TOKEN,
      STORAGE.EMAIL
    ]);

    if (response?.code || !response?.accessToken) {
      if (response?.validationErrors) {
        map(response?.validationErrors, (item): any => {
          setError(item?.field, {
            message: item.message,
          });
        });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Đăng nhập thất bại',
          text2: 'Có lỗi xảy ra trong quá trình đăng nhập',
          position: 'top',
        });
      }
    } else {
      await AsyncStorage.setItem(STORAGE.ACCESS_TOKEN, response?.accessToken);
      await AsyncStorage.setItem(STORAGE.REFRESH_TOKEN, response?.refreshToken);
      
      await fetchCartCount();
      fetchUser();
      if (values.email.trim().toLowerCase() === '<EMAIL>') {
         await AsyncStorage.setItem(STORAGE.EMAIL, '<EMAIL>');
        navigation.reset({
          index: 0,
          routes: [{ name: 'SystemReportScreen' }],
        });
      } else {
        navigation.reset({
          index: 0,
          routes: [{ name: 'AppTab' }],
        });
      }
      Toast.show({
        type: 'success',
        text1: 'Đăng nhập thành công',
        position: 'top',
      });
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View>
          <Text style={styles.title}>Chào mừng bạn đến với Hathyo.com</Text>
        </View>
        <View>
          <Text style={styles.subTitle}>Đăng nhập</Text>
        </View>
        <View style={styles.item}>
          <FormInput
            label="Email"
            control={control}
            required
            placeholder="Nhập email"
            name="email"
            errors={errors?.email}
          />
        </View>
        <View style={styles.item}>
          <FormInput
            label="Mật khẩu"
            control={control}
            required
            placeholder="Nhập mật khẩu"
            name="password"
            errors={errors?.password}
            password
          />
          <TouchableOpacity
            style={styles.flexRight}
            onPress={() => setStep(AUTHENTICATION_STEP.FORGOT_PASS)}
          >
            <Text style={styles.text}>Quên mật khẩu ?</Text>
          </TouchableOpacity>
        </View>
        <View style={commonStyles.rowCenter}>
          <CommonButton
            onPress={handleSubmit(onSubmit)}
            customStyles={{ backgroundColor: colors['Moss/500'] }}
          >
            Đăng nhập
          </CommonButton>
        </View>
      </View>
      <View style={commonStyles.rowCenter}>
        <TouchableOpacity onPress={() => setStep(AUTHENTICATION_STEP.REGISTER)}>
          <Text style={styles.text}>
            Chưa có tài khoản ?
            <Text style={styles.registerText}> Đăng ký ngay</Text>
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  content: {
    padding: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
    marginBottom: metrics.spacing4,
  },
  item: {
    marginBottom: metrics.spacing4,
  },
  subTitle: {
    fontSize: fonts.size.h2,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  title: {
    fontSize: fonts.size.h1,
    fontWeight: '600',
    color: colors['Moss/500'],
    marginBottom: metrics.spacing4,
  },
  text: {
    fontSize: fonts.size.input,
    fontWeight: '400',
    color: colors['Grayiron/400'],
    marginBottom: metrics.spacing4,
  },
  registerText: {
    fontSize: fonts.size.input,
    fontWeight: '500',
    color: colors['Moss/500'],
    marginBottom: metrics.spacing4,
  },
  flexRight: {
    flex: 1,
    alignItems: 'flex-end',
    marginTop: metrics.spacing3,
  },
});

export default LoginForm;