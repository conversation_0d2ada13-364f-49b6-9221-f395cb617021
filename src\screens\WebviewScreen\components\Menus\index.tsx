import React from 'react';
import { View, StyleSheet } from 'react-native';

import metrics from 'themes/metrics';
import MenuButton from 'components/MenuButton';

const itemWidth =
  (metrics.fullWidth - metrics.spacing4 * 2 - metrics.spacing2 * 3) / 4;
const Menu = () => {
  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <MenuButton
          customStyles={{ width: itemWidth }}
          iconName="search"
          title="Dinh dưỡng"
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          iconName="search"
          title="Dinh dưỡng"
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          iconName="search"
          title="Dinh dưỡng"
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          iconName="search"
          title="Dinh dưỡng"
        />
      </View>
      <View style={styles.row}>
        <MenuButton
          customStyles={{ width: itemWidth }}
          iconName="search"
          title="Dinh dưỡng"
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          iconName="search"
          title="Dinh dưỡng"
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          iconName="search"
          title="Dinh dưỡng"
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          iconName="search"
          title="Dinh dưỡng"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: metrics.spacing4,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  row: {
    justifyContent: 'space-around',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexDirection: 'row',
    gap: metrics.spacing2,
    marginBottom: metrics.spacing3,
  },
});

export default Menu;
