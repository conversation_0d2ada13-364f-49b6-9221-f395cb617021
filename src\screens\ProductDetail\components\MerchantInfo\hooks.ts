import { getMerchantProducts, getMerchantInfor } from 'api/products';
import { useEffect, useState } from 'react';

export const useMerchantProducts = ({ id }: any) => {
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState([]);
  const [merchant, setMerchant] = useState(null);

  const fetchMerchantInfo = async () => {
    try {
      setLoading(true);
      const response = await getMerchantInfor({ id });
      setMerchant(response);
    } catch (error) {
      console.error('Error fetching merchant info:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await getMerchantProducts({ id },  { page: 0, size: 10 });
      setProducts(response.products);
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMerchantInfo();
    fetchProducts();
  }, [id]); 

  return {
    loading,
    products,
    merchant,
  };
};
