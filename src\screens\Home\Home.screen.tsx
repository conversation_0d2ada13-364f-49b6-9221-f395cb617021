import React from 'react';
import { SafeAreaView, StyleSheet, ScrollView, View, Text } from 'react-native';

import HomeHeader from 'components/Header/HomeHeader';
import Banner from './components/Banner';
import Menu from './components/Menus';
import HotPost from './components/HotPosts';
import metrics from 'themes/metrics';
import commonStyles from 'themes/commonStyles';
import { Input } from '@ui-kitten/components';
import HorizontalProducts from './components/HorizontalProducts';
import Coupons from './components/Coupons';

const Home = () => {
  return (
    <>
      <HomeHeader />
      <SafeAreaView style={styles.safeAreaView}>
        <ScrollView style={styles.outerWrapper}>
          <View style={styles.rowSpacing}>
            <Text style={commonStyles.whiteTitle}>Chào buổi sáng</Text>
          </View>
          {/* <View style={styles.rowSpacing}>
            <Input
              style={commonStyles.flex1}
              inputAccessoryViewID="search"
              placeholder="Tìm kiếm mọi thứ"
            />
          </View> */}
          <View style={styles.noteContainer}>
            <Text style={styles.noteText}>Ứng dụng đang hoạt động ở chế độ thử nghiệm, đang thực hiện đăng ký với Bộ Công Thương</Text>
          </View>
          <Banner />
          <Coupons />
          {/* <Menu /> */}
          <HotPost />
          <HorizontalProducts hasBackground title="Sản phẩm bán chạy" />
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: { flex: 1 },
  outerWrapper: {
    flex: 1,
    marginTop: -200,
  },
  rowSpacing: {
    flexDirection: 'row',
    paddingHorizontal: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  noteContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)', 
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginHorizontal: metrics.spacing4,
    marginBottom: metrics.spacing3,
    borderLeftWidth: 4,
    borderLeftColor: '#FFCC00', // viền vàng bên trái
  },
  noteText: {
    color: 'white', 
    fontSize: 13,
    lineHeight: 18,
    textAlign: 'left',
  },
  headerSpacing: {
    paddingBottom: 200,
  },
  paddingBottom: {
    paddingBottom: metrics.spacing4,
  },
});

export default Home;