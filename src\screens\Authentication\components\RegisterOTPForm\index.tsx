import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import CommonButton from 'components/CommonButton';
import { AUTHENTICATION_STEP } from 'constants/authentication';
import FormInput from 'components/FormInput';
import { activate, resendActivation } from 'api/users';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import Toast from 'react-native-toast-message';

const schema = yup.object({
  key: yup.string().trim().required('Vui lòng nhập mã xác minh'),
});

const RegisterOTPForm = ({ setStep, setCurrentData, currentData }: any) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm({
    defaultValues: {
      key: '',
    },
    resolver: yupResolver(schema),
  });

  const resend = async () => {
    try {
      const { phoneOrEmail } = currentData;
      const response = await resendActivation({ body: { phoneOrEmail } });
      if (response?.code) {
        setError('key', {
          message:
            response.message || 'Có lỗi xảy ra trong quá trình gửi mã xác nhận',
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onSubmit = async (values: any) => {
    try {
      const { phoneOrEmail } = currentData;
      const response = await activate({
        body: { phoneOrEmail, key: values.key, email: phoneOrEmail },
      });
      if (response) {
        if (response?.code) {
          setError('key', {
            message:
              response.message ||
              'Có lỗi xảy ra trong quá trình gửi mã xác nhận',
          });
        } else {
          setCurrentData({
            ...currentData,
            key: values?.key,
          });
          setStep(AUTHENTICATION_STEP.REGISTER_CREATE_PASS);
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View>
          <Text style={styles.subTitle}>Mã xác minh</Text>
        </View>
        <View style={styles.item}>
          <FormInput
            label={
              'Nhập mã xác minh mà Hathyo.com đã gửi vào email của bạn: *****'
            }
            control={control}
            required
            placeholder="Nhập mã xác minh"
            name="key"
            errors={errors?.key}
          />
          <View style={styles.center}>
            <TouchableOpacity onPress={resend}>
              <Text style={styles.text}>
                Không nhận được mã xác minh ?
                <Text style={styles.registerText}> Gửi lại mã </Text>
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        <View style={commonStyles.rowCenter}>
          <CommonButton
            onPress={handleSubmit(onSubmit)}
            customStyles={{ backgroundColor: colors['Moss/500'] }}
          >
            Xác nhận
          </CommonButton>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  content: {
    padding: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
    marginBottom: metrics.spacing4,
  },
  item: {
    marginBottom: metrics.spacing3, 
  },
  subTitle: {
    fontSize: fonts.size.h2,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  text: {
    fontSize: fonts.size.input,
    fontWeight: '400',
    color: colors['Grayiron/400'],
    marginBottom: metrics.spacing4,
  },
  registerText: {
    fontSize: fonts.size.input,
    fontWeight: '500',
    color: colors['Moss/600'],
    marginBottom: metrics.spacing4,
  },
  center: {
    alignItems: 'center',
    marginTop: metrics.spacing2,
  },
});

export default RegisterOTPForm;