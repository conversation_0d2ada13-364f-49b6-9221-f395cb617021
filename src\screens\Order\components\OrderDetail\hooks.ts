import { useState, useEffect, useCallback } from 'react';
import { getOrderDetail, cancelOrder, confirmOrderReceived } from 'api/order';
import { createProductRating } from 'api/products';
import { Order } from 'types/order';

interface UseOrderDetailReturn {
  order: Order | null;
  loading: boolean;
  error: string | null;
  cancelOrder: (cancelReason: string) => Promise<boolean>;
  confirmOrderReceived: () => Promise<boolean>;
  submitProductReview: (productId: number, rating: number, comment: string) => Promise<boolean>;
}

const useOrderDetail = (orderId: number): UseOrderDetailReturn => {
  const [state, setState] = useState<{
    order: Order | null;
    loading: boolean;
    error: string | null;
  }>({
    order: null,
    loading: true,
    error: null,
  });

  const fetchOrder = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const response = await getOrderDetail(orderId);
      if (response) {
        setState({
          order: response,
          loading: false,
          error: null,
        });
      } else {
        throw new Error('Không tìm thấy đơn hàng');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Lỗi khi lấy chi tiết đơn hàng';
      setState({
        order: null,
        loading: false,
        error: errorMessage,
      });
    }
  }, [orderId]);

  useEffect(() => {
    fetchOrder();
  }, [fetchOrder]);

  const handleCancelOrder = useCallback(async (cancelReason: string) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const response = await cancelOrder(orderId, cancelReason);
      if (response) {
        await fetchOrder();
        return true;
      }
      throw new Error('Hủy đơn hàng thất bại');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Lỗi khi hủy đơn hàng';
      setState((prev) => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      return false;
    }
  }, [orderId, fetchOrder]);

  const handleConfirmOrderReceived = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const success = await confirmOrderReceived(orderId);
      console.log('Confirm order received response:', success);
      if (success) {
        await fetchOrder();
        return true;
      }
      throw new Error('Xác nhận nhận hàng thất bại');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Lỗi khi xác nhận nhận hàng';
      setState((prev) => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
      return false;
    }
  }, [orderId, fetchOrder]);

  const handleProductReview = useCallback(
    async (productId: number, rating: number, comment: string) => {
      if (!state.order) {
        setState((prev) => ({
          ...prev,
          error: 'Không có thông tin đơn hàng để đánh giá',
        }));
        return false;
      }

      if (state.order.status !== 'RECEIVED') {
        setState((prev) => ({
          ...prev,
          error: 'Chỉ có thể đánh giá sản phẩm khi đơn hàng ở trạng thái Đã nhận',
        }));
        return false;
      }

      const orderItem = state.order.orderItems?.find((item) => item.productId === productId);
      if (!orderItem) {
        setState((prev) => ({
          ...prev,
          error: 'Không tìm thấy sản phẩm trong đơn hàng',
        }));
        return false;
      }

      const reviewData = {
        productId,
        orderItemId: orderItem.id,
        rate: rating,
        comment: comment || '', 
        imageUrls: '',
      };

      try {
        setState((prev) => ({ ...prev, loading: true, error: null }));
        console.log('Submitting review with data:', reviewData);
        const response = await createProductRating(reviewData);
        console.log('API response:', response);

        if (response) {
          await fetchOrder();
          return true;
        }
        throw new Error('Gửi đánh giá thất bại: Không nhận được phản hồi hợp lệ');
      } catch (err) {
        console.error('Error submitting review:', err);
        const errorMessage =
          err instanceof Error ? err.message : 'Lỗi khi gửi đánh giá sản phẩm';
        setState((prev) => ({
          ...prev,
          loading: false,
          error: errorMessage,
        }));
        return false;
      }
    },
    [state.order, fetchOrder]
  );

  return {
    order: state.order,
    loading: state.loading,
    error: state.error,
    cancelOrder: handleCancelOrder,
    confirmOrderReceived: handleConfirmOrderReceived,
    submitProductReview: handleProductReview,
  };
};

export default useOrderDetail;