import React, { useMemo, useState, useEffect } from 'react';
import { SafeAreaView, StyleSheet, ScrollView, ActivityIndicator, View, Text, Pressable } from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import HomeHeader from 'components/Header/HomeHeader';
import CustomAlert from 'utils/widgets/CustomAlert';
import metrics from 'themes/metrics';
import commonStyles from 'themes/commonStyles';
import colors from 'themes/colors';
import useOrders from './hooks';
import OrderItems from '../Order/components/OrderItem';
import { Order } from 'types/order';
import { ORDER_STATUS } from 'themes/constant';
import fonts from 'themes/fonts';
import { checkAuthentication } from 'utils/authUtils';

const TABS = [
  { key: ORDER_STATUS.PENDING.key, title: ORDER_STATUS.PENDING.name, icon: 'hourglass-empty' },
  { key: ORDER_STATUS.ACCEPTED.key, title: ORDER_STATUS.ACCEPTED.name, icon: 'thumb-up' },
  { key: ORDER_STATUS.IN_TRANSIT.key, title: ORDER_STATUS.IN_TRANSIT.name, icon: 'local-shipping' },
  { key: ORDER_STATUS.DELIVERED.key, title: ORDER_STATUS.DELIVERED.name, icon: 'check-circle' },
  { key: ORDER_STATUS.RECEIVED.key, title: ORDER_STATUS.RECEIVED.name, icon: 'checklist' },
  { key: ORDER_STATUS.CANCELLED.key, title: ORDER_STATUS.CANCELLED.name, icon: 'cancel' },
];

const OrderScreen = () => {
  const [activeTab, setActiveTab] = useState(TABS[0].key);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [alert, setAlert] = useState<{
    visible: boolean;
    type: 'success' | 'error' | 'info' | 'confirm';
    title: string;
    message: string;
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[];
  } | null>(null);
  const { orders = [], loading, error, refresh } = useOrders(activeTab);
  const navigation = useNavigation();

  const showAlert = (
    type: 'success' | 'error' | 'info' | 'confirm',
    title: string,
    message: string,
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[]
  ) => {
    setAlert({ visible: true, type, title, message, buttons });
  };

  const hideAlert = () => {
    setAlert(null);
  };

  useEffect(() => {
    const checkAuth = async () => {
      const { isAuthenticated: authStatus } = await checkAuthentication();
      setIsAuthenticated(authStatus);

      if (!authStatus) {
        showAlert(
          'info',
          'Yêu cầu đăng nhập',
          'Vui lòng đăng nhập để xem đơn hàng của bạn',
          [
            {
              text: 'Đăng nhập',
              style: 'default',
              onPress: () => {
                hideAlert();
                navigation.navigate('Authentication');
              }
            }
          ]
        );
      }
    };

    checkAuth();
  }, [navigation]);

  useFocusEffect(
    React.useCallback(() => {
      refresh();
    }, [refresh])
  );

  const renderContent = useMemo(() => {
    if (loading && orders.length === 0) {
      return (
        <View style={[styles.center, styles.outerWrapper]}>
          <ActivityIndicator size="large" color={colors['Primary/400']} />
        </View>
      );
    }
    if (error && orders.length === 0) {
      return (
        <View style={[styles.center, styles.outerWrapper]}>
          <Text style={commonStyles.errorText}>{error}</Text>
        </View>
      );
    }
    return (
      <ScrollView style={styles.outerWrapper}>
        <OrderItems
          orders={orders}
          loading={loading}
          onItemPress={(order: Order) => navigation.navigate('OrderDetail', { orderId: order.id })}
        />
      </ScrollView>
    );
  }, [loading, error, orders, navigation]);

  if (isAuthenticated === null) {
    return (
      <>
        <HomeHeader backButton title="Đơn hàng của tôi" noStretch />
        <SafeAreaView style={styles.safeAreaView}>
          <View style={styles.center}>
            <ActivityIndicator size="large" color={colors['Primary/400']} />
            <Text style={styles.loadingText}>Đang kiểm tra đăng nhập...</Text>
          </View>
        </SafeAreaView>
      </>
    );
  }

  if (!isAuthenticated) {
    return (
      <>
        <HomeHeader backButton title="Đơn hàng của tôi" noStretch />
        <SafeAreaView style={styles.safeAreaView}>
          <View style={styles.center}>
            <Text style={styles.authRequiredText}>Vui lòng đăng nhập để xem đơn hàng</Text>
          </View>
          {alert && (
            <CustomAlert
              visible={alert.visible}
              type={alert.type}
              title={alert.title}
              message={alert.message}
              buttons={alert.buttons}
              onDismiss={hideAlert}
              dismissable={alert.type !== 'success'}
            />
          )}
        </SafeAreaView>
      </>
    );
  }

  return (
    <>
      <HomeHeader backButton title="Đơn hàng của tôi" noStretch />
      <SafeAreaView style={styles.safeAreaView}>
        <View style={styles.tabContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.tabScroll}>
            {TABS.map((tab) => (
              <Pressable
                key={tab.key}
                style={({ pressed }) => [
                  styles.tab,
                  activeTab === tab.key && styles.activeTab,
                  pressed && styles.pressedTab,
                ]}
                onPress={() => setActiveTab(tab.key)}
                android_ripple={{ color: colors['Primary/200'], borderless: true }}
              >
                <MaterialIcons
                  name={tab.icon}
                  size={22}
                  color={activeTab === tab.key ? colors.white : colors['Grayiron/700']}
                  style={styles.tabIcon}
                />
                <Text style={[styles.tabText, activeTab === tab.key && styles.activeTabText]}>
                  {tab.title}
                </Text>
              </Pressable>
            ))}
          </ScrollView>
        </View>
        {renderContent}
      </SafeAreaView>
      {alert && (
        <CustomAlert
          visible={alert.visible}
          type={alert.type}
          title={alert.title}
          message={alert.message}
          buttons={alert.buttons}
          onDismiss={hideAlert}
          dismissable={alert.type !== 'success'}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
    backgroundColor: colors.white,
  },
  center: {
    ...commonStyles.rowCenter,
    flex: 1,
  },
  outerWrapper: {
    flex: 1,
    paddingTop: metrics.spacing4,
  },
  tabContainer: {
    backgroundColor: colors.white,
    paddingVertical: metrics.spacing2,
    paddingHorizontal: metrics.spacing3,
    borderBottomWidth: 1,
    borderBottomColor: colors['Grayiron/50'],
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 2,
    elevation: 1,
  },
  tabScroll: {
    flexGrow: 1,
    justifyContent: 'flex-start',
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: metrics.spacing1,
    paddingHorizontal: metrics.spacing2,
    marginHorizontal: metrics.spacing1,
    borderRadius: 8,
    backgroundColor: colors['Grayiron/200'],
    height: 36,
  },
  activeTab: {
    backgroundColor: colors['Moss/400'],
    borderBottomWidth: 0,
  },
  pressedTab: {
    transform: [{ scale: 0.95 }],
  },
  tabIcon: {
    marginRight: metrics.spacing1,
  },
  tabText: {
    fontSize: fonts.size.medium,
    color: colors['Grayiron/500'],
    fontWeight: '600',
    textAlign: 'center',
  },
  activeTabText: {
    color: colors.white,
    fontWeight: '700',
  },
  loadingText: {
    fontSize: 16,
    marginTop: metrics.spacing3,
    color: colors['Gray/700'],
    textAlign: 'center',
  },
  authRequiredText: {
    fontSize: 16,
    color: colors['Gray/700'],
    textAlign: 'center',
  },
});

export default OrderScreen;