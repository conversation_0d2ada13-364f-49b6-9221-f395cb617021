import { StyleSheet } from 'react-native';
import colors from 'themes/colors';
import fonts from 'themes/fonts';
import metrics from 'themes/metrics';

const styles = StyleSheet.create({
  container: {
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: metrics.spacing3,
    paddingVertical: metrics.spacing2,
    flexDirection: 'row',
    flex: 1,
  },
  border: {
    borderRadius: metrics.radius4,
  },
  title: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  postImage: {
    width: metrics.fullWidth / 3 - metrics.spacing4,
    height: metrics.fullWidth / 3 - metrics.spacing4,
    borderRadius: metrics.radius2
  },
  content: {
    height: metrics.fullWidth / 3 - metrics.spacing4,
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    flex: 1,
    paddingLeft: metrics.spacing3,
  },
  hintTitle: {
    color: colors['Grayiron/600'],
    fontSize: fonts.size.medium,
    marginLeft: metrics.spacing2
  },
});

export default styles;
