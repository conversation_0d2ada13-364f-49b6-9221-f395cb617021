/* eslint-disable react-hooks/exhaustive-deps */
import { getPostsByTopicId } from 'api/posts';
import { useEffect, useState } from 'react';

export const useTopicDetail = ({ id }: any) => {
  const [loadingPost, setLoadingPost] = useState(false);
  const [posts, setPosts] = useState({});
  const fetchPosts = async ({ page = 0 }) => {
    try {
      setLoadingPost(true);
      const response = await getPostsByTopicId({ id, page, size: 10 });
      setPosts(response?.posts as any);
    } catch (error) {
    } finally {
      setLoadingPost(false);
    }
  };

  useEffect(() => {
    // fetchTopic();
    fetchPosts({ page: 0 });
  }, []);

  return {
    loadingPost,
    posts,
    fetchPosts,
  };
};
