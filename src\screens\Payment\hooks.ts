import { useState } from 'react';
import { checkoutConfirm } from 'api/payments';
import { useCart } from 'hooks/contexts/CartContext';

interface UserNote {
  cartId: number;
  userNote: string;
}

interface CheckoutConfirmParams {
  addressId: number;
  userCouponId: number;
  couponHathyoCode: string;
  userNote: UserNote[];
}

interface CheckoutConfirmResponse {
  orderId?: string;
  [key: string]: any;
}

export const useCheckoutConfirm = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [response, setResponse] = useState<CheckoutConfirmResponse | null>(null);
  const { fetchCartCount } = useCart();

  const confirmCheckout = async (data: CheckoutConfirmParams) => {
    try {
      setLoading(true);
      setError(null);
      setResponse(null);

      const result = await checkoutConfirm(data);
      setResponse(result);
      await fetchCartCount();
      return result;
    } catch (err: any) {
      const errorMessage = err?.response?.data?.message || err?.message || 'Không thể xác nhận thanh toán';
      setError(errorMessage);
      console.error('Checkout confirm error:', errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    confirmCheckout,
    loading,
    error,
    response,
  };
};