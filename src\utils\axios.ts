import AsyncStorage from '@react-native-async-storage/async-storage';
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { STORAGE } from 'constants/authentication';
import { useUserStore } from 'stores/user';

export const apis = (): AxiosInstance => {
  const axiosApi: AxiosRequestConfig = {
    baseURL: 'https://api.hathyo.com/admin/api/v1',
    timeout: 60000,
  };

  const instance = axios.create(axiosApi);
  instance.interceptors.request.use(
    async function (config) {
      const token = await AsyncStorage.getItem(STORAGE.ACCESS_TOKEN);

      if (token) {
        config.headers.Authorization = 'Bearer ' + token;
      }

      return config;
    },
    function (error) {
      return Promise.reject(error);
    },
  );

  instance.interceptors.response.use(
    function (response) {
      return response;
    },
    async function (error) {
      if (error?.status === 401) {
        // const setUser = useUserStore((state: any) => state?.setUser);
        await AsyncStorage.multiRemove([
          STORAGE.ACCESS_TOKEN,
          STORAGE.REFRESH_TOKEN,
        ]);
        // setUser({});
      }

      return Promise.reject(error?.response?.data || error?.response || error);
    },
  );
  return instance;
};
