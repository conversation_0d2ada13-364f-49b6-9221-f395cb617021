import { map, pick } from 'lodash';
import { useProductsStore } from 'stores/products';

interface Category {
  id: number;
  name: string;
  childCategories?: Category[];
}

interface UseCategoriesMenuProps {
  id?: number | null;
}

export const useCategoriesMenu = ({ id }: UseCategoriesMenuProps) => {
  const categories = useProductsStore((state: any) => state.categories || []); // Ensure categories is always an array

  if (id) {
    const findCategory = (categories: Category[], id: number): Category | undefined => {
      for (const category of categories) {
        if (category.id === id) return category;
        if (category.childCategories) {
          const found = findCategory(category.childCategories, id);
          if (found) return found;
        }
      }
      return undefined;
    };

    const category = findCategory(categories, id);
    return { categories: category?.childCategories || [] }; // Always return an array
  }

  return {
    categories: map(categories, (item: Category) =>
      pick(item, ['id', 'name', 'childCategories'])
    ) as Category[],
  };
};