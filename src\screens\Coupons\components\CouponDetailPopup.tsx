import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Modal,
  Image,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import { Coupon } from 'types/Coupon';

const mossColors = {
  primary: '#4A7043',
  primaryDark: '#3A5A34',
  primaryLight: '#6B8E23',
  accent: '#8BAF7A',
  text: '#2F3D2A',
  background: '#F9FBF7',
  disabled: '#A8B5A2',
  warning: '#C0392B',
};

type RootStackParamList = {
  Cart: undefined;
};

type NavigationProps = NativeStackNavigationProp<RootStackParamList>;

interface CouponDetailPopupProps {
  visible: boolean;
  coupon: Coupon | null;
  onClose: () => void;
}

const CouponDetailPopup: React.FC<CouponDetailPopupProps> = ({ visible, coupon, onClose }) => {
  const navigation = useNavigation<NavigationProps>();
  const slideAnim = useRef(new Animated.Value(300)).current;

  useEffect(() => {
    if (visible) {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 300,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, slideAnim]);

  const formatDate = (dateStr: string): string => {
    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return 'N/A';
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = date.getFullYear();
      return `${day}/${month}/${year}`;
    } catch {
      return 'N/A';
    }
  };

  const getDiscountDisplay = (): string => {
    if (!coupon) return 'N/A';
    if (coupon.discountType === 'PERCENT' && coupon.discountPercent !== null) {
      return `${coupon.discountPercent}%`;
    }
    if (coupon.discountValue !== null) {
      return `${coupon.discountValue.toLocaleString()}đ`;
    }
    return 'N/A';
  };

  if (!coupon) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <TouchableOpacity style={styles.backdrop} onPress={onClose} activeOpacity={0.8} />
        <Animated.View style={[styles.popup, { transform: [{ translateY: slideAnim }] }]}>
          <LinearGradient
            colors={['#FFFFFF', '#F5F7F2']}
            style={styles.popupContent}
          >
            <View style={styles.header}>
              <Text style={styles.title}>{coupon.title}</Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <MaterialCommunityIcons name="close" size={24} color={mossColors.text} />
              </TouchableOpacity>
            </View>
            <Image source={{ uri: coupon.image }} style={styles.couponImage} resizeMode="contain" />
            <View style={styles.detailContainer}>
              <Text style={styles.description}>{coupon.description}</Text>
              <View style={styles.detailRow}>
                <MaterialCommunityIcons name="tag" size={16} color={mossColors.primaryDark} />
                <Text style={styles.detailText}>Giảm giá: {getDiscountDisplay()}</Text>
              </View>
              <View style={styles.detailRow}>
                <MaterialCommunityIcons name="calendar" size={16} color={mossColors.primaryDark} />
                <Text style={styles.detailText}>Hết hạn: {formatDate(coupon.expiredAt)}</Text>
              </View>
              <View style={styles.detailRow}>
                <MaterialCommunityIcons name="cart" size={16} color={mossColors.primaryDark} />
                <Text style={styles.detailText}>
                  Đơn tối thiểu: {coupon.minimumPriceApply.toLocaleString()}đ
                </Text>
              </View>
              {coupon.maxDiscountPrice !== null && (
                <View style={styles.detailRow}>
                  <MaterialCommunityIcons name="currency-usd" size={16} color={mossColors.primaryDark} />
                  <Text style={styles.detailText}>
                    Giảm tối đa: {coupon.maxDiscountPrice.toLocaleString()}đ
                  </Text>
                </View>
              )}
              <View style={styles.detailRow}>
                <MaterialCommunityIcons
                  name={coupon.applyStatus ? 'check-circle' : 'alert-circle'}
                  size={16}
                  color={coupon.applyStatus ? mossColors.primary : mossColors.warning}
                />
                <Text style={styles.detailText}>
                  Trạng thái: {coupon.applyStatus ? 'Áp dụng được' : 'Không áp dụng được'}
                </Text>
              </View>
              <Text style={styles.codeText}>Mã: {coupon.code}</Text>
            </View>
            <TouchableOpacity
              style={styles.cartButton}
              onPress={() => {
                navigation.navigate('Cart');
                onClose();
              }}
              activeOpacity={0.7}
            >
              <LinearGradient
                colors={['#4A7043', '#3A5A34']}
                style={styles.buttonInner}
              >
                <Text style={styles.buttonText}>Đi đến giỏ hàng</Text>
              </LinearGradient>
            </TouchableOpacity>
          </LinearGradient>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  backdrop: {
    flex: 1,
  },
  popup: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: metrics.radius3,
    borderTopRightRadius: metrics.radius3,
    maxHeight: '80%',
  },
  popupContent: {
    padding: metrics.spacing3,
    borderTopLeftRadius: metrics.radius3,
    borderTopRightRadius: metrics.radius3,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: metrics.spacing2,
  },
  title: {
    ...fonts.style.normal,
    color: mossColors.text,
    fontWeight: '700',
    fontSize: 18,
  },
  closeButton: {
    padding: metrics.spacing1,
  },
  couponImage: {
    width: '100%',
    height: 150,
    borderRadius: metrics.radius2,
    marginBottom: metrics.spacing2,
  },
  detailContainer: {
    marginBottom: metrics.spacing3,
  },
  description: {
    ...fonts.style.description,
    color: mossColors.text + 'A0',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: metrics.spacing2,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: metrics.spacing1,
  },
  detailText: {
    ...fonts.style.description,
    color: mossColors.text,
    fontSize: 13,
    marginLeft: metrics.spacing1,
  },
  codeText: {
    ...fonts.style.normal,
    color: mossColors.primary,
    fontWeight: '600',
    fontSize: 14,
    marginTop: metrics.spacing1,
  },
  cartButton: {
    borderRadius: metrics.radius2,
    overflow: 'hidden',
  },
  buttonInner: {
    paddingVertical: metrics.spacing2,
    paddingHorizontal: metrics.spacing3,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    ...fonts.style.normal,
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
    textAlign: 'center',
  },
});

export default CouponDetailPopup;