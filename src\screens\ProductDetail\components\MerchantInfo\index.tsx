import React from 'react';
import { View, StyleSheet, Text, ScrollView } from 'react-native';
import FastImage from 'react-native-fast-image';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { get, map } from 'lodash';
import { Divider } from '@ui-kitten/components';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import Product from 'components/Product';
import CommonButton from 'components/CommonButton';
import { useMerchantProducts } from './hooks';
import { addRecentProducts, addToCart } from 'utils/cart';

type RootStackParamList = {
  ProductDetail: { id: string };
  AppTab: undefined;
  Merchant: { merchantId: string }; // Thêm type cho Merchant screen
};

type NavigationProps = NativeStackNavigationProp<RootStackParamList>;

interface Product {
  id: string;
  title: string;
  mainImageUrl: string;
  price: number;
  anchoPrice?: number;
  discountPercent?: number;
  rating?: number;
}

interface Merchant {
  id: string;
  logo?: string;
  storeName?: string;
}

const MerchantInfo = ({ merchantId }: { merchantId: string }) => {
  const { products, merchant, loading, error } = useMerchantProducts({ id: merchantId });
  const navigation = useNavigation<NavigationProps>();
  const [isNavigating, setIsNavigating] = React.useState(false);

  const logo = get(merchant, 'logo', 'https://via.placeholder.com/150');
  const storeName = get(merchant, 'storeName', 'Tên cửa hàng');

  if (loading) {
    return <Text style={styles.sectionTitle}>Đang tải...</Text>;
  }

  if (error || !merchant) {
    return <Text style={styles.sectionTitle}>Lỗi khi tải thông tin cửa hàng</Text>;
  }

  return (
    <View style={styles.container}>
      <View style={styles.merchantContainer}>
        <View style={commonStyles.row}>
          <FastImage style={styles.avatar} source={{ uri: logo }} />
          <View style={styles.col}>
            <Text style={styles.title}>{storeName}</Text>
            <Text style={styles.subTitle}>Online 8 phút trước</Text>
          </View>
        </View>
        <View style={styles.buttonContainer}>
          <CommonButton
            customStyles={styles.button}
            size="small"
            onPress={() => {
              navigation.navigate('Merchant', { merchantId: String(merchantId) });
            }}
          >
            Xem shop
          </CommonButton>
          <CommonButton
            customStyles={styles.button}
            size="small"
            onPress={() => console.log('Chat với shop:', merchantId)}
          >
            Chat với shop
          </CommonButton>
        </View>
      </View>

      <Text style={styles.sectionTitle}>Các sản phẩm khác của Shop</Text>
      <ScrollView
        style={styles.products}
        horizontal
        bounces
        showsHorizontalScrollIndicator={false}
      >
        {map(products, (product: Product) => (
          <View key={product.id} style={styles.item}>
            <Product
              customStyles={{ maxWidth: metrics.fullWidth / 2 }}
              title={product.title}
              uri={product.mainImageUrl}
              price={product.price}
              anchoPrice={product.anchoPrice}
              discountPercent={product.discountPercent}
              rating={product.rating}
              onPress={() => {
                if (product.id && !isNavigating) {
                  setIsNavigating(true);
                  addRecentProducts(product);
                  navigation.push('ProductDetail', { id: product.id });
                  setTimeout(() => setIsNavigating(false), 1000);
                }
              }}
              onAddToCart={() => addToCart({ ...product, quantity: 1 })}
            />
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

// Giữ nguyên phần styles như cũ
const styles = StyleSheet.create({
  container: {
    marginBottom: metrics.spacing4,
    paddingTop: metrics.spacing4,
    backgroundColor: colors.white,
  },
  merchantContainer: {
    paddingHorizontal: metrics.spacing4,
    marginBottom: metrics.spacing4,
  },
  buttonContainer: {
    ...commonStyles.rowSpaceBetween,
    gap: metrics.spacing4,
    marginTop: metrics.spacing2,
  },
  button: {
    flex: 1,
    backgroundColor: colors['Moss/500']
  },
  avatar: {
    marginRight: metrics.spacing2,
    width: 46,
    height: 46,
    borderRadius: 23,
    backgroundColor: colors['Danger/500'] || colors['Grayiron/500'],
  },
  col: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  title: {
    color: colors['Grayiron/600'],
    fontSize: fonts.size.h2,
    fontWeight: '600',
  },
  subTitle: {
    color: colors['Grayiron/400'],
    fontSize: fonts.size.regular,
    fontWeight: '400',
  },
  sectionTitle: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
    paddingHorizontal: metrics.spacing4,
  },
  products: {
    paddingHorizontal: metrics.spacing4,
    paddingBottom: metrics.spacing4,
  },
  item: {
    marginRight: metrics.spacing4,
  },
});

export default MerchantInfo;