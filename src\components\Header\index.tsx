import React from 'react';
import { StyleSheet, TextProps, View, ViewProps } from 'react-native';
import metrics from 'themes/metrics';
import commonStyles from 'themes/commonStyles';
import HeaderIcon from 'components/Header/HeaderIcon';
import { Text } from 'react-native';
import fonts from 'themes/fonts';

type Props = {
  title?: React.ReactElement | string;
  titleStyle?: TextProps | any;
  customStyle?: ViewProps | any;
  onBackPress?: any;
  backButton?: boolean;
  leftComponent?: React.ReactElement;
  rightComponent?: React.ReactElement | null;
};

function Header({
  leftComponent,
  rightComponent,
  title,
  backButton,
  titleStyle,
  customStyle,
  onBackPress,
}: Props) {
  return (
    <View style={[styles.container, customStyle]}>
      <View style={styles.left}>
        {leftComponent
          ? leftComponent
          : backButton && <HeaderIcon onPress={onBackPress} />}
      </View>
      <View style={styles.center}>
        <Text style={[styles.title, titleStyle]}>{title}</Text>
      </View>
      <View style={styles.right}>{rightComponent}</View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...commonStyles.rowSpaceBetween,
    alignItems: 'center',
    paddingHorizontal: metrics.spacing4,
    // backgroundColor: 'red',
    paddingVertical: metrics.spacing2,
  },
  title: {
    color: 'white',
    fontWeight: '600',
    fontSize: fonts.size.regular,
    textAlign: 'center',
  },
  left: {
    ...commonStyles.row,
    maxWidth: metrics.fullWidth / 3 - metrics.spacing4 * 2,
    flex: 1,
    justifyContent: 'flex-start',
  },
  center: {
    ...commonStyles.row,
    minWidth: metrics.fullWidth / 3 - metrics.spacing4 * 2,
    flex: 1,
    justifyContent: 'center',
  },
  right: {
    ...commonStyles.row,
    maxWidth: metrics.fullWidth / 3 - metrics.spacing4 * 2,
    justifyContent: 'flex-end',
    flex: 1,
  },
  backButton: {
    padding: metrics.spacing3,
    borderRadius: metrics.radius2,
  },
  headerIcon: {
    width: 14,
    height: 14,
  },
});

export default Header;
