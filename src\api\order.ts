import { ORDER_STATUS } from "themes/constant";
import { apis } from "utils/axios";

export const getListOrder = async (page: number, size: number, status?: string) => {
  try {
    const response = await apis().get('/orders', {
      params: {
        page,
        size,
        ...(status && ORDER_STATUS[status as keyof typeof ORDER_STATUS]
          ? { status: ORDER_STATUS[status as keyof typeof ORDER_STATUS].key }
          : {}),
      },
    });
    return response?.data;
  } catch (e) {
    console.error('Lỗi khi lấy danh sách đơn hàng:', e);
    return null;
  }
};

export const getOrderDetail = async (id: number) => {
  try {
    const response = await apis().get(`/orders/${id}`);
    return response?.data;
  } catch (e) {
    console.error(`Lỗi khi lấy chi tiết đơn hàng ${id}:`, e);
    return null;
  }
};

export const cancelOrder = async (id: number, cancelReason: string) => {
  try {
    const response = await apis().patch(`/orders/${id}/cancel`, {
      cancelReason,
    });
    return response?.data;
  } catch (e) {
    console.error(`Lỗi khi hủy đơn hàng ${id}:`, e);
    throw new Error('Không thể hủy đơn hàng. Vui lòng thử lại.');
  }
};

export const confirmOrderReceived = async (id: number) => {
  try {
    const response = await apis().patch(`/orders/${id}/received`);
    console.log("in ra response là: ", response);
    if (response.status === 200) {
      return true;
    }
    throw new Error('Xác nhận nhận hàng thất bại');
  } catch (e) {
    console.error(`Lỗi khi xác nhận nhận đơn hàng ${id}:`, e);
    throw new Error('Không thể xác nhận nhận đơn hàng. Vui lòng thử lại.');
  }
};