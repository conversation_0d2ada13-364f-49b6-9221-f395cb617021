import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE } from 'constants/authentication';

export interface AuthCheckResult {
  isAuthenticated: boolean;
  token: string | null;
}

/**
 * Kiểm tra xem người dùng đã đăng nhập hay chưa
 * @returns Promise<AuthCheckResult>
 */
export const checkAuthentication = async (): Promise<AuthCheckResult> => {
  try {
    const token = await AsyncStorage.getItem(STORAGE.ACCESS_TOKEN);
    return {
      isAuthenticated: !!token,
      token,
    };
  } catch (error) {
    console.error('Error checking authentication:', error);
    return {
      isAuthenticated: false,
      token: null,
    };
  }
};

/**
 * Hiển thị alert yêu cầu đăng nhập và chuyển hướng
 * @param navigation - Navigation object
 * @param showAlert - Function để hiển thị CustomAlert
 * @param hideAlert - Function để ẩn CustomAlert
 * @param customMessage - Thông báo tùy chỉnh (optional)
 */
export const showLoginRequiredAlert = (
  navigation: any,
  showAlert: (
    type: 'success' | 'error' | 'info' | 'confirm',
    title: string,
    message: string,
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[]
  ) => void,
  hideAlert: () => void,
  customMessage?: string
) => {
  const message = customMessage || 'Vui lòng đăng nhập để thực hiện hành động này';
  
  showAlert(
    'info',
    'Yêu cầu đăng nhập',
    message,
    [
      { 
        text: 'Hủy', 
        style: 'cancel', 
        onPress: hideAlert 
      },
      { 
        text: 'Đăng nhập', 
        style: 'default', 
        onPress: () => {
          hideAlert();
          navigation.navigate('Authentication');
        }
      }
    ]
  );
};

/**
 * Wrapper function để kiểm tra authentication trước khi thực hiện action
 * @param navigation - Navigation object
 * @param showAlert - Function để hiển thị CustomAlert
 * @param hideAlert - Function để ẩn CustomAlert
 * @param action - Function sẽ được thực hiện nếu đã đăng nhập
 * @param customMessage - Thông báo tùy chỉnh (optional)
 */
export const requireAuth = async (
  navigation: any,
  showAlert: (
    type: 'success' | 'error' | 'info' | 'confirm',
    title: string,
    message: string,
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[]
  ) => void,
  hideAlert: () => void,
  action: () => void | Promise<void>,
  customMessage?: string
) => {
  const { isAuthenticated } = await checkAuthentication();
  
  if (!isAuthenticated) {
    showLoginRequiredAlert(navigation, showAlert, hideAlert, customMessage);
    return false;
  }
  
  try {
    await action();
    return true;
  } catch (error) {
    console.error('Error executing authenticated action:', error);
    return false;
  }
};

/**
 * Hook-like function để sử dụng trong components
 * @param navigation - Navigation object
 * @param showAlert - Function để hiển thị CustomAlert
 * @param hideAlert - Function để ẩn CustomAlert
 */
export const useAuthCheck = (
  navigation: any,
  showAlert: (
    type: 'success' | 'error' | 'info' | 'confirm',
    title: string,
    message: string,
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[]
  ) => void,
  hideAlert: () => void
) => {
  return {
    checkAuth: () => checkAuthentication(),
    requireAuth: (action: () => void | Promise<void>, customMessage?: string) =>
      requireAuth(navigation, showAlert, hideAlert, action, customMessage),
    showLoginAlert: (customMessage?: string) =>
      showLoginRequiredAlert(navigation, showAlert, hideAlert, customMessage),
  };
};
