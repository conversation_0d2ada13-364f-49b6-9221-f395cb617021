import React from 'react';
import {
  SafeAreaView,
  View,
  StatusBar,
  ImageBackground,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import Header from 'components/Header';
import HeaderIcon from 'components/Header/HeaderIcon';
import { useNavigation } from '@react-navigation/native';
import { useCart } from 'hooks/contexts/CartContext';
import commonStyles from 'themes/commonStyles';
import metrics from 'themes/metrics';

type Props = {
  noStretch?: boolean;
  backButton?: boolean;
  title?: string;
  rightComponent?: React.ReactElement | null;
  onBackPress?: () => void;
};

const HeaderRightIcon = () => {
  const navigation = useNavigation();
  const { cartCount } = useCart();

  const handlePress = () => {
    // Thêm kiểm tra navigation tồn tại
    if (navigation) {
      navigation.navigate('Cart'); 
      // Hoặc thử navigation.dispatch(CommonActions.navigate('Cart'))
    }
  };

  return (
    <View style={styles.iconsContainer}>
      <TouchableOpacity 
        onPress={handlePress}
        style={styles.iconWrapper}
        activeOpacity={0.7} // Thêm hiệu ứng khi nhấn
      >
        <HeaderIcon
        containerStyle={styles.mHorizontal}
        iconName="shopping-cart-outline"
        onPress={() => navigation?.navigate('Cart')}
      />
        {cartCount > 0 && (
          <View style={styles.badge}>
            <Text style={styles.badgeText}>
              {cartCount > 9 ? '9+' : cartCount}
            </Text>
          </View>
        )}
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.iconWrapper}
        activeOpacity={0.7}
      >
        {/* <HeaderIcon
          iconName="bell"
          size={24}
          color="#000"
        /> */}
      </TouchableOpacity>
    </View>
  );
};

const HomeHeader = ({
  noStretch,
  backButton,
  onBackPress,
  title,
  rightComponent = <HeaderRightIcon />,
}: Props) => {
  const navigation = useNavigation();
  
  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffffff" />
      <ImageBackground
        imageStyle={{
          resizeMode: noStretch ? 'cover' : 'stretch',
          alignSelf: 'flex-start',
        }}
        source={require('assets/images/header-background.png')}>
        <SafeAreaView style={styles.safeAreaView} />
        <Header
          title={title}
          backButton={backButton}
          onBackPress={onBackPress ? onBackPress : () => navigation.goBack()}
          rightComponent={rightComponent}
        />
        {!noStretch && <View style={styles.headerSpacing} />}
      </ImageBackground>
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: { flex: 0 },
  iconsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconWrapper: {
    marginHorizontal: 1, 
    padding: 2,
  },
  badge: {
    position: 'absolute',
    right: -4,
    top: -4,
    backgroundColor: 'red',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  headerSpacing: {
    paddingBottom: 200,
  },
});

export default HomeHeader;