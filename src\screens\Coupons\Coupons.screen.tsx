/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useRef, useEffect } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  View,
  Text,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import { useCoupons } from './hooks';
import HomeHeader from 'components/Header/HomeHeader';
import CustomAlert from 'utils/widgets/CustomAlert';
import CouponCard from './components/CouponCard';
import CouponDetailPopup from './components/CouponDetailPopup';
import colors from 'themes/colors';
import fonts from 'themes/fonts';
import metrics from 'themes/metrics';
import { Coupon, CollectedCoupon } from 'types/Coupon';

type RootStackParamList = {
  Coupons: undefined;
  Cart: undefined;
  Checkout: { couponId: number };
};

type NavigationProps = NativeStackNavigationProp<RootStackParamList>;

const CouponsScreen: React.FC = () => {
  const {
    activeCoupons,
    collectedCoupons,
    loading,
    error,
    totalPages,
    currentPage,
    setCurrentPage,
    fetchCollectedCoupons,
    fetchCouponDetail,
  } = useCoupons('HATHYO', 0, 10);
  const navigation = useNavigation<NavigationProps>();
  const [activeTab, setActiveTab] = useState<'active' | 'collected'>('active');
  const [alert, setAlert] = useState<{
    visible: boolean;
    type: 'success' | 'error' | 'info' | 'confirm';
    title: string;
    message: string;
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[];
  } | null>(null);
  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | null>(null);
  const [isPopupVisible, setPopupVisible] = useState(false);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.98)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 350,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 9,
        tension: 90,
        useNativeDriver: true,
      }),
    ]).start();
    return () => {
      fadeAnim.setValue(0);
      scaleAnim.setValue(0.98);
    };
  }, [fadeAnim, scaleAnim]);

  useEffect(() => {
    if (error) {
      showAlert('error', 'Lỗi', error, [{ text: 'OK', onPress: hideAlert }]);
    }
  }, [error]);

  const showAlert = (
    type: 'success' | 'error' | 'info' | 'confirm',
    title: string,
    message: string,
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[]
  ) => {
    setAlert({ visible: true, type, title, message, buttons });
  };

  const hideAlert = () => {
    setAlert(null);
  };

  const handleCouponPress = async (coupon: Coupon) => {
    try {
      const couponDetail = await fetchCouponDetail(coupon.id);
      if (couponDetail) {
        setSelectedCoupon(couponDetail);
        setPopupVisible(true);
      }
    } catch (err) {
      showAlert('error', 'Lỗi', 'Không thể tải chi tiết coupon. Vui lòng thử lại.', [
        { text: 'OK', onPress: hideAlert },
      ]);
    }
  };

  const renderCoupon = ({ item }: { item: Coupon | CollectedCoupon }) => {
    const coupon = 'coupon' in item ? item.coupon : item;
    if (!coupon.id || !coupon.code) {
      console.warn('Invalid coupon:', coupon);
      return null;
    }
    return (
      <Animated.View style={{ opacity: fadeAnim, transform: [{ scale: scaleAnim }] }}>
        <CouponCard
          coupon={coupon}
          onGoToCart={() => navigation.navigate('Cart')}
          onBuyNow={() => navigation.navigate('Checkout', { couponId: coupon.id })}
          onPress={() => handleCouponPress(coupon)}
        />
      </Animated.View>
    );
  };

  const validCoupons = activeTab === 'active' ? activeCoupons : collectedCoupons;

  if (loading && !validCoupons.length) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors['Primary/600']} />
        <Text style={styles.loadingText}>Đang tải coupon...</Text>
      </SafeAreaView>
    );
  }

  if (error && !validCoupons.length) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <MaterialCommunityIcons
          name="alert-circle-outline"
          size={60}
          color={colors['Danger/600']}
          style={styles.errorIcon}
        />
        <Text style={styles.errorText}>{error}</Text>
        <View style={styles.errorButtonContainer}>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => navigation.goBack()}
            activeOpacity={0.7}
          >
            <MaterialCommunityIcons
              name="arrow-left"
              size={18}
              color={colors.white}
              style={styles.buttonIcon}
            />
            <Text style={styles.retryButtonText}>Quay lại</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={fetchCollectedCoupons}
            activeOpacity={0.7}
          >
            <MaterialCommunityIcons
              name="refresh"
              size={18}
              color={colors.white}
              style={styles.buttonIcon}
            />
            <Text style={styles.retryButtonText}>Thử lại</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeAreaView}>
     <HomeHeader
  title="Mã giảm giá"
  backButton
  noStretch
  onBackPress={() => {
    console.log('Back pressed in CouponsScreen, state:', navigation.getState());
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.navigate('AppTab'); 
    }
  }}
/>
      <FlatList
        data={validCoupons}
        renderItem={renderCoupon}
        keyExtractor={(item) => ('coupon' in item ? item.coupon.id : item.id).toString()}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={
          <Text style={styles.emptyText}>
            {activeTab === 'active' ? 'Không có coupon hoạt động.' : 'Bạn chưa thu thập coupon nào.'}
          </Text>
        }
      />
      {activeTab === 'active' && totalPages > 1 && (
        <View style={styles.paginationContainer}>
          <TouchableOpacity
            style={[styles.paginationButton, currentPage === 0 && styles.disabledButton]}
            onPress={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 0}
            activeOpacity={0.7}
          >
            <Text style={styles.paginationText}>Trước</Text>
          </TouchableOpacity>
          <Text style={styles.paginationText}>
            Trang {currentPage + 1}/{totalPages}
          </Text>
          <TouchableOpacity
            style={[styles.paginationButton, currentPage >= totalPages - 1 && styles.disabledButton]}
            onPress={() => setCurrentPage(currentPage + 1)}
            disabled={currentPage >= totalPages - 1}
            activeOpacity={0.7}
          >
            <Text style={styles.paginationText}>Tiếp</Text>
          </TouchableOpacity>
        </View>
      )}
      {alert && (
        <CustomAlert
          visible={alert.visible}
          type={alert.type}
          title={alert.title}
          message={alert.message}
          buttons={alert.buttons}
          onDismiss={hideAlert}
          dismissable={alert.type !== 'success'}
        />
      )}
      <CouponDetailPopup
        visible={isPopupVisible}
        coupon={selectedCoupon}
        onClose={() => setPopupVisible(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
    backgroundColor: '#F9FBF7',
  },
  gradientOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '50%',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing2,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E6ECE0',
    elevation: 2,
    shadowColor: '#4A7043',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  tabButton: {
    flex: 1,
    paddingVertical: metrics.spacing1,
    paddingHorizontal: metrics.spacing2,
    borderRadius: metrics.radius2,
    marginHorizontal: metrics.spacing1,
    backgroundColor: '#E6ECE0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTab: {
    backgroundColor: '#4A7043',
    elevation: 2,
    shadowColor: '#4A7043',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 2,
  },
  tabText: {
    ...fonts.style.normal,
    color: '#6B8E23',
    fontWeight: '600',
    fontSize: 14,
  },
  activeTabText: {
    color: '#FFFFFF',
    fontWeight: '700',
  },
  listContainer: {
    padding: metrics.spacing2,
    paddingBottom: metrics.spacing3,
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: metrics.spacing1,
    paddingHorizontal: metrics.spacing2,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E6ECE0',
  },
  paginationButton: {
    paddingVertical: metrics.spacing1,
    paddingHorizontal: metrics.spacing2,
    borderRadius: metrics.radius2,
    backgroundColor: '#4A7043',
    marginHorizontal: metrics.spacing1,
  },
  disabledButton: {
    backgroundColor: '#A8B5A2',
  },
  paginationText: {
    ...fonts.style.normal,
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
  },
  emptyText: {
    ...fonts.style.description,
    color: '#6B8E23',
    textAlign: 'center',
    padding: metrics.spacing3,
    fontSize: 15,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FBF7',
  },
  loadingText: {
    ...fonts.style.description,
    color: '#6B8E23',
    marginTop: metrics.spacing2,
    fontSize: 15,
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: metrics.spacing3,
    backgroundColor: '#F9FBF7',
  },
  errorIcon: {
    marginBottom: metrics.spacing2,
  },
  errorText: {
    ...fonts.style.description,
    color: '#C0392B',
    textAlign: 'center',
    marginBottom: metrics.spacing2,
    fontSize: 15,
    fontWeight: '500',
  },
  errorButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: metrics.spacing2,
  },
  retryButton: {
    flexDirection: 'row',
    backgroundColor: '#4A7043',
    paddingVertical: metrics.spacing1,
    paddingHorizontal: metrics.spacing3,
    borderRadius: metrics.radius2,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2,
    shadowColor: '#4A7043',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 3,
  },
  retryButtonText: {
    ...fonts.style.normal,
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
    marginLeft: metrics.spacing1,
  },
  buttonIcon: {
    marginRight: metrics.spacing1,
  },
});

export default CouponsScreen;