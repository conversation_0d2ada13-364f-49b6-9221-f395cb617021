import React, { ReactElement } from 'react';
import { Text, TouchableOpacity, View, StyleSheet, ViewStyle } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import colors from 'themes/colors';

type Props = {
  iconName?: string;
  title: string;
  imgIcon?: ReactElement;
  onPress?: () => void;
  customStyles?: ViewStyle;
  iconComponent?: React.ComponentType<any>;
  iconSize?: number;
};

const MenuButton = ({
  iconName = 'help-circle-outline',
  title,
  imgIcon,
  onPress,
  customStyles,
  iconComponent: IconComponent = Icon,
  iconSize = 20, // Giảm nhẹ kích thước icon
}: Props) => (
  <TouchableOpacity
    onPress={onPress}
    style={[styles.container, customStyles]}
    activeOpacity={0.7}
  >
    <View style={styles.iconContainer}>
      {imgIcon || <IconComponent name={iconName} size={iconSize} color={colors['Moss/400']} />}
    </View>
    <Text style={styles.title} numberOfLines={2}>{title}</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 6, // Giảm padding
    borderRadius: 8,
    backgroundColor: colors.white,
    shadowColor: colors['Gray/700'],
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08, // Nhẹ hơn một chút
    shadowRadius: 1.5,
    elevation: 1,
    marginBottom: 6, // Gọn hơn
  },
  iconContainer: {
    width: 40, // Nhỏ hơn một chút
    height: 40,
    borderRadius: 20,
    backgroundColor: colors['Moss/50'],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 6,
  },
  title: {
    fontSize: 11.5, // Nhỏ hơn một chút
    fontWeight: '500',
    color: colors['Gray/700'],
    textAlign: 'center',
    lineHeight: 15,
    minHeight: 28,
    paddingHorizontal: 2,
  },
});

export default MenuButton;
