import React, { memo } from 'react';
import { SafeAreaView, StyleSheet } from 'react-native';
import { get } from 'lodash'; // Using original import to avoid errors
import HomeHeader from 'components/Header/HomeHeader';
import AddressPanel from './components/AddressPanel';

const AddressDetail = ({ route }) => {
  const address = get(route, 'params.address');
  const addressId = get(route, 'params.addressId');
  
  return (
    <>
      <HomeHeader
        rightComponent={null}
        title={address || addressId ? 'Sửa địa chỉ' : 'Thêm địa chỉ'}
        backButton
        noStretch
      />
      <SafeAreaView style={styles.safeAreaView}>
        <AddressPanel address={address} addressId={addressId} />
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
    backgroundColor: 'transparent',
  },
});

// Use memo to prevent unnecessary re-renders
export default memo(AddressDetail);