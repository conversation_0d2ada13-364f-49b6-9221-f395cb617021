export interface Coupon {
  id: number;
  code: string;
  type: string;
  discountType: string | null;
  discountPercent?: number | null;
  discountValue?: number | null;
  minimumPriceApply: number;
  maxDiscountPrice: number | null;
  title: string;
  description: string;
  image: string;
  expiredAt: string;
  applyStatus: boolean | null;
}

export interface CollectedCoupon {
  couponId: number;
  coupon: Coupon;
}