import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import MenuButton from 'components/MenuButton';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import { useCategoriesMenu } from './hooks';
import { useNavigation } from '@react-navigation/native';
import { isEmpty, map } from 'lodash';
import { useProductsStore } from 'stores/products';

const itemWidth =
  (metrics.fullWidth - metrics.spacing4 * 2 - metrics.spacing2 * 3) / 4;
const ShopMenu = ({
  id = null,
  name = '<PERSON>h mục sản phẩm',
  customStyles,
}: any) => {
  const { categories }: any = useCategoriesMenu({ id });
  const setFilter = useProductsStore((state: any) => state?.setFilter);
  const filter = useProductsStore((state: any) => state?.filter);
  const navigation = useNavigation();

  if (isEmpty(categories)) {
    return null;
  }

  const onPressCategory = (category: any) => {
    setFilter({ ...filter, categoryId: category?.id });
    navigation?.navigate('ProductFilter');
  };

  return (
    <View style={[styles.container, customStyles]}>
      {name && <Text style={styles.title}>{name}</Text>}
      <View style={styles.row}>
        {map(categories, (category): any => (
          <MenuButton
            key={category?.id}
            customStyles={{ width: itemWidth }}
            // iconName="fast-food-outline"
            title={category?.name}
            onPress={() => onPressCategory(category)}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  row: {
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexDirection: 'row',
    gap: metrics.spacing2,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
});

export default ShopMenu;
