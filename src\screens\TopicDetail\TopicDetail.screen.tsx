import React from 'react';
import { SafeAreaView, StyleSheet, ScrollView } from 'react-native';

import HomeHeader from 'components/Header/HomeHeader';
import PostByTopic from './components/PostByTopic';
import metrics from 'themes/metrics';
import PostMenu from 'screens/Post/components/PostMenu';
import { useNavigation } from '@react-navigation/native';
import Breadcrumbs from './components/Breadcrumbs';

const TopicDetail = ({ route }: any) => {
  const topicId = route?.params?.id;
  const name = route?.params?.name;
  const treeId = route?.params?.treeId;
  const navigation = useNavigation();

  return (
    <>
      <HomeHeader
        noStretch
        backButton
        onBackPress={() => navigation?.goBack()}
        title="Danh mục bài viết"
        rightComponent={null}
      />
      <SafeAreaView style={styles.safeAreaView}>
        <ScrollView style={styles.outerWrapper}>
          <Breadcrumbs treeId={treeId} />
          <PostMenu
            customStyles={styles.postMenu}
            id={topicId}
            name={''}
            treeId={treeId}
          />
          <PostByTopic title={`Bài viết về: ${name}`} id={topicId} size={20} />
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: { flex: 1 },
  outerWrapper: {
    flex: 1,
    paddingTop: metrics.spacing4,
  },
  postMenu: {
    paddingTop: 0,
    paddingBottom: metrics.spacing4,
  },
});

export default TopicDetail;
