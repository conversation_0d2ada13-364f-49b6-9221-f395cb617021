import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import RowItem from 'components/RowItem';
import HomeHeader from 'components/Header/HomeHeader';

// Define navigation param list
type RootStackParamList = {
  Profile: undefined;
  ResetPassword: undefined;
  ForgotPassword: undefined;
  DeleteAccount: undefined;
};

// Define navigation prop type
type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const AccountManager: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();

  return (
    <View style={styles.container}>
      <HomeHeader 
        title="Quản lí tài khoản" 
        backButton 
        noStretch
        onBackPress={() => navigation.goBack()}
      />
        <View style={styles.itemContainer}>
          <RowItem
            title="Thông tin cá nhân"
            leftIconName="account-circle-outline"
            onPress={() => navigation.navigate('Profile')}
          />
           {/* <RowItem
            title="Đặt lại mật khẩu"
            leftIconName="lock-reset"
            onPress={() => navigation.navigate('ChangePassword')}
          /> */}
         {/* <RowItem
            title="Quên mật khẩu"
            leftIconName="lock-question"
            onPress={() => navigation.navigate('ForgotPassword')}
          /> */}
          <RowItem
            title="Xóa tài khoản"
            leftIconName="account-remove-outline"
            onPress={() => navigation.navigate('ConfirmDeleteAccount')}
          />
        </View>
      </View>
  );
};

const styles = StyleSheet.create({
 
  content: {
    paddingVertical: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
  },
  itemContainer: {
    paddingHorizontal: metrics.spacing6,
    marginBottom: metrics.spacing3,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
});

export default AccountManager;