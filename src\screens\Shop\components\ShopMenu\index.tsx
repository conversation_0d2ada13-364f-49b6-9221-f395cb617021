import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import Ionicons from 'react-native-vector-icons/Ionicons';
import MenuButton from 'components/MenuButton';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import { useCategoriesMenu } from './hooks';
import { useNavigation } from '@react-navigation/native';
import { isEmpty, map } from 'lodash';
import { useProductsStore } from 'stores/products';

const itemWidth = (metrics.fullWidth - metrics.spacing4 * 2 - metrics.spacing2 * 3) / 4;

// 8 Main categories with correct icon names & components
const MAIN_CATEGORY_ICONS: Record<number, { iconName: string; iconComponent: any }> = {
  354: { iconName: 'server', iconComponent: FontAwesome5 },             // Voucher, dịch vụ
  58: { iconName: 'fast-food-outline', iconComponent: Ionicons },       // Thực phẩm, đồ uống
  13: { iconName: 'futbol', iconComponent: FontAwesome5 },          // Thể thao, du lịch
  47: { iconName: 'logo-electron', iconComponent: Ionicons },           // Thiết bị điện tử
  86: { iconName: 'fitness-outline', iconComponent: Ionicons },         // Sức khỏe
  195: { iconName: 'home-outline', iconComponent: Ionicons },           // Nhà cửa, đời sống
  236: { iconName: 'rose-outline', iconComponent: Ionicons },           // Mẹ và Bé
  121: { iconName: 'cut', iconComponent: FontAwesome5 },                // Làm đẹp
};

const MAIN_CATEGORY_IDS = Object.keys(MAIN_CATEGORY_ICONS).map(Number);

const ShopMenu = ({ id = null, name = 'Danh mục sản phẩm', customStyles }: any) => {
  const { categories } = useCategoriesMenu({ id });
  const setFilter = useProductsStore((state: any) => state.setFilter);
  const filter = useProductsStore((state: any) => state.filter);
  const navigation = useNavigation();

  if (isEmpty(categories)) return null;

  const handleCategoryPress = (category: any) => {
    setFilter({
      ...filter,
      categoryId: category.id,
      categoryName: category.name,
    });
    navigation.navigate('ProductFilter');
  };

  const getCategoryIcon = (categoryId: number) => {
    return MAIN_CATEGORY_ICONS[categoryId] || { iconName: 'shopping-bag', iconComponent: FontAwesome5 };
  };

  const mainCategories = categories.filter((category: any) =>
    MAIN_CATEGORY_IDS.includes(category.id)
  );

  const displayCategories = [
    ...mainCategories,
    { id: 'view-all', name: 'Xem tất cả', isViewAll: true },
  ];

  return (
    <View style={[styles.container, customStyles]}>
      {name && <Text style={styles.title}>{name}</Text>}
      <View style={styles.row}>
        {map(displayCategories, (category) => {
          const { iconName, iconComponent } = category.isViewAll
            ? { iconName: 'th-list', iconComponent: FontAwesome5 }
            : getCategoryIcon(category.id);

          return (
            <MenuButton
              key={category.id}
              customStyles={{ width: itemWidth }}
              iconName={iconName}
              iconComponent={iconComponent}
              title={category.name}
              onPress={() =>
                category.isViewAll
                  ? navigation.navigate('AllCategories')
                  : handleCategoryPress(category)
              }
            />
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: metrics.spacing2,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Gray/700'],
    marginBottom: metrics.spacing4,
  },
});

export default ShopMenu;
