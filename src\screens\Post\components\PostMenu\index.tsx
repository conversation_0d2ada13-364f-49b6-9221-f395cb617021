import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import MenuButton from 'components/MenuButton';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import { usePostMenu } from './hooks';
import { isEmpty, map } from 'lodash';
import { useNavigation } from '@react-navigation/native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

const itemWidth =
  (metrics.fullWidth - metrics.spacing4 * 2 - metrics.spacing2 * 3) / 4;

const iconMap: any = {
  '1': (
    <MaterialCommunityIcons
      name="virus-outline"
      size={24}
      color={colors['Moss/400']}
    />
  ),
  '2': 'fast-food-outline',
  '3': 'heart-circle-outline',
  '4': (
    <MaterialCommunityIcons
      name="run-fast"
      size={24}
      color={colors['Moss/400']}
    />
  ),
};

const PostMenu = ({
  id = null,
  name = '<PERSON>h mục bài viết',
  treeId,
  customStyles,
}: any) => {
  const { topics }: any = usePostMenu({ id });
  const navigation = useNavigation();

  if (isEmpty(topics)) {
    return null;
  }

  return (
    <View style={[styles.container, customStyles]}>
      {name && <Text style={styles.title}>{name}</Text>}
      <View style={styles.row}>
        {map(topics, topic => {
          const icon = iconMap[topic?.id];

          return (
            <MenuButton
              key={topic?.id}
              customStyles={{ width: itemWidth }}
              iconName={typeof icon === 'string' ? icon : undefined}
              imgIcon={typeof icon !== 'string' ? icon : undefined}
              title={topic?.name}
              onPress={() =>
                navigation?.navigate('TopicDetail', {
                  id: topic?.id,
                  treeId: treeId ? `${treeId}/${topic?.id}` : `${topic?.id}`,
                  name: topic?.name,
                })
              }
            />
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  row: {
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexDirection: 'row',
    gap: metrics.spacing2,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
});

export default PostMenu;
