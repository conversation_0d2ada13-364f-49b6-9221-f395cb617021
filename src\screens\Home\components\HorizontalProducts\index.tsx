import React from "react";
import ProductGrid from "components/shared/ProductGrid";
import { useHomeProducts } from "./hooks";

type Props = {
  hasBackground?: boolean;
  filter?: boolean;
  title?: string;
  customStyles?: any;
};

const VerticalProducts = ({ filter, title, hasBackground, customStyles }: Props) => {
  const { products, loading, loadingMore, hasMore, fetchProducts } = useHomeProducts();

  const handleLoadMore = () => {
    if (hasMore && !loadingMore && fetchProducts) {
      fetchProducts();
    }
  };

  return (
    <ProductGrid
      products={products}
      loading={loading}
      loadingMore={loadingMore}
      hasMore={hasMore}
      title={title}
      hasBackground={hasBackground}
      filter={filter}
      customStyles={customStyles}
      onLoadMore={handleLoadMore}
    />
  );
};

export default VerticalProducts;
