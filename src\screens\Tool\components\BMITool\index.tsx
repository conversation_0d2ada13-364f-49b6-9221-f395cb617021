import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { Input } from '@ui-kitten/components';

import CommonButton from 'components/CommonButton';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';

const BMITool = () => {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.rowItem}>
          <View>
            <Text style={styles.title}>
              Tính chỉ số BMI - Chỉ số khối cơ thể
            </Text>

            <Text style={styles.subTitle}>
              Tham vấn y khoa: <PERSON><PERSON><PERSON> sĩ <PERSON>ễ<PERSON>nh
            </Text>
            <Text style={styles.subTitle}>Ngày 02/02/2024</Text>
          </View>
          <Icon color={colors['Moss/600']} name="scale-balance" size={60} />
        </View>
      </View>
      <View style={styles.content}>
        <Text style={styles.label}>Giới tính của bạn</Text>
        <View style={styles.formItem}>
          <CommonButton
            customStyles={commonStyles.flex1}
            appearance="outline"
            status="basic">
            Nam
          </CommonButton>
          <CommonButton
            customStyles={commonStyles.flex1}
            appearance="outline"
            status="basic">
            Nữ
          </CommonButton>
        </View>
        <Text style={styles.label}>Bạn bao nhiêu tuổi</Text>
        <View style={styles.formItem}>
          <Input style={styles.input} placeholder="Số tuổi" />
        </View>
        <Text style={styles.label}>Bạn cao bao nhiêu</Text>
        <View style={styles.formItem}>
          <Input style={styles.input} placeholder="Chiều cao" />
        </View>
        <Text style={styles.label}>Bạn bao nhiêu tuổi</Text>
        <View style={styles.formItem}>
          <Input style={styles.input} placeholder="Số tuổi" />
        </View>
        <View style={commonStyles.rowCenter}>
          <CommonButton>Tính ngay</CommonButton>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
    marginBottom: metrics.spacing4,
  },
  header: {
    padding: metrics.spacing4,
    backgroundColor: colors['Moss/400'],
    borderTopLeftRadius: metrics.radius4,
    borderTopRightRadius: metrics.radius4,
  },
  content: {
    padding: metrics.spacing4,
    backgroundColor: 'white',
    borderBottomLeftRadius: metrics.radius4,
    borderBottomRightRadius: metrics.radius4,
  },
  label: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing2,
  },
  input: {
    flex: 1,
    backgroundColor: 'white',
  },
  item: {
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',
  },
  rowItem: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    gap: metrics.spacing4,
  },
  formItem: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: metrics.spacing4,
    gap: metrics.spacing4,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: 'white',
    marginBottom: metrics.spacing4,
  },
  subTitle: {
    fontSize: fonts.size.input,
    fontWeight: '400',
    color: 'white',
    marginBottom: metrics.spacing4,
  },
});

export default BMITool;
