import React from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import { Divider, Text } from '@ui-kitten/components';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';

import metrics from 'themes/metrics';
import colors from 'themes/colors';
import fonts from 'themes/fonts';
import PostCard from 'components/PostCard';

// Định nghĩa type cho Stack Navigator
type RootStackParamList = {
  PostDetail: { id: number | string };
  AppTab: undefined;
};

type NavigationProps = NativeStackNavigationProp<RootStackParamList>;

interface PostCardListProps {
  type?: 'vertical' | 'horizontal';
  posts: any[];
  onPress?: (id: number) => void;
}

const PostCardList: React.FC<PostCardListProps> = ({ type = 'vertical', posts, onPress }) => {
  const navigation = useNavigation<NavigationProps>();
  const [isNavigating, setIsNavigating] = React.useState(false);

  const renderItem = ({ item, index }: { item: any; index: number }) => (
    <View style={type === 'horizontal' ? styles.cardHorizontal : styles.cardVertical}>
      <PostCard
        title={item.title}
        author={item.author}
        uri={item.thumbnail}
        avatarUri={item.thumbnail}
        topicName={item?.topic?.name}
        hideTime={true}
        onPress={() => {
          if (item.id && !isNavigating) {
            setIsNavigating(true);
            console.log('PostCardList - Navigating to PostDetail with ID:', item.id);
            if (onPress) {
              onPress(item.id);
            } else {
              navigation.push('PostDetail', { id: item.id }); 
            }
            setTimeout(() => setIsNavigating(false), 1000); 
          } else {
            console.warn('PostCardList - Invalid post ID or navigating:', item);
          }
        }}
      />
      {type === 'vertical' && index < posts.length - 1 && <Divider style={styles.divider} />}
    </View>
  );

  if (!posts || posts.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>Không có bài viết liên quan</Text>
      </View>
    );
  }

  return (
    <FlatList
      data={posts}
      renderItem={renderItem}
      keyExtractor={(item) => item.id.toString()}
      horizontal={type === 'horizontal'}
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={[
        styles.listContainer,
        type === 'horizontal' ? styles.horizontalContainer : styles.verticalContainer,
      ]}
    />
  );
};

const styles = StyleSheet.create({
  listContainer: {
    paddingHorizontal: metrics.spacing2,
  },
  horizontalContainer: {
    paddingVertical: metrics.spacing2,
  },
  verticalContainer: {
    paddingVertical: metrics.spacing2,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
  },
  cardHorizontal: {
    marginRight: metrics.spacing2,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
    elevation: 2,
    overflow: 'hidden',
  },
  cardVertical: {
    width: '100%',
  },
  divider: {
    marginVertical: metrics.spacing2,
    backgroundColor: colors['Gray/200'],
  },
  emptyContainer: {
    padding: metrics.spacing4,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: fonts.size.medium,
    color: colors['Grayiron/600'],
  },
});

export default PostCardList;