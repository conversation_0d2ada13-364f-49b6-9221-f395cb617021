import React, { memo, useMemo, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Switch, Text, TextInput, ActivityIndicator } from 'react-native';
import { Picker } from '@react-native-picker/picker';
import { Controller } from 'react-hook-form';
import { useNavigation } from '@react-navigation/native';
import CommonButton from 'components/CommonButton';
import cities from 'assets/city.json';
import districts from 'assets/district.json';
import wards from 'assets/ward.json';
import { useAddressForm } from './hook';
import colors from 'themes/colors';


const AddressPanel = ({ address, addressId }) => {
  const navigation = useNavigation();
  const {
    control,
    handleSubmit,
    watch,
    errors,
    selectedProvince,
    selectedDistrict,
    onSubmit,
    setValue,
    fetchError,
    isLoading,
    isNewAddress,
  } = useAddressForm(address, addressId);

  const filteredDistricts = useMemo(() => {
    const result = districts.filter((district) => Number(district.city_id) === Number(selectedProvince));
    console.log('filteredDistricts:', result);
    return result;
  }, [selectedProvince]);

  const filteredWards = useMemo(() => {
    const result = wards.filter((ward) => Number(ward.districtId) === Number(selectedDistrict));
    console.log('filteredWards:', result);
    return result;
  }, [selectedDistrict]);

  useEffect(() => {
    console.log('selectedProvince:', selectedProvince);
    console.log('selectedDistrict:', selectedDistrict);
  }, [selectedProvince, selectedDistrict]);

  const handleCityChange = (cityId) => {
    console.log('handleCityChange called with cityId:', cityId);
    if (cityId === 0) {
      setValue('provinceId', 0);
      setValue('customerProvince', '');
      setValue('districtId', 0);
      setValue('customerDistrict', '');
      setValue('wardId', 0);
      setValue('customerWard', '');
      return;
    }
    const selectedCity = cities.find(city => Number(city.id) === cityId);
    if (selectedCity) {
      setValue('provinceId', cityId);
      setValue('customerProvince', selectedCity.name);
      setValue('districtId', 0);
      setValue('customerDistrict', '');
      setValue('wardId', 0);
      setValue('customerWard', '');
    } else {
      console.warn(`City with ID ${cityId} not found`);
    }
  };

  const handleDistrictChange = (districtId) => {
    console.log('handleDistrictChange called with districtId:', districtId);
    const parsedDistrictId = Number(districtId);
    if (parsedDistrictId === 0) {
      setValue('districtId', 0);
      setValue('customerDistrict', '');
      setValue('wardId', 0);
      setValue('customerWard', '');
      return;
    }
    const selectedDistrict = districts.find(district => Number(district.id) === parsedDistrictId);
    if (selectedDistrict) {
      setValue('districtId', parsedDistrictId);
      setValue('customerDistrict', selectedDistrict.name);
      setValue('wardId', 0);
      setValue('customerWard', '');
    } else {
      console.warn(`District with ID ${parsedDistrictId} not found`);
    }
  };

  const handleWardChange = (wardId) => {
    console.log('handleWardChange called with wardId:', wardId);
    if (wardId === 0) {
      setValue('wardId', 0);
      setValue('customerWard', '');
      return;
    }
    const selectedWard = wards.find(ward => Number(ward.id) === wardId);
    if (selectedWard) {
      setValue('wardId', wardId);
      setValue('customerWard', selectedWard.name);
    } else {
      console.warn(`Ward with ID ${wardId} not found`);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
        <Text style={styles.loadingText}>Đang tải địa chỉ...</Text>
      </View>
    );
  }

  if (fetchError) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{fetchError}</Text>
        <CommonButton onPress={() => navigation.goBack()}>Quay lại</CommonButton>
      </View>
    );
  }

  if (!cities.length) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Không có dữ liệu tỉnh/thành phố.</Text>
        <CommonButton onPress={() => navigation.goBack()}>Quay lại</CommonButton>
      </View>
    );
  }

  const wardId = Number(watch('wardId'));
  const isCityMissing = selectedProvince && !cities.find(city => Number(city.id) === selectedProvince);
  const isDistrictMissing = selectedDistrict && !districts.find(district => Number(district.id) === selectedDistrict);
  const isWardMissing = wardId && !wards.find(ward => Number(ward.id) === wardId);

  if (isCityMissing || isDistrictMissing || isWardMissing) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>
          {isCityMissing ? 'Tỉnh/thành phố không hợp lệ' : 
           isDistrictMissing ? 'Quận/huyện không hợp lệ' : 
           'Phường/xã không hợp lệ'}
        </Text>
        <CommonButton onPress={() => navigation.goBack()}>Quay lại</CommonButton>
      </View>
    );
  }

  if (selectedProvince > 0 && filteredDistricts.length === 0) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>
          Không tìm thấy quận/huyện cho tỉnh/thành phố này (ID: {selectedProvince}).
        </Text>
        <CommonButton onPress={() => navigation.goBack()}>Quay lại</CommonButton>
      </View>
    );
  }

  if (selectedDistrict > 0 && filteredWards.length === 0) {
    console.warn(`No wards found for district ID: ${selectedDistrict}`);
  }

  return (
    <View style={styles.container}>
      <ScrollView>
        <Controller
          control={control}
          name="receiverName"
          render={({ field: { onChange, value } }) => (
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Họ và tên *</Text>
              <TextInput
                style={styles.input}
                value={value}
                onChangeText={onChange}
                placeholder="Nhập họ và tên"
              />
              {errors.receiverName && (
                <Text style={styles.error}>{errors.receiverName.message}</Text>
              )}
            </View>
          )}
        />

        <Controller
          control={control}
          name="receiverPhoneNumber"
          render={({ field: { onChange, value } }) => (
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Số điện thoại *</Text>
              <TextInput
                style={styles.input}
                value={value}
                onChangeText={onChange}
                placeholder="Nhập số điện thoại"
                keyboardType="phone-pad"
              />
              {errors.receiverPhoneNumber && (
                <Text style={styles.error}>{errors.receiverPhoneNumber.message}</Text>
              )}
            </View>
          )}
        />

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Tỉnh/Thành phố *</Text>
          <Controller
            control={control}
            name="provinceId"
            render={({ field: { value } }) => (
              <View style={[styles.pickerContainer]}>
                <Picker
                  selectedValue={Number(value)}
                  onValueChange={handleCityChange}
                  style={styles.picker}
                >
                  <Picker.Item label="Chọn tỉnh/thành phố" value={0} />
                  {cities.map((city) => (
                    <Picker.Item
                      key={city.id}
                      label={city.name}
                      value={Number(city.id)}
                    />
                  ))}
                </Picker>
              </View>
            )}
          />
          {errors.customerProvince && (
            <Text style={styles.error}>{errors.customerProvince.message}</Text>
          )}
        </View>

        {selectedProvince > 0 && (
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Quận/Huyện *</Text>
            <Controller
              control={control}
              name="districtId"
              render={({ field: { value } }) => (
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={Number(value)}
                    onValueChange={handleDistrictChange}
                    style={styles.picker}
                    enabled={!!selectedProvince}
                  >
                    <Picker.Item label="Chọn quận/huyện" value={0} />
                    {filteredDistricts.map((district) => (
                      <Picker.Item
                        key={district.id}
                        label={district.name}
                        value={Number(district.id)}
                      />
                    ))}
                  </Picker>
                </View>
              )}
            />
            {errors.customerDistrict && (
              <Text style={styles.error}>{errors.customerDistrict.message}</Text>
            )}
          </View>
        )}

        {selectedDistrict > 0 && (
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Phường/Xã *</Text>
            <Controller
              control={control}
              name="wardId"
              render={({ field: { value } }) => (
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={Number(value)}
                    onValueChange={handleWardChange}
                    style={styles.picker}
                    enabled={!!selectedDistrict}
                  >
                    <Picker.Item label="Chọn phường/xã" value={0} />
                    {filteredWards.map((ward) => (
                      <Picker.Item
                        key={ward.id}
                        label={ward.name}
                        value={Number(ward.id)}
                      />
                    ))}
                  </Picker>
                </View>
              )}
            />
            {errors.customerWard && (
              <Text style={styles.error}>{errors.customerWard.message}</Text>
            )}
          </View>
        )}

        <Controller
          control={control}
          name="customerStreetAddress"
          render={({ field: { onChange, value } }) => (
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Địa chỉ cụ thể *</Text>
              <TextInput
                style={styles.input}
                value={value}
                onChangeText={onChange}
                placeholder="Nhập địa chỉ cụ thể"
              />
              {errors.customerStreetAddress && (
                <Text style={styles.error}>{errors.customerStreetAddress.message}</Text>
              )}
            </View>
          )}
        />

        <Controller
          control={control}
          name="isDefault"
          render={({ field: { onChange, value } }) => (
            <View style={styles.toggleContainer}>
              <Text style={styles.label}>Đặt làm địa chỉ mặc định</Text>
              <Switch
                value={value}
                onValueChange={onChange}
                trackColor={{ false: '#767577', true: '#81b0ff' }}
                thumbColor={value ? '#fff' : '#f4f3f4'}
              />
            </View>
          )}
        />
      </ScrollView>

      <View style={styles.buttonContainer}>
        <CommonButton
          onPress={handleSubmit(onSubmit)}
          style={styles.submitButton}
          appearance="filled"
          customStyles={{
            backgroundColor: colors['Moss/500'] || '#4CAF50',
            borderColor: colors['Moss/500'] || '#4CAF50',
          }}
          textStyle={{ color: '#fff' }}
        >
          {(address?.id || addressId) ? 'Cập nhật địa chỉ' : 'Thêm địa chỉ'}
        </CommonButton>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    marginBottom: 4,
    fontWeight: '500',
    color: colors['Gray/700'] || '#374151',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    paddingHorizontal: 12,
    fontSize: 16,
    color: colors['Gray/900'] || '#111827',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    overflow: 'hidden',
  },
  picker: {
    height: 50,
    width: '100%',
    color: colors['Gray/900'] || '#111827',
  },
  error: {
    color: colors['Danger/500'] || '#E31B2E',
    fontSize: 14,
    marginTop: 4,
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  buttonContainer: {
    marginTop: 16,
  },
  submitButton: {
    marginBottom: 10,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: colors['Gray/700'] || '#374151',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  errorText: {
    color: colors['Danger/500'] || '#E31B2E',
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
  },
});

export default memo(AddressPanel);