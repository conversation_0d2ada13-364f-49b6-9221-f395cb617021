import React from 'react';
import { View, StyleSheet, FlatList } from 'react-native';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import MenuButton from 'components/MenuButton';
import { useNavigation } from '@react-navigation/native';
import { useProductsStore } from 'stores/products';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import Header from 'components/Header';
import HomeHeader from 'components/Header/HomeHeader';

const CATEGORY_ICONS: Record<number, string> = {
  // ===== DANH MỤC CHÍNH =====
  424: "plug",                // Đi<PERSON>n tử, điện lạnh
  423: "mobile-alt",          // Điện thoại, máy tính bảng
  427: "motorcycle",          // Ô tô, xe máy, xe đạp
  58: "utensils",             // <PERSON>h<PERSON><PERSON> phẩm, đ<PERSON> uống
  377: "female",              // Thời trang nữ
  378: "male",                // Thời trang nam
  13: "futbol",               // Th<PERSON> thao, du lịch
  426: "headphones",          // Thiế<PERSON> bị số và phụ kiện
  86: "heartbeat",            // Sức khỏe
  422: "book",                // Sách, văn phòng phẩm, quà tặng
  404: "hat-cowboy",          // Phụ kiện thời trang
  195: "home",                // Nhà cửa, đời sống
  236: "baby",                // Mẹ và Bé
  425: "laptop",              // Máy tính, laptop, linh kiện
  121: "spa",                 // Làm đẹp
  354: "server",              // Dịch vụ

  // ===== DANH MỤC PHỤ =====
  // Thực phẩm
  1: "cookie",                // Bánh
  2: "wine-glass-alt",        // Rượu
  3: "beer",                  // Bia
  4: "leaf",                  // Rau củ
  5: "cheese",                // Phô mai
  6: "snowflake",             // Đông lạnh
  7: "pepper-hot",            // Gia vị
  
  // Thể thao
  9: "dumbbell",              // Gym
  10: "suitcase-rolling",     // Du lịch
  
  // Điện tử
  11: "mobile-alt",           // Điện thoại
  12: "laptop",               // Laptop
  
  // Sức khỏe
  14: "pills",                // Thuốc
  15: "pills",                // Dược phẩm
  16: "hand-holding-heart",   // Từ thiện
  
  // Nhà cửa
  17: "blender",              // Nhà bếp
  18: "couch",                // Sofa
  19: "bed",                  // Giường
  
  // Trẻ em
  20: "baby-carriage",        // Đồ trẻ em
  21: "gamepad",              // Đồ chơi
  
  // Khác
  22: "fan",                  // Quạt
  23: "cut",                  // Dụng cụ cắt
  8: "tshirt"                 // Thời trang (chung)
};
const AllCategories = () => {
  const navigation = useNavigation();
  const categories = useProductsStore((state: any) => state.categories);
  const setFilter = useProductsStore((state: any) => state.setFilter);
  const filter = useProductsStore((state: any) => state.filter)

  // Same item width calculation as ShopMenu
  const itemWidth = (metrics.fullWidth - metrics.spacing4 * 2 - metrics.spacing2 * 3) / 4;

  const handleCategoryPress = (category: any) => {
    setFilter({
      ...filter,
      categoryId: category.id,
      categoryName: category.name,
    });
    navigation.navigate('ProductFilter');
  };

  const getCategoryIcon = (categoryId: number) => {
    return CATEGORY_ICONS[categoryId] || 'shopping-bag';
  };

  const renderItem = ({ item }: { item: any }) => (
    <MenuButton
      key={item.id}
      customStyles={{ width: itemWidth }}
      iconName={getCategoryIcon(item.id)}
      iconComponent={FontAwesome5}
      title={item.name}
      onPress={() => handleCategoryPress(item)}
    />
  );

  return (
    <View style={styles.container}>
      <HomeHeader 
        title="Tất cả danh mục" 
        backButton 
        noStretch
        onBackPress={() => navigation.goBack()}
      />
      
      <FlatList
        data={categories}
        renderItem={renderItem}
        keyExtractor={(item) => item.id.toString()}
        numColumns={4}
        contentContainerStyle={styles.listContent}
        columnWrapperStyle={styles.columnWrapper}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,

  },
  listContent: {
    padding: metrics.spacing3,
    paddingBottom: metrics.spacing6,
  },
  columnWrapper: {
    justifyContent: 'flex-start',
    gap: metrics.spacing2,
    marginBottom: metrics.spacing2,
  },
});

export default AllCategories;