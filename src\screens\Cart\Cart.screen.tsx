import React, { useState, useEffect } from 'react';
import { SafeAreaView, StyleSheet, ScrollView, View, RefreshControl, Text, ActivityIndicator } from 'react-native';
import HomeHeader from 'components/Header/HomeHeader';
import VerticalProductsItems from './components/VerticalProductsItems';
import CustomAlert from 'utils/widgets/CustomAlert';
import metrics from 'themes/metrics';
import { useNavigation } from '@react-navigation/native';
import PaymentButton from './components/PaymentButton';
import colors from 'themes/colors';
import RecentProducts from './components/RecentProducts';
import useUserCart from './hooks';
import { checkAuthentication } from 'utils/authUtils';

const Cart = ({ route }) => {
  const navigation = useNavigation();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [alert, setAlert] = useState<{
    visible: boolean;
    type: 'success' | 'error' | 'info' | 'confirm';
    title: string;
    message: string;
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[];
  } | null>(null);

  const {
    loading,
    cart,
    selected,
    setSelected,
    setCart,
    products,
    refreshing,
    updatingItemId,
    handleRefresh,
    handleSelectAll,
    handleSelectByMerchant,
    handleSelectByCartItemId,
    handleDeleteCartItem,
    handleUpdateCartItem,
    syncSelectedItems
  } = useUserCart();

  const showAlert = (
    type: 'success' | 'error' | 'info' | 'confirm',
    title: string,
    message: string,
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[]
  ) => {
    setAlert({ visible: true, type, title, message, buttons });
  };

  const hideAlert = () => {
    setAlert(null);
  };

  useEffect(() => {
    const checkAuth = async () => {
      const { isAuthenticated: authStatus } = await checkAuthentication();
      setIsAuthenticated(authStatus);

      if (!authStatus) {
        showAlert(
          'info',
          'Yêu cầu đăng nhập',
          'Vui lòng đăng nhập để xem giỏ hàng của bạn',
          [
            {
              text: 'Đăng nhập',
              style: 'default',
              onPress: () => {
                hideAlert();
                navigation.navigate('Authentication');
              }
            }
          ]
        );
      }
    };

    checkAuth();
  }, [navigation]);

  if (isAuthenticated === null) {
    return (
      <>
        <HomeHeader
          noStretch
          backButton
          title="Giỏ hàng"
          rightComponent={<View />}
        />
        <SafeAreaView style={styles.safeAreaView}>
          <View style={styles.centerContent}>
            <ActivityIndicator size="large" color={colors['Moss/500']} />
            <Text style={styles.loadingText}>Đang kiểm tra đăng nhập...</Text>
          </View>
        </SafeAreaView>
      </>
    );
  }

  if (!isAuthenticated) {
    return (
      <>
        <HomeHeader
          noStretch
          backButton
          title="Giỏ hàng"
          rightComponent={<View />}
        />
        <SafeAreaView style={styles.safeAreaView}>
          <View style={styles.centerContent}>
            <Text style={styles.authRequiredText}>Vui lòng đăng nhập để xem giỏ hàng</Text>
          </View>
          {alert && (
            <CustomAlert
              visible={alert.visible}
              type={alert.type}
              title={alert.title}
              message={alert.message}
              buttons={alert.buttons}
              onDismiss={hideAlert}
              dismissable={alert.type !== 'success'}
            />
          )}
        </SafeAreaView>
      </>
    );
  }

  return (
    <>
      <HomeHeader
        noStretch
        backButton
        title="Giỏ hàng"
        rightComponent={<View />}
      />
      <SafeAreaView style={styles.safeAreaView}>
        <ScrollView 
          style={styles.outerWrapper}
          refreshControl={
            <RefreshControl 
              refreshing={refreshing} 
              onRefresh={handleRefresh} 
            />
          }
        >
          <VerticalProductsItems
            cart={cart}
            selected={selected}
            setSelected={setSelected}
            setCart={setCart}
            handleSelectAll={handleSelectAll}
            handleSelectByMerchant={handleSelectByMerchant}
            handleSelectByCartItemId={handleSelectByCartItemId}
            handleDeleteCartItem={handleDeleteCartItem}
            handleUpdateCartItem={handleUpdateCartItem}
            updatingItemId={updatingItemId}
          />
          {products && products.length > 0 && (
            <RecentProducts
              products={products}
              cart={cart}
              setCart={setCart}
              customStyles={styles.horizontalProducts}
            />
          )}
          <View style={styles.gap} />
        </ScrollView>
        <PaymentButton selected={selected} syncSelectedItems={syncSelectedItems} />
      </SafeAreaView>
      {alert && (
        <CustomAlert
          visible={alert.visible}
          type={alert.type}
          title={alert.title}
          message={alert.message}
          buttons={alert.buttons}
          onDismiss={hideAlert}
          dismissable={alert.type !== 'success'}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
    backgroundColor: 'white'
  },
  outerWrapper: {
    flex: 1,
    backgroundColor: colors['Grayiron/50'],
  },
  horizontalProducts: {
    marginHorizontal: metrics.spacing4,
    paddingHorizontal: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
  },
  gap: {
    height: 30,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: metrics.spacing4,
  },
  loadingText: {
    fontSize: 16,
    marginTop: metrics.spacing3,
    color: colors['Gray/700'],
    textAlign: 'center',
  },
  authRequiredText: {
    fontSize: 16,
    color: colors['Gray/700'],
    textAlign: 'center',
  },
});

export default Cart;