import React from 'react';
import {
  ImageBackground,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';

import styles from 'components/PostCard/styles';
import FastImage from 'react-native-fast-image';
import { Avatar, Button, Text } from '@ui-kitten/components';
import Tag from 'components/Tag';
import commonStyles from 'themes/commonStyles';
import metrics from 'themes/metrics';
import HeaderIcon from 'components/Header/HeaderIcon';
import AntDesign from 'react-native-vector-icons/AntDesign';
import LinearGradient from 'react-native-linear-gradient';
import dayjs from 'dayjs';

type Props = {
  border?: boolean;
  uri?: string;
  avatarUri?: string;
  title: string;
  topicName: string;
  author?: string;
  createdAt?: string;
  onPress?: any;
  customStyles?: ViewStyle;
  type?: 'horizontal' | 'full';
  hideTime?: boolean;
};

function PostCard({
  uri,
  avatarUri,
  title,
  topicName,
  onPress,
  customStyles,
  border,
  author,
  type,
  createdAt,
  hideTime = false,
}: Props) {
  switch (type) {
    case 'full':
      return (
        <TouchableOpacity onPress={onPress} style={customStyles}>
          <ImageBackground
            style={styles.postFullContainer}
            imageStyle={styles.imageContainer}
            source={{ uri }}>
            <View style={styles.postFullContent}>
              <View style={[commonStyles.rowSpaceBetween, styles.topCard]}>
                <Tag size="small" type="warning" title={topicName} />
              </View>
              <LinearGradient
                colors={['rgba(0, 0, 0, 0.1)', 'rgba(0, 0, 0, 0.5)']}
                style={styles.mainContent}>
                <View style={styles.row}>
                  <Avatar
                    shape="round"
                    size="small"
                    source={{ uri: avatarUri }}
                  />
                  <Text style={styles.whiteHintTitle}>{author}</Text>
                </View>
                <Text
                  style={[styles.whiteTitle, styles.paddingTop2]}
                  status="primary"
                  numberOfLines={2}>
                  {title}
                </Text>
                {/* <View style={[commonStyles.row, styles.paddingTop2]}>1
                  <TouchableOpacity style={styles.rowPaddingRight}>
                    <AntDesign size={20} name="like2" color="white" />
                    <Text style={styles.whiteTitle}>1</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.rowPaddingRight}>
                    <AntDesign size={20} name="message1" color="white" />
                    <Text style={styles.whiteTitle}>1</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.rowPaddingRight}>
                    <AntDesign size={20} name="sharealt" color="white" />
                    <Text style={styles.whiteTitle}>1</Text>
                  </TouchableOpacity>
                </View> */}
              </LinearGradient>
            </View>
          </ImageBackground>
        </TouchableOpacity>
      );
    default:
      return (
        <TouchableOpacity
          onPress={onPress}
          style={[styles.container, customStyles, border && styles.border]}>
          <FastImage style={styles.postImage} source={{ uri }} />
          <View style={styles.content}>
            <View style={hideTime ? styles.rowStart : styles.rowBetween}>
              <Tag size="small" type="warning" title={topicName} />
              {!hideTime && (
                <Text style={styles.hintTitle}>
                  {dayjs(createdAt)?.fromNow?.()}
                </Text>
              )}
            </View>
            <Text style={styles.title} status="primary" numberOfLines={2}>
              {title}
            </Text>
            <View style={commonStyles.rowCenter}>
              <Avatar shape="round" size="small" source={{ uri: avatarUri }} />
              <Text style={styles.avatarTitle}>{author}</Text>
            </View>
          </View>
        </TouchableOpacity>
      );
  }
}

export default PostCard;
