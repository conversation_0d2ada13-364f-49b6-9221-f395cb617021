{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.0.5", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@eva-design/eva": "^2.2.0", "@gorhom/bottom-sheet": "^5", "@hookform/resolvers": "^3.9.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-clipboard/clipboard": "^1.16.3", "@react-native-community/blur": "^4.4.0", "@react-native-masked-view/masked-view": "^0.3.1", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/material-top-tabs": "^6.6.5", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@react-navigation/stack": "^6.3.20", "@ui-kitten/components": "^5.3.1", "@ui-kitten/eva-icons": "^5.3.1", "axios": "^1.7.7", "dayjs": "^1.11.13", "expo": "^52.0.46", "expo-updates": "~0.27.4", "lodash": "^4.17.21", "numeral": "^2.0.6", "react": "18.2.0", "react-hook-form": "^7.53.1", "react-native": "0.73.1", "react-native-chart-kit": "^6.12.0", "react-native-date-picker": "^5.0.12", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "2.18.0", "react-native-image-picker": "^8.2.0", "react-native-linear-gradient": "^2.8.3", "react-native-pager-view": "^6.2.3", "react-native-reanimated": "^3.16.5", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "3.27.0", "react-native-svg": "^15.5.0", "react-native-swiper-flatlist": "^3.2.4", "react-native-tab-view": "^3.5.2", "react-native-toast-message": "^2.3.3", "react-native-vector-icons": "^10.2.0", "react-native-webview": "^13.12.2", "yup": "^1.4.0", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "^0.73.18", "@react-native/eslint-config": "^0.73.1", "@react-native/metro-config": "^0.73.2", "@react-native/typescript-config": "^0.73.1", "@types/lodash": "^4.17.7", "@types/numeral": "^2.0.5", "@types/react": "^18.2.6", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "packageManager": "yarn@3.6.4"}