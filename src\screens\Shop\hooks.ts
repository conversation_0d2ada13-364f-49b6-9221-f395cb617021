import { getFilterProducts } from 'api/products';
import { useEffect, useState, useCallback } from 'react';
import { useProductsStore } from 'stores/products';
import { PRODUCT_SORT } from 'themes/constant';
import debounce from 'lodash/debounce';

export const useProducts = () => {
  const [loading, setLoading] = useState(false);
  const [sort, setSort] = useState(PRODUCT_SORT.createdAsc.key);
  const [products, setProducts] = useState([]);
  const [keyword, setKeyword] = useState('');

  const filter = useProductsStore((state) => state?.filter);
  const setFilter = useProductsStore((state) => state?.setFilter);

  const setSortProducts = useCallback((value: string) => {
    setSort(value);
    setFilter({ ...filter, sort: value });
  }, [filter, setFilter]);

  const fetchPosts = useCallback(async (searchKeyword: string, sortValue: string) => {
    try {
      setLoading(true);
      const params: any = {
        query: searchKeyword || null,
        categoryId: null,
      };
      
      // Chỉ thêm sort khi không có keyword (không tìm kiếm)
      if (!searchKeyword) {
        params.sort = sortValue;
      }

      const response = await getFilterProducts(params);
      const productList = response?.products || [];
      if (Array.isArray(productList)) {
        setProducts(productList);
      } else {
        console.warn('Products is not an array:', productList);
        setProducts([]);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  }, []);

  const searchProducts = useCallback(
    debounce((searchKeyword: string) => {
      setKeyword(searchKeyword);
      setFilter({ ...filter, query: searchKeyword });
    }, 300),
    [filter, setFilter]
  );

  useEffect(() => {
    fetchPosts(keyword, sort);
  }, [keyword, sort, fetchPosts]);

  return {
    loading,
    products,
    sort,
    keyword,
    setProducts,
    setSortProducts,
    searchProducts,
  };
};