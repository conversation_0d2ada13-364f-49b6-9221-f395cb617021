import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';

import metrics from 'themes/metrics';
import PostCard from 'components/PostCard';
import { Divider, Text } from '@ui-kitten/components';
import Tag from 'components/Tag';
import fonts from 'themes/fonts';
import colors from 'themes/colors';

import post from 'utils/post2.json';

const HotPost = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Bài viết cho bạn</Text>
      <ScrollView
        horizontal
        style={styles.tags}
        bounces
        showsHorizontalScrollIndicator={false}>
        <Tag
          customStyles={styles.tag}
          size="large"
          type="success"
          title="Bài viết hot"
        />
        <Tag
          customStyles={styles.tag}
          size="large"
          title="Bài viết nổi bật"
        />
        <Tag customStyles={styles.tag} size="large" title="Bài viết mới" />
      </ScrollView>
      <View style={styles.content}>
        <PostCard
          title={post.title}
          author={post.author}
          uri={post.thumbnail}
          avatarUri={post.thumbnail}
          topicName={post?.topic?.name}
          createdAt={post?.createdAt}
        />
        <Divider />
        <PostCard
          title={post.title}
          author={post.author}
          uri={post.thumbnail}
          avatarUri={post.thumbnail}
          topicName={post?.topic?.name}
          createdAt={post?.createdAt}
        />
        <Divider />
        <PostCard
          title={post.title}
          author={post.author}
          uri={post.thumbnail}
          avatarUri={post.thumbnail}
          topicName={post?.topic?.name}
          createdAt={post?.createdAt}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: metrics.spacing4,
  },
  content: {
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
  },
  row: {
    justifyContent: 'space-around',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexDirection: 'row',
    gap: metrics.spacing2,
    marginBottom: metrics.spacing3,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  tag: {
    marginRight: metrics.spacing2,
  },
  tags: {
    marginBottom: metrics.spacing4,
  },
});

export default HotPost;
