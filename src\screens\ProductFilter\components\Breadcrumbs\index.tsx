import React from 'react';
import {
  StyleSheet,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';


import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import { find, isEmpty, map, range, size, split } from 'lodash';
import { useNavigation } from '@react-navigation/native';
import { flatCategories } from 'utils/index';
import { useProductsStore } from 'stores/products';

const Breadcrumbs = ({ treeId }: any) => {
  const categories = useProductsStore((state: any) => state?.categories);

  const flatted = flatCategories(categories);

  const arr = split(treeId, '/');

  const navigation = useNavigation();
  return (
    <ScrollView
      horizontal
      bounces
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.row}>
      <TouchableOpacity onPress={() => navigation?.goBack()}>
        <Text style={styles.title}><PERSON><PERSON><PERSON> viết</Text>
      </TouchableOpacity>
      {!isEmpty(arr) && <Text style={styles.title}>/</Text>}
      {map(arr, (categoryId, index) => {
        const category = find(flatted, { id: Number(categoryId) });
        return (
          <React.Fragment key={categoryId}>
            <TouchableOpacity
              onPress={() =>
                navigation?.navigate('ProductFilter', {
                  id: category?.id,
                  treeId: range(0, index + 1)
                    .map(i => arr[i])
                    .join('/'),
                  name: category?.name,
                })
              }>
              <Text style={styles.title}>
                {find(flatted, { id: Number(categoryId) })?.name}
              </Text>
            </TouchableOpacity>
            {size(arr) !== index + 1 && <Text style={styles.title}>/</Text>}
          </React.Fragment>
        );
      })}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  row: {
    paddingHorizontal: metrics.spacing4,
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',
    gap: metrics.spacing2,
  },
  title: {
    fontSize: fonts.size.medium,
    fontWeight: '600',
    color: colors['Moss/600'],
    marginBottom: metrics.spacing4,
    alignItems: 'center',
  },
});

export default Breadcrumbs;
