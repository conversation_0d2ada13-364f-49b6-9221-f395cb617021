import React, { createContext, useContext, useState, useEffect } from 'react';
import { getCartItemCount } from 'api/cart';
import AsyncStorage from '@react-native-async-storage/async-storage';

type CartContextType = {
  cartCount: number;
  setCartCount: (count: number) => void;
  fetchCartCount: () => Promise<void>;
};

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cartCount, setCartCount] = useState(0);

  const fetchCartCount = async () => {
    try {
      const response = await getCartItemCount();
      const count = response || 0;
      setCartCount(count);
      await AsyncStorage.setItem('cartCount', count.toString());
    } catch (error) {
      console.error('Error fetching cart count:', error);
      // Fallback to saved count
      const savedCount = await AsyncStorage.getItem('cartCount');
      if (savedCount) setCartCount(parseInt(savedCount, 10));
    }
  };

  useEffect(() => {
    fetchCartCount();
  }, []);

  return (
    <CartContext.Provider value={{ cartCount, setCartCount, fetchCartCount }}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) throw new Error('useCart must be used within CartProvider');
  return context;
};