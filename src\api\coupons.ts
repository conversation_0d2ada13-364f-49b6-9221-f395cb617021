import { Coupon } from 'types/coupon';
import { apis } from 'utils/axios';

export const getCollectedCoupons = async (
  priceCart: number,
  type: string = 'HATHYO',
  page: number = 0,
  size: number = 10
) => {
  try {
    console.log('Calling getCollectedCoupons API:', { priceCart, type, page, size });
    const response = await apis().get('/coupons', {
      params: { priceCart, type, page, size },
    });
    console.log('getCollectedCoupons response:', response.data);
    return response?.data;
  } catch (e: any) {
    console.error('getCollectedCoupons error:', {
      message: e.message,
      status: e.response?.status,
      data: e.response?.data,
    });
    throw new Error(
      e.response?.data?.message ||
        'Không thể tải danh sách coupon đã thu thập. Vui lòng thử lại.'
    );
  }
};


export const getCouponDetail = async (id: number): Promise<Coupon> => {
  try {
    console.log('Calling getCouponDetail API:', { id });
    const response = await apis().get(`/coupons/${id}`);
    console.log('getCouponDetail response:', response.data);
    return response?.data?.data;
  } catch (e: any) {
    console.error('getCouponDetail error:', {
      message: e.message,
      status: e.response?.status,
      data: e.response?.data,
    });
    throw new Error(
      e.response?.data?.message ||
        'Không thể tải chi tiết coupon. Vui lòng thử lại.'
    );
  }
};