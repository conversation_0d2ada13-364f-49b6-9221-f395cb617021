import React from 'react';
import { View, StyleSheet } from 'react-native';
import Toast from 'react-native-toast-message';

const ToastWrapper = () => {
  return (
    <View style={styles.container} pointerEvents="none">
      <Toast 
        position="top"
        topOffset={50}
        visibilityTime={3000}
        autoHide={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 99999,
    elevation: 99999,
    pointerEvents: 'none',
  },
});

export default ToastWrapper;
