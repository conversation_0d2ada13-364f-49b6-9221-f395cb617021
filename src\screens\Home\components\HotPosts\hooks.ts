/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';
import { getHomePagePosts } from 'api/posts';

// TypeScript interfaces for type safety
interface Post {
  id: string;
  title: string;
  // Add other post fields as needed (e.g., content, image, etc.)
}

interface TopicData {
  topic: string;
  posts: Post[];
}

interface HomePostsResponse {
  data: TopicData[];
}

export const useHomePosts = () => {
  const [loading, setLoading] = useState(false);
  const [homePosts, setHomePosts] = useState<HomePostsResponse | null>(null);
  const [since, setSince] = useState('');

  const fetchHomePosts = async () => {
    try {
      setLoading(true);
      const response: HomePostsResponse = await getHomePagePosts({ since });
      console.log('API Response:', JSON.stringify(response, null, 2)); // Log response for debugging
      setHomePosts(response);
    } catch (error) {
      console.error('Error fetching posts:', error); // Log errors for debugging
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHomePosts();
  }, [since]);

  return {
    loading,
    homePosts,
    since,
    setSince,
  };
};