{
  "extends": "@react-native/typescript-config/tsconfig.json",
  "compilerOptions": {
    "strict": true,
    // Path alias config
    "baseUrl": ".",
    "paths": {
      "components/*": ["src/components/*"],
      "api/*": ["src/api/*"],
      "assets/*": ["src/assets/*"],
      "hooks/*": ["src/hooks/*"],
      "navigation/*": ["src/navigation/*"],
      "screens/*": ["src/screens/*"],
      "utils/*": ["src/utils/*"],
      "themes/*": ["src/themes/*"],
      "stores/*": ["src/stores/*"],
      "constants/*": ["src/constants/*"],
      "types/*": ["src/types/*"],
    }
  }
}
