import React, { useState, useEffect } from 'react';
import { View, TextInput, StyleSheet, TouchableOpacity } from 'react-native';
import colors from 'themes/colors';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import Icon from 'react-native-vector-icons/Ionicons';
import SearchResults from './components/SearchResult';

type SearchBarProps = {
  onSearch: (text: string) => void;
  loading?: boolean;
  keyword?: string;
  products?: any[];
  isSearching?: boolean;
};

const SearchBar: React.FC<SearchBarProps> = ({ 
  onSearch, 
  loading = false, 
  keyword = '', 
  products = [], 
  isSearching = false 
}) => {
  const [searchText, setSearchText] = useState(keyword);

  useEffect(() => {
    setSearchText(keyword);
  }, [keyword]);

  const handleSubmit = () => {
    if (searchText.trim() !== keyword) {
      if (typeof onSearch === 'function') {
        onSearch(searchText.trim());
      } else {
        console.error('onSearch is not a function:', onSearch);
      }
    }
  };

  const handleClear = () => {
    setSearchText('');
    if (typeof onSearch === 'function') {
      onSearch('');
    } else {
      console.error('onSearch is not a function:', onSearch);
    }
  };

  return (
    <View style={styles.wrapper}>
      <View style={styles.container}>
        <View style={styles.searchContainer}>
          <Icon name="search" size={20} color={colors['Gray/500']} style={styles.searchIcon} />
          <TextInput
            style={styles.input}
            placeholder="Tìm kiếm sản phẩm..."
            placeholderTextColor={colors['Gray/400']}
            value={searchText}
            onChangeText={setSearchText}
            onSubmitEditing={handleSubmit}
            returnKeyType="search"
            autoCapitalize="none"
          />
          {searchText ? (
            <TouchableOpacity onPress={handleClear} style={styles.clearButton}>
              <Icon name="close-circle" size={16} color={colors['Gray/500']} />
            </TouchableOpacity>
          ) : null}
        </View>
      </View>
      
      {isSearching && (loading || products.length > 0) && (
        <SearchResults 
          products={products} 
          loading={loading} 
          keyword={keyword}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    backgroundColor: colors['Grayiron/50'],
    paddingVertical: metrics.spacing2,
  },
  container: {
    flexDirection: 'row',
    paddingHorizontal: metrics.spacing3,
    alignItems: 'center',
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: metrics.radius2,
    paddingHorizontal: metrics.spacing2,
    borderWidth: 1,
    borderColor: colors['Grayiron/200'],
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    marginRight: metrics.spacing1,
    color: colors['Gray/100'],
  },
  input: {
    flex: 1,
    height: 40,
    color: colors['Grayiron/700'],
    fontSize: fonts.size.medium,
    paddingVertical: metrics.spacing1,
  },
  clearButton: {
    padding: metrics.spacing1,
  },
});

export default SearchBar;