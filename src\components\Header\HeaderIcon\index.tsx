/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { StyleSheet, TouchableOpacity, ViewStyle } from 'react-native';
import { Icon } from '@ui-kitten/components';
import metrics from 'themes/metrics';
import colors from 'themes/colors';

type Props = {
  transparent?: boolean;
  iconName?: string;
  containerStyle?: ViewStyle;
  onPress?: any;
};

const HeaderIcon = ({
  transparent,
  iconName = 'arrow-back-outline',
  containerStyle,
  onPress,
}: Props) => (
  <TouchableOpacity
    onPress={onPress}
    style={[
      styles.backButton,
      !transparent ? { backgroundColor: 'white' } : {},
      containerStyle,
    ]}>
    <Icon
      name={iconName}
      fill={transparent ? 'white' : colors['Grayiron/400']}
      style={styles.headerIcon}
    />
  </TouchableOpacity>
);
const styles = StyleSheet.create({
  backButton: {
    padding: metrics.spacing3,
    borderRadius: metrics.radius2,
  },
  headerIcon: {
    width: 18,
    height: 18,
  },
});

export default HeaderIcon;
