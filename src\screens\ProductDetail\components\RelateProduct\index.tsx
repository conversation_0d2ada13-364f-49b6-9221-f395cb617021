import React from 'react';
import {
  View,
  StyleSheet,
  Text,
  FlatList,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import Product from 'components/Product';
import { addRecentProducts, addToCart } from 'utils/cart';
import { useGetProducts } from './hooks';

// Định nghĩa type cho Stack Navigator
type RootStackParamList = {
  ProductDetail: { id: number | string };
  AppTab: undefined;
};

type NavigationProps = NativeStackNavigationProp<RootStackParamList>;

type ProductType = {
  id: number;
  title: string;
  mainImageUrl: string;
  price: number;
  anchoPrice: number;
  rating?: number;
};

const RelateProduct = () => {
  const navigation = useNavigation<NavigationProps>();
  const { loading, products, error } = useGetProducts({ size: 10 });
  const [isNavigating, setIsNavigating] = React.useState(false);

  // Debug dữ liệu
  React.useEffect(() => {
    console.log('RelateProduct - Data:', { products, loading, error });
  }, [products, loading, error]);

  const renderProductItem = ({ item }: { item: ProductType }) => (
    <View style={styles.productItem}>
      <Product
        customStyles={styles.product}
        title={item?.title}
        uri={item?.mainImageUrl}
        price={item?.price}
        anchoPrice={item?.anchoPrice}
        discountPercent={item?.discountPercent}
        rating={item?.rating}
        onPress={() => {
          if (item?.id && !isNavigating) {
            setIsNavigating(true);
            console.log('RelateProduct - Navigating to ProductDetail with ID:', item.id);
            addRecentProducts(item);
            navigation.push('ProductDetail', { id: item.id }); // Sử dụng push
            setTimeout(() => setIsNavigating(false), 1000); // Reset sau 1 giây
          } else {
            console.warn('RelateProduct - Invalid product ID or navigating:', item);
          }
        }}
        onAddToCart={() => addToCart({ ...item, quantity: 1 })}
      />
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors['Primary/500']} />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Không thể tải sản phẩm liên quan</Text>
      </View>
    );
  }

  if (!products || products.length === 0) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Không có sản phẩm liên quan</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.sectionTitle}>Sản phẩm liên quan</Text>
      <FlatList
        data={products}
        renderItem={renderProductItem}
        keyExtractor={(item: ProductType) => item.id.toString()}
        numColumns={2}
        columnWrapperStyle={styles.columnWrapper}
        contentContainerStyle={styles.productsContainer}
        scrollEnabled={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: metrics.spacing4,
    backgroundColor: colors.white,
    marginBottom: metrics.spacing4,
  },
  sectionTitle: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
    paddingHorizontal: metrics.spacing4,
  },
  productsContainer: {
    paddingHorizontal: metrics.spacing4,
    paddingBottom: metrics.spacing4,
  },
  columnWrapper: {
    justifyContent: 'space-between',
    marginBottom: metrics.spacing4,
  },
  productItem: {
    width: '48%',
  },
  product: {
    width: '100%',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: metrics.spacing6,
  },
  errorContainer: {
    padding: metrics.spacing4,
    backgroundColor: colors.white,
    borderRadius: metrics.radius3,
    alignItems: 'center',
  },
  errorText: {
    color: colors['Danger/500'],
    fontSize: fonts.size.medium,
  },
});

export default RelateProduct;