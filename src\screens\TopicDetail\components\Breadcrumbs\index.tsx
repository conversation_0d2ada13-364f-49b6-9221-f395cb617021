import React from 'react';
import {
  View,
  StyleSheet,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';

import MenuButton from 'components/MenuButton';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import { usePostMenu } from './hooks';
import { find, isEmpty, map, range, size, slice, split } from 'lodash';
import { useNavigation } from '@react-navigation/native';
import Tag from 'components/Tag';
import { useTopicStore } from 'stores/topics';
import { flatTopics } from 'utils/index';

const Breadcrumbs = ({ treeId }: any) => {
  const topics = useTopicStore(state => state?.topics);

  const flatted = flatTopics(topics);

  const arr = split(treeId, '/');

  const navigation = useNavigation();
  return (
    <ScrollView
      horizontal
      bounces
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.row}>
      <TouchableOpacity onPress={() => navigation?.goBack()}>
        <Text style={styles.title}>Bài viết</Text>
      </TouchableOpacity>
      {!isEmpty(arr) && <Text style={styles.title}>/</Text>}
      {map(arr, (topicId, index) => {
        const topic = find(flatted, { id: Number(topicId) });
        return (
          <React.Fragment key={topicId}>
            <TouchableOpacity
              onPress={() =>
                navigation?.navigate('TopicDetail', {
                  id: topic?.id,
                  treeId: range(0, index + 1)
                    .map(i => arr[i])
                    .join('/'),
                  name: topic?.name,
                })
              }>
              <Text style={styles.title}>
                {find(flatted, { id: Number(topicId) })?.name}
              </Text>
            </TouchableOpacity>
            {size(arr) !== index + 1 && <Text style={styles.title}>/</Text>}
          </React.Fragment>
        );
      })}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  row: {
    paddingHorizontal: metrics.spacing4,
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',
    gap: metrics.spacing2,
  },
  title: {
    fontSize: fonts.size.medium,
    fontWeight: '600',
    color: colors['Moss/600'],
    marginBottom: metrics.spacing4,
    alignItems: 'center',
  },
});

export default Breadcrumbs;
