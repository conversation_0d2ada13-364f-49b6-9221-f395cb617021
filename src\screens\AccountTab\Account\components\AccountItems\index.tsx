import React from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import RowItem from 'components/RowItem';
import FastImage from 'react-native-fast-image';
import { useNavigation } from '@react-navigation/native';

const AccountItems = () => {
  const navigation = useNavigation();
  return (
    <View style={styles.container}>
      <View style={commonStyles.rowSpaceBetween}>
        <Text style={styles.title}>Tài khoản</Text>
      </View>
      <View style={styles.content}>
        <View style={styles.itemContainer}>
          <RowItem
            title="Quản lý tài khoản"  
            leftIconName="account-circle-outline"
            onPress={() => navigation?.navigate('AccountManager')}
          />
          <RowItem
            title="Quản lý sổ địa chỉ"
            leftIconName="map-marker-multiple-outline"
            onPress={() => navigation?.navigate('AddressBook')}
          />
         <RowItem
  title="Mã giảm giá"
  leftIconName="ticket-percent" // icon phù hợp hơn
  onPress={() => navigation?.navigate('CouponsScreen')}
/>

          {/* <RowItem
            title="Quản lý thẻ thanh toán"
            leftIconName="credit-card-multiple-outline"
            onPress={() => null}
          />
          <RowItem
            title="Cài đặt"
            leftIconName="cog-outline"
            onPress={() => null}
          /> */}
        </View>
        {/* <TouchableOpacity
          onPress={() =>
            navigation?.navigate('WebviewScreen', {
              uri: 'https://admin.hathyo.com/user/register',
              title: 'Trở thành cộng tác viên',
            })
          }>
          <FastImage
            style={styles.contributeImage}
            source={require('assets/images/contribute-image.png')}
            resizeMode="contain"
          />
        </TouchableOpacity> */}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  content: {
    paddingVertical: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
  },
  itemContainer: {
    paddingHorizontal: metrics.spacing6,
    marginBottom: metrics.spacing3,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  subTitle: {
    fontSize: fonts.size.input,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  contributeImage: {
    width: 'auto',
    height: (metrics.fullWidth * 175) / 319 - metrics.spacing4 * 2,
    marginHorizontal: metrics.spacing4,
  },
});

export default AccountItems;