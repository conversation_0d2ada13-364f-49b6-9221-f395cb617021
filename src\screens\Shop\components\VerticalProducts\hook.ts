import { useState } from 'react';

interface LoadMoreProps {
  initialItems: any[];
  itemsPerPage?: number;
}

export const useLoadMore = ({ initialItems, itemsPerPage = 10 }: LoadMoreProps) => {
  const [displayedItems, setDisplayedItems] = useState<any[]>(initialItems.slice(0, itemsPerPage));
  const [currentPage, setCurrentPage] = useState(1);
  const totalItems = initialItems.length;
  const hasMore = currentPage * itemsPerPage < totalItems;

  const loadMore = () => {
    const nextPage = currentPage + 1;
    const newItems = initialItems.slice(0, nextPage * itemsPerPage);
    setDisplayedItems(newItems);
    setCurrentPage(nextPage);
  };

  return {
    displayedItems,
    hasMore,
    loadMore,
  };
};