import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { CheckBox } from '@ui-kitten/components';
import Icon from 'react-native-vector-icons/MaterialIcons';

import CommonButton from 'components/CommonButton';
import { AUTHENTICATION_STEP, REGEX_INPUT } from 'constants/authentication';
import FormInput from 'components/FormInput';
import { sendOtp, signUp } from 'api/users';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import CustomAlert from 'utils/widgets/CustomAlert';
import Toast from 'react-native-toast-message';

// Schema cho bước 1: Nhập tất cả thông tin
const registerSchema = yup.object({
  phoneNo: yup
    .string()
    .required('Vui lòng nhập số điện thoại')
    .trim()
    .matches(REGEX_INPUT.PHONE, 'Số điện thoại chưa đúng định dạng'),
  firstname: yup
    .string()
    .required('Vui lòng nhập tên')
    .trim(),
  lastname: yup
    .string()
    .required('Vui lòng nhập họ')
    .trim(),
  email: yup
    .string()
    .required('Vui lòng nhập email')
    .trim()
    .matches(REGEX_INPUT.EMAIL, 'Email chưa đúng định dạng'),
  password: yup
    .string()
    .required('Vui lòng nhập mật khẩu')
    .trim()
    .min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),
  acceptTerm: yup
    .boolean()
    .isTrue('Vui lòng chấp nhận điều khoản của chúng tôi'),
});

const RegisterForm = ({ setStep, currentData, setCurrentData }: any) => {
  const [alertVisible, setAlertVisible] = useState(false);
  const [otp, setOtp] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [canResend, setCanResend] = useState(true);

  // Form cho bước 1
  const {
    control,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm({
    defaultValues: {
      phoneNo: '',
      firstname: '',
      lastname: '',
      email: '',
      password: '',
      acceptTerm: undefined,
    },
    resolver: yupResolver(registerSchema),
  });

  // Countdown timer effect
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [countdown]);

  // Xử lý gửi OTP và hiển thị alert
  const onSubmit = async (values: any) => {
    setLoading(true);
    try {
      const response = await sendOtp({
        phoneNumber: values.phoneNo,
      });
      if (response?.code) {
        setError('phoneNo', {
          message: response.message || 'Có lỗi xảy ra trong quá trình gửi mã OTP',
        });
        Toast.show({
          type: 'error',
          text1: 'Lỗi gửi OTP',
          text2: response.message || 'Có lỗi xảy ra trong quá trình gửi mã OTP',
        });
      } else {
        setCurrentData({
          ...currentData,
          phoneNo: values.phoneNo,
          firstname: values.firstname,
          lastname: values.lastname,
          email: values.email,
          password: values.password,
        });
        setAlertVisible(true);
        setCountdown(60);
        setCanResend(false);
        Toast.show({
          type: 'success',
          text1: 'Gửi OTP thành công',
          text2: `Mã OTP đã được gửi đến số ${values.phoneNo}`,
        });
      }
    } catch (error: any) {
      setError('phoneNo', {
        message: 'Có lỗi xảy ra trong quá trình gửi mã OTP',
      });
      Toast.show({
        type: 'error',
        text1: 'Lỗi gửi OTP',
        text2: error?.message || 'Có lỗi xảy ra trong quá trình gửi mã OTP',
      });
    } finally {
      setLoading(false);
    }
  };

  // Xử lý resend OTP
  const handleResendOtp = async () => {
    if (!canResend) {
      Toast.show({
        type: 'warning',
        text1: 'Vui lòng chờ',
        text2: `Gửi lại mã sau ${countdown} giây`,
      });
      return;
    }

    setLoading(true);
    try {
      const response = await sendOtp({
        phoneNumber: currentData.phoneNo,
      });
      if (response?.code) {
        Toast.show({
          type: 'error',
          text1: 'Lỗi gửi lại OTP',
          text2: response.message || 'Có lỗi xảy ra trong quá trình gửi lại mã OTP',
        });
      } else {
        setCountdown(60);
        setCanResend(false);
        Toast.show({
          type: 'success',
          text1: 'Gửi lại OTP thành công',
          text2: `Mã OTP đã được gửi lại đến số ${currentData.phoneNo}`,
        });
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Lỗi gửi lại OTP',
        text2: error?.message || 'Có lỗi xảy ra trong quá trình gửi lại mã OTP',
      });
    } finally {
      setLoading(false);
    }
  };

  // Xử lý xác nhận OTP và gọi API đăng ký
  const handleOtpSubmit = async () => {
    if (!otp.trim()) {
      Toast.show({
        type: 'warning',
        text1: 'Vui lòng nhập mã OTP',
      });
      return;
    }

    setLoading(true);
    try {
      const signupData = {
        firstname: currentData.firstname,
        lastname: currentData.lastname,
        phoneNo: currentData.phoneNo,
        email: currentData.email,
        password: currentData.password,
        roles: [],
        origin: 'https://hathyo.com',
        otp: otp,
      };
      const response = await signUp({ body: signupData });
      if (response?.code) {
        setError('phoneNo', {
          message: response.message || 'Mã OTP không hợp lệ',
        });
        Toast.show({
          type: 'error',
          text1: 'Mã OTP không hợp lệ',
          text2: response.message || 'Vui lòng kiểm tra lại mã OTP',
        });
      } else {
        setAlertVisible(false); // Ẩn popup sau khi xác nhận OTP thành công
        setOtp(''); // Reset OTP input
        setCountdown(0);
        setCanResend(true);
        Toast.show({
          type: 'success',
          text1: 'Đăng ký thành công',
          text2: 'Tài khoản của bạn đã được tạo thành công',
        });
        setStep(AUTHENTICATION_STEP.LOGIN);
      }
    } catch (error: any) {
      setError('phoneNo', {
        message: 'Có lỗi xảy ra trong quá trình đăng ký',
      });
      Toast.show({
        type: 'error',
        text1: 'Lỗi đăng ký',
        text2: error?.message || 'Có lỗi xảy ra trong quá trình đăng ký',
      });
    } finally {
      setLoading(false);
    }
  };

  // Toggle show/hide password
  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  return (
    <View style={styles.container}>
      {/* Form bước 1: Nhập tất cả thông tin */}
      <View style={styles.content}>
        <View>
          <Text style={styles.subTitle}>Đăng ký</Text>
        </View>
        <View style={styles.item}>
          <FormInput
            label="Số điện thoại"
            control={control}
            required
            placeholder="Nhập số điện thoại"
            name="phoneNo"
            errors={errors?.phoneNo}
            keyboardType="phone-pad"
          />
        </View>
        <View style={styles.item}>
          <FormInput
            label="Tên"
            control={control}
            required
            placeholder="Nhập tên"
            name="firstname"
            errors={errors?.firstname}
          />
        </View>
        <View style={styles.item}>
          <FormInput
            label="Họ"
            control={control}
            required
            placeholder="Nhập họ"
            name="lastname"
            errors={errors?.lastname}
          />
        </View>
        <View style={styles.item}>
          <FormInput
            label="Email"
            control={control}
            required
            placeholder="Nhập email"
            name="email"
            errors={errors?.email}
            keyboardType="email-address"
          />
        </View>
        <View style={styles.item}>
          <FormInput
            label="Mật khẩu"
            control={control}
            required
            placeholder="Nhập mật khẩu"
            name="password"
            errors={errors?.password}
            secureTextEntry={!showPassword}
            rightIcon={
              <TouchableOpacity onPress={toggleShowPassword}>
                <Icon
                  name={showPassword ? 'visibility-off' : 'visibility'}
                  size={24}
                  color={colors['Grayiron/400']}
                />
              </TouchableOpacity>
            }
          />
        </View>
        <View style={styles.item}>
          <Controller
            control={control}
            rules={{ required: true }}
            render={({ field: { onChange, onBlur, value } }) => (
              <CheckBox
                checked={!!value}
                status="basic"
                onBlur={onBlur}
                onChange={onChange}>
                Tôi đã đọc và đồng ý với Điều khoản sử dụng và Chính sách bảo
                mật thông tin cá nhân của Hathyo.com
              </CheckBox>
            )}
            name="acceptTerm"
          />
          {errors?.acceptTerm && (
            <Text style={commonStyles.errorText}>
              {errors.acceptTerm.message}
            </Text>
          )}
        </View>
        <View style={commonStyles.rowCenter}>
          <CommonButton
            onPress={handleSubmit(onSubmit)}
            customStyles={{
              backgroundColor: colors['Moss/500'],
              opacity: loading ? 0.7 : 1
            }}
            disabled={loading}
          >
            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={colors.white} />
                <Text style={styles.loadingText}>Đang gửi...</Text>
              </View>
            ) : (
              'Đăng ký'
            )}
          </CommonButton>
        </View>
      </View>
      <View style={commonStyles.rowCenter}>
        <TouchableOpacity onPress={() => setStep(AUTHENTICATION_STEP.LOGIN)}>
          <Text style={styles.text}>
            Đã có tài khoản ?
            <Text style={styles.registerText}> Đăng nhập ngay</Text>
          </Text>
        </TouchableOpacity>
      </View>

      {/* Alert để nhập OTP */}
      <CustomAlert
        visible={alertVisible}
        type="confirm"
        title="Nhập mã OTP"
        message={`Vui lòng nhập mã OTP đã được gửi đến số điện thoại ${currentData.phoneNo}.`}
        showInput={true}
        inputPlaceholder="Nhập mã OTP"
        onInputChange={setOtp}
        buttons={[
          {
            text: 'Hủy',
            style: 'cancel',
            onPress: () => {
              setAlertVisible(false);
              setOtp('');
              setCountdown(0);
              setCanResend(true);
            },
          },
          {
            text: canResend ? 'Gửi lại OTP' : `Gửi lại sau ${countdown}s`,
            style: canResend ? 'default' : 'disabled',
            onPress: canResend ? handleResendOtp : undefined,
            disabled: !canResend || loading,
          },
          {
            text: loading ? 'Đang xử lý...' : 'Xác nhận',
            style: 'default',
            onPress: handleOtpSubmit,
            disabled: loading,
          },
        ]}
        onDismiss={() => {
          setAlertVisible(false);
          setOtp('');
          setCountdown(0);
          setCanResend(true);
        }}
        dismissable={!loading}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  content: {
    padding: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
    marginBottom: metrics.spacing4,
  },
  item: {
    marginBottom: metrics.spacing3,
  },
  subTitle: {
    fontSize: fonts.size.h2,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  text: {
    fontSize: fonts.size.input,
    fontWeight: '400',
    color: colors['Grayiron/400'],
    marginBottom: metrics.spacing4,
  },
  registerText: {
    fontSize: fonts.size.input,
    fontWeight: '500',
    color: colors['Moss/500'],
    marginBottom: metrics.spacing4,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginLeft: metrics.spacing1,
    color: colors.white,
    fontSize: fonts.size.input,
    fontWeight: '500',
  },
});

export default RegisterForm;