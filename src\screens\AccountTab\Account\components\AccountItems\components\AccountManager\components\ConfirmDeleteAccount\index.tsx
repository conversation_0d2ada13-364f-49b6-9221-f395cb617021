import React, { useState } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import metrics from 'themes/metrics';
import colors from 'themes/colors';
import fonts from 'themes/fonts';
import HomeHeader from 'components/Header/HomeHeader';
import CustomAlert from 'utils/widgets/CustomAlert';

type RootStackParamList = {
  ConfirmDeleteAccount: undefined;
  DeleteAccount: undefined;
  AppTab: undefined;
};

type NavigationProps = NativeStackNavigationProp<RootStackParamList>;

const ConfirmDeleteAccount: React.FC = () => {
  const navigation = useNavigation<NavigationProps>();
  const [showConfirm, setShowConfirm] = useState(false);
  const scaleAnim = React.useRef(new Animated.Value(0.95)).current;

  React.useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 8,
      tension: 40,
      useNativeDriver: true,
    }).start();
  }, [scaleAnim]);

  const handleConfirm = () => {
    setShowConfirm(true);
  };

  const handleConfirmYes = () => {
    setShowConfirm(false);
    navigation.navigate('DeleteAccount');
  };

  return (
    <SafeAreaView style={styles.safeAreaView}>
      <HomeHeader backButton
      title='Xóa tài khoản'
      noStretch />
      <View style={styles.contentContainer}>
        <LinearGradient
          colors={['rgba(255, 77, 77, 0.08)', 'rgba(255, 77, 77, 0.02)', 'transparent']}
          style={styles.gradientOverlay}
        />
        <Animated.View style={[styles.content, { transform: [{ scale: scaleAnim }] }]}>
          {/* Warning Card */}
          <View style={styles.warningCard}>
            <View style={styles.iconContainer}>
              <MaterialCommunityIcons
                name="alert-circle"
                size={48}
                color={colors['Danger/500']}
              />
            </View>
            <Text style={styles.title}>Xóa tài khoản vĩnh viễn</Text>
            <Text style={styles.subtitle}>
              Hành động này không thể hoàn tác
            </Text>
          </View>

          {/* Warning List */}
          <View style={styles.warningList}>
            <View style={styles.warningItem}>
              <MaterialCommunityIcons
                name="database-remove"
                size={20}
                color={colors['Danger/600']}
                style={styles.warningIcon}
              />
              <Text style={styles.warningText}>Tất cả dữ liệu cá nhân sẽ bị xóa</Text>
            </View>
            <View style={styles.warningItem}>
              <MaterialCommunityIcons
                name="history"
                size={20}
                color={colors['Danger/600']}
                style={styles.warningIcon}
              />
              <Text style={styles.warningText}>Lịch sử giao dịch sẽ bị mất</Text>
            </View>
            <View style={styles.warningItem}>
              <MaterialCommunityIcons
                name="account-cancel"
                size={20}
                color={colors['Danger/600']}
                style={styles.warningIcon}
              />
              <Text style={styles.warningText}>Không thể khôi phục tài khoản</Text>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => navigation.goBack()}
              activeOpacity={0.8}
            >
              <Text style={styles.cancelButtonText}>Hủy bỏ</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.deleteButton}
              onPress={handleConfirm}
              activeOpacity={0.8}
            >
              <MaterialCommunityIcons
                name="delete-forever"
                size={18}
                color={colors.white}
                style={styles.buttonIcon}
              />
              <Text style={styles.deleteButtonText}>Tiếp tục xóa</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
      <CustomAlert
        visible={showConfirm}
        type="confirm"
        title="Bạn có chắc chắn?"
        message="Xóa tài khoản là hành động không thể hoàn tác. Bạn sẽ cần nhập mã OTP để hoàn tất."
        buttons={[
          { text: 'Hủy', style: 'cancel', onPress: () => setShowConfirm(false) },
          { text: 'Tiếp tục', style: 'destructive', onPress: handleConfirmYes },
        ]}
        dismissable={true}
        onDismiss={() => setShowConfirm(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
    backgroundColor: colors['Gray/50'],
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  gradientOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '60%',
  },
  content: {
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
  },
  warningCard: {
    backgroundColor: colors.white,
    borderRadius: metrics.radius4,
    padding: metrics.spacing6,
    alignItems: 'center',
    marginBottom: metrics.spacing4,
    shadowColor: colors['Gray/900'],
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 8,
    borderWidth: 1,
    borderColor: colors['Danger/100'],
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors['Danger/50'],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: metrics.spacing3,
  },
  title: {
    ...fonts.style.h2,
    fontWeight: '700',
    color: colors['Gray/900'],
    textAlign: 'center',
    marginBottom: metrics.spacing1,
    fontSize: 24,
  },
  subtitle: {
    ...fonts.style.normal,
    color: colors['Danger/600'],
    textAlign: 'center',
    fontWeight: '500',
    fontSize: 16,
  },
  warningList: {
    backgroundColor: colors.white,
    borderRadius: metrics.radius3,
    padding: metrics.spacing4,
    marginBottom: metrics.spacing6,
    shadowColor: colors['Gray/900'],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 4,
    width: '100%',
  },
  warningItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: metrics.spacing2,
  },
  warningIcon: {
    marginRight: metrics.spacing3,
  },
  warningText: {
    ...fonts.style.normal,
    color: colors['Gray/700'],
    flex: 1,
    fontSize: 15,
    lineHeight: 20,
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    gap: metrics.spacing3,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: colors['Gray/100'],
    paddingVertical: metrics.spacing3,
    borderRadius: metrics.radius3,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors['Gray/200'],
  },
  cancelButtonText: {
    ...fonts.style.normal,
    color: colors['Gray/700'],
    fontWeight: '600',
    fontSize: 16,
  },
  deleteButton: {
    flex: 1,
    backgroundColor: colors['Danger/500'],
    paddingVertical: metrics.spacing3,
    borderRadius: metrics.radius3,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors['Danger/500'],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  deleteButtonText: {
    ...fonts.style.normal,
    color: colors.white,
    fontWeight: '600',
    fontSize: 16,
  },
  buttonIcon: {
    marginRight: metrics.spacing1,
  },
});

export default ConfirmDeleteAccount;