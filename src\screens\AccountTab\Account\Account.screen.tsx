import React from 'react';
import { SafeAreaView, StyleSheet, ScrollView, View, Text, Image, TouchableOpacity, Linking } from 'react-native';
import HomeHeader from 'components/Header/HomeHeader';
import metrics from 'themes/metrics';
import MyOrders from './components/MyOrders';
import AccountItems from './components/AccountItems';
import AboutUs from './components/AboutUs';
import LogoutButton from './components/LogoutButton';
import colors from 'themes/colors';
import { useNavigation } from '@react-navigation/native';
import { useUserStore } from 'stores/user';
import { isEmpty } from 'lodash';

const Content = () => {
  const navigation = useNavigation();
  const user = useUserStore((state: any) => state?.user);

  if (!isEmpty(user)) {
    return (
      <ScrollView
        style={styles.outerWrapper}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <MyOrders />
        <AccountItems />
        <AboutUs />
        <LogoutButton />
      </ScrollView>
    );
  }

  return (
    <ScrollView
      style={styles.outerWrapper}
      contentContainerStyle={styles.scrollContent}
      showsVerticalScrollIndicator={false}
    >
      <View style={styles.welcomeContainer}>
        <Image
          source={require('assets/images/logo.png')}
          style={styles.logo}
          resizeMode="contain"
        />
        <Text style={styles.title}>Chào mừng đến với Hathyo</Text>
        <Text style={styles.subtitle}>
          Đăng nhập để khám phá trải nghiệm cá nhân hóa và những ưu đãi độc quyền.
        </Text>
        <TouchableOpacity
          style={styles.loginButton}
          onPress={() => navigation?.navigate('Authentication')}
          activeOpacity={0.7}
        >
          <Text style={styles.loginButtonText}>Đăng nhập</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.exploreButton}
          onPress={() => Linking.openURL('https://hathyo.com')}
          activeOpacity={0.7}
        >
          <Text style={styles.exploreButtonText}>Khám phá hathyo.com</Text>
        </TouchableOpacity>
      </View>
      <AboutUs />
    </ScrollView>
  );
};

const Account = () => {
  return (
    <>
      <HomeHeader noStretch />
      <SafeAreaView style={styles.safeAreaView}>
        <Content />
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
    backgroundColor: colors.white,
  },
  outerWrapper: {
    flex: 1,
  },
  scrollContent: {
    paddingTop: metrics.spacing4,
    paddingBottom: metrics.spacing8,
  },
  welcomeContainer: {
    alignItems: 'center',
    padding: metrics.spacing5,
    marginHorizontal: metrics.spacing4,
    backgroundColor: colors.white,
    borderRadius: 20,
    shadowColor: colors['Gray/900'],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 6,
  },
  logo: {
    width: 200,
    height: 120,
  },
  title: {
    fontSize: 26,
    fontWeight: '700',
    color: colors['Moss/500'],
    textAlign: 'center',
    marginBottom: metrics.spacing2,
  },
  subtitle: {
    fontSize: 15,
    color: colors['Gray/600'],
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: metrics.spacing4,
    paddingHorizontal: metrics.spacing3,
  },
  loginButton: {
    backgroundColor: colors['Moss/500'],
    borderRadius: 12,
    paddingVertical: metrics.spacing2,
    paddingHorizontal: metrics.spacing5,
    width: '75%',
    alignItems: 'center',
    marginBottom: metrics.spacing2,
  },
  loginButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
  exploreButton: {
    borderColor: colors['Moss/500'],
    borderWidth: 1.5,
    borderRadius: 12,
    paddingVertical: metrics.spacing2,
    paddingHorizontal: metrics.spacing5,
    width: '75%',
    alignItems: 'center',
  },
  exploreButtonText: {
    color: colors['Moss/500'],
    fontSize: 16,
    fontWeight: '600',
  },
});

export default Account;