/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Toast from 'react-native-toast-message';

import { getProducts } from 'api/products';
import { addToCart } from 'api/cart';
import { useCart } from 'hooks/contexts/CartContext';
import { STORAGE } from 'constants/authentication';

export const useHomeProducts = () => {
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [products, setProducts] = useState([]);
  const [addingToCart, setAddingToCart] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const { fetchCartCount } = useCart();

  const fetchProducts = async (pageNum = 1, append = false) => {
    if (!hasMore && append) return;

    try {
      if (append) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      // Assume getProducts accepts page and limit parameters
      const response = await getProducts({ page: pageNum, limit: 10 }); // Adjust limit as needed
      const newProducts = response.data || response; // Adjust based on your API response structure

      // Check if there are more products to load
      const totalPages = response.totalPages || 1; // Adjust based on your API response
      setHasMore(pageNum < totalPages);

      // Append or replace products
      setProducts((prev) => (append ? [...prev, ...newProducts] : newProducts));
      if (append) {
        setPage(pageNum + 1);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      Toast.show({
        type: 'error',
        text1: 'Lỗi tải sản phẩm',
        text2: 'Không thể tải danh sách sản phẩm. Vui lòng thử lại sau.',
        position: 'top',
      });
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const handleAddToCart = async (product, quantity = 1, variant = null) => {
    if (!product) return false;

    const token = await AsyncStorage.getItem(STORAGE.ACCESS_TOKEN);
    if (token == null) {
      Toast.show({
        type: 'error',
        text1: 'Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng',
        position: 'top',
      });
      return false;
    }

    setAddingToCart(true);

    try {
      const cartItem = {
        id: product.id,
        merchantId: product.merchantId,
        quantity: quantity,
        price: variant ? variant.price : product.price,
        ...(variant?.id && { variantId: variant.id }),
        ...(variant?.mainAttributeValueId && { mainAttributeValueId: variant.mainAttributeValueId }),
        ...(variant?.secondAttributeValueId && { secondAttributeValueId: variant.secondAttributeValueId }),
      };

      const response = await addToCart(cartItem);

      if (response?.success !== false) {
        await fetchCartCount();
        Toast.show({
          type: 'success',
          text1: 'Đã thêm vào giỏ hàng',
          position: 'top',
        });
        return true;
      } else {
        Toast.show({
          type: 'error',
          text1: 'Thêm vào giỏ hàng thất bại',
          text2: response?.message || 'Vui lòng thử lại sau.',
          position: 'top',
        });
        return false;
      }
    } catch (e) {
      console.error('Add to cart error:', e);
      Toast.show({
        type: 'error',
        text1: 'Thêm vào giỏ hàng thất bại',
        position: 'top',
      });
      return false;
    } finally {
      setAddingToCart(false);
    }
  };

  useEffect(() => {
    fetchProducts(1);
  }, []);

  return {
    loading,
    loadingMore,
    products,
    addingToCart,
    hasMore,
    fetchProducts,
    handleAddToCart,
  };
};