import { get } from 'lodash';
import { PRODUCT_SORT } from 'themes/constant';
import { apis } from 'utils/axios';

export const getProducts = async () => {
  try {
    const response = await apis().get('products/homepage');
    return response?.data;
  } catch (e) {
    console.error('Failed to fetch products:', e);
    throw e;
  }
};

export const getProductDetail = async ({ id }: { id: string | number }) => {
  try {
    const response = await apis().get(`products/homepage/${id}`);
    console.log("in ra sản phẩm là: ", response.data)
    return response?.data;
  } catch (e) {
    console.error(`Failed to fetch product detail for ID ${id}:`, e);
    throw e;
  }
};

export const getMerchantInfor = async ({ id }: { id: string | number }) => {
  try {
    const response = await apis().get(`merchant/${id}`);
    return response?.data;
  } catch (error) {
    console.error('Failed to fetch merchant information:', error);
    throw error;
  }
};

export const getMerchantProducts = async (
  { id }: { id: string | number },
  { page = 0, size = 10 }: { page?: number; size?: number } = {}
) => {
  try {
    console.log("in ra id truyền vào hàm là", id);
    const params: Record<string, any> = { page, size };

    const response = await apis().get(`merchant/${id}/products`, {
      params
    });

    console.log("in ra response get list là:", response.data);
    return response?.data;
  } catch (e) {
    console.error(`Failed to fetch products for merchant ID ${id}:`, e);
    throw e;
  }
};

export const getCategories = async () => {
  try {
    const response = await apis().get('/shop-categories');
    return response?.data;
  } catch (e) {
    console.error('Failed to fetch categories:', e);
    throw e;
  }
};

export const getFilterProducts = async ({
  categoryId,
  query,
  sort,
}: {
  categoryId?: string | number;
  query?: string;
  sort?: string;
}) => {
  try {
    const params: Record<string, any> = {};
    params.status = "APPROVED";
    params.page = 0; 
    params.size = 50; 
    console.log("in ra categoriId là: ", categoryId)
    if (categoryId) params.shopCategoryId = categoryId;
    if (query) params.query = query;
    if (sort) {
      params.sortBy = get(PRODUCT_SORT, `${sort}.value`);
      params.sortDirection = get(PRODUCT_SORT, `${sort}.direction`);
    }
    console.log("in ra para là: ", params)

    const response = await apis().get('/products', { params });
    console.log("in ra sản phẩm sau khi tìm là: ", response.data)
    return response?.data;
  } catch (e) {
    console.error('Failed to fetch filtered products:', e);
    throw e;
  }
};

export const getRateableOrders = async (
  { page = 0, size = 10, rated = false } = {}
) => {
  try {
    const params = { page, size, rated };
    const response = await apis().get('/orders/rates', { params });
    return response;
  } catch (e) {
    console.error('Failed to fetch rateable orders:', e);
    throw e;
  }
};

export const createProductRating = async ({ 
  productId, 
  orderItemId, 
  rate, 
  comment, 
  imageUrls 
}: { 
  productId: string | number, 
  orderItemId: string | number, 
  rate: number, 
  comment: string, 
  imageUrls: string 
}) => {
  try {
    console.log("in ra comment là: ", comment)
    const response = await apis().post('/products/rate', {
      productId,
      orderItemId,
      rate,
      comment,
      imageUrls,
    });
    return response;
  } catch (e) {
    console.error('Failed to create product rating:', e);
    throw e;
  }
};

export const getProductRatings = async ({ id, page = 0, size = 10 }) => {
  try {
    const params = { page, size, productId: id };
    const response = await apis().get('/products/rate', { params });
    return response.data;
  } catch (e) {
    console.error(`Failed to fetch ratings for product ID ${id}:`, e);
    throw e;
  }
};

export const getUserRatings = async (
  { page = 0, size = 10 } = {}
) => {
  try {
    const params = { page, size };
    const response = await apis().get('/products/rate/user', { params });
    return response;
  } catch (e) {
    console.error('Failed to fetch user ratings:', e);
    throw e;
  }
};