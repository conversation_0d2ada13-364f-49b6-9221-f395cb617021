import React, { useState, useCallback } from 'react';
import { SafeAreaView, StyleSheet, ScrollView, View, ActivityIndicator } from 'react-native';
import { Text, Divider, Button } from '@ui-kitten/components';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import ListProductPayment from './components/ListProductPayment';
import PaymentAddress from '../Payment/components/PaymentAdress';
import CouponSelector from './components/CouponSelector';
import { usePaymentPreview } from './components/ListProductPayment/hooks';
import { usePaymentAddress } from '../Payment/components/PaymentAdress/hooks';
import { useCheckoutConfirm } from './hooks';
import HomeHeader from 'components/Header/HomeHeader';
import metrics from 'themes/metrics';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import { Coupon } from 'types/coupon';

const PreviewPaymentScreen = () => {
  const navigation = useNavigation();
  const { cartData, coupons, selectedCoupon, updatePreviewWithCoupon, refresh, resetUnauthorized, loading: cartLoading, error: cartError } = usePaymentPreview();
  const { addresses, defaultAddress, loading: addressLoading, error: addressError } = usePaymentAddress();
  const { confirmCheckout, loading: checkoutLoading } = useCheckoutConfirm();
  const [userNotes, setUserNotes] = useState<{ [key: number]: string }>({});

  const handleUserNoteChange = (cartId: number, note: string) => {
    setUserNotes((prev) => ({ ...prev, [cartId]: note }));
  };

  const handleCouponSelect = (coupon: Coupon | null) => {
    if (defaultAddress) {
      updatePreviewWithCoupon(defaultAddress.id.toString(), coupon);
    }
  };

  const hasAddress = addresses && addresses.length > 0;

  const handleCheckout = async () => {
    if (!hasAddress || !defaultAddress) {
      navigation.navigate('AddressBook', { returnToCheckout: true });
      return;
    }

    try {
      const userNoteArray = Object.keys(userNotes).map((cartId) => ({
        cartId: parseInt(cartId),
        userNote: userNotes[cartId] || '',
      }));

      const checkoutData = {
        addressId: defaultAddress.id,
        userCouponId: selectedCoupon?.id || 0,
        couponHathyoCode: selectedCoupon?.code || '',
        userNote: userNoteArray,
      };

      const result = await confirmCheckout(checkoutData);
      console.log('Checkout successful:', result);
      Toast.show({ type: 'success', text1: 'Đặt hàng thành công', position: 'top' });
      navigation.navigate('Order');
    } catch (error) {
      console.error('Checkout failed:', error);
      Toast.show({ type: 'error', text1: 'Đặt hàng thất bại', position: 'top' });
      navigation.navigate('Cart');
    }
  };

  const navigateToAddressBook = () => {
    navigation.navigate('AddressBook', { returnToCheckout: true });
  };

  useFocusEffect(
    useCallback(() => {
      if (cartError?.includes('401 NOT_AUTHORIZED')) {
        Toast.show({
          type: 'error',
          text1: 'Phiên đăng nhập hết hạn',
          text2: 'Vui lòng đăng nhập lại.',
          position: 'top',
        });
        navigation.navigate('Login', { onLoginSuccess: resetUnauthorized });
        return () => {};
      }
      refresh();
      return () => {};
    }, [refresh, cartError, navigation, resetUnauthorized])
  );

  if (cartLoading || addressLoading || checkoutLoading) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <HomeHeader noStretch backButton title="Giỏ hàng" rightComponent={<View />} />
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors['Primary/300']} />
          <Text style={styles.loadingText}>Đang tải giỏ hàng...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (cartError || addressError) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <HomeHeader noStretch backButton title="Giỏ hàng" rightComponent={<View />} />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {cartError === 'Coupon is used' ? 'Mã giảm giá không hợp lệ. Vui lòng thử lại.' :
             cartError?.includes('401 NOT_AUTHORIZED') ? 'Phiên đăng nhập hết hạn. Vui lòng đăng nhập lại.' :
             cartError || addressError || 'Có lỗi xảy ra. Vui lòng thử lại.'}
          </Text>
          {!hasAddress && (
            <Button style={styles.addAddressButton} onPress={navigateToAddressBook}>
              Thêm địa chỉ
            </Button>
          )}
          {hasAddress && !cartError?.includes('401 NOT_AUTHORIZED') && (
            <Button
              style={styles.retryButton}
              onPress={() => defaultAddress && updatePreviewWithCoupon(defaultAddress.id.toString(), null)}
            >
              Thử lại
            </Button>
          )}
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <HomeHeader noStretch backButton title="Giỏ hàng" rightComponent={<View />} />
      <ScrollView style={styles.outerWrapper}>
        {!hasAddress ? (
          <View style={styles.noAddressContainer}>
            <Text style={styles.noAddressText}>Trước tiên, bạn phải thêm địa chỉ thanh toán</Text>
            <Button style={styles.addAddressButton} onPress={navigateToAddressBook}>
              Thêm địa chỉ
            </Button>
          </View>
        ) : (
          <PaymentAddress />
        )}
        <Divider style={styles.divider} />
        <CouponSelector
          coupons={coupons}
          selectedCoupon={selectedCoupon}
          onSelectCoupon={handleCouponSelect}
          totalPrice={cartData?.totalPrice || 0}
        />
        <Divider style={styles.divider} />
        <ListProductPayment
          cartData={cartData}
          onUserNoteChange={handleUserNoteChange}
          loading={cartLoading} // Pass loading prop
        />
        <Divider style={styles.divider} />
        {cartData && (
          <View style={styles.summaryContainer}>
            <Text category="s1" style={styles.summaryTitle}>Tóm tắt đơn hàng</Text>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Tổng tiền sản phẩm:</Text>
              <Text style={styles.summaryValue}>{(cartData.totalProductsPrice ?? 0).toLocaleString('vi-VN')} VNĐ</Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Phí vận chuyển:</Text>
              <Text style={styles.summaryValue}>{(cartData.totalShippingPrice ?? 0).toLocaleString('vi-VN')} VNĐ</Text>
            </View>
            {cartData.discountProductsPrice > 0 && (
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Giảm giá sản phẩm:</Text>
                <Text style={styles.summaryValue}>-{(cartData.discountProductsPrice ?? 0).toLocaleString('vi-VN')} VNĐ</Text>
              </View>
            )}
            {cartData.discountShippingPrice > 0 && (
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Giảm giá vận chuyển:</Text>
                <Text style={styles.summaryValue}>-{(cartData.discountShippingPrice ?? 0).toLocaleString('vi-VN')} VNĐ</Text>
              </View>
            )}
            <View style={styles.summaryRow}>
              <Text style={[styles.summaryLabel, styles.bold]}>Tổng cộng:</Text>
              <Text style={[styles.summaryValue, styles.bold, { color: colors['Primary/300'] }]}>
                {(cartData.totalPrice ?? 0).toLocaleString('vi-VN')} VNĐ
              </Text>
            </View>
          </View>
        )}
        <View style={styles.checkoutButtonContainer}>
          <Text style={styles.privacyText}>
            Bằng cách đặt hàng, bạn đồng ý với chính sách bảo mật của chúng tôi
          </Text>
          <Button
            style={styles.checkoutButton}
            onPress={handleCheckout}
            disabled={!hasAddress || checkoutLoading || !cartData}
          >
            {!hasAddress ? 'Thêm địa chỉ và thanh toán' : checkoutLoading ? 'Đang xử lý...' : 'Đặt hàng'}
          </Button>
        </View>
        <View style={styles.gap} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: 'white',
  },
  outerWrapper: {
    flex: 1,
    backgroundColor: colors['Grayiron/50'],
  },
  divider: {
    marginVertical: metrics.spacing2,
    backgroundColor: colors['Grayiron/200'],
  },
  noAddressContainer: {
    backgroundColor: 'white',
    padding: metrics.spacing2,
    borderRadius: metrics.radius2,
    marginHorizontal: metrics.spacing2,
    marginVertical: metrics.spacing1,
    alignItems: 'center',
  },
  noAddressText: {
    color: colors['Grayiron/500'],
    marginBottom: metrics.spacing2,
    textAlign: 'center',
  },
  addAddressButton: {
    backgroundColor: colors['Primary/300'],
    borderColor: colors['Primary/300'],
  },
  noCartContainer: {
    backgroundColor: 'white',
    padding: metrics.spacing2,
    borderRadius: metrics.radius2,
    marginHorizontal: metrics.spacing2,
    alignItems: 'center',
  },
  noCartText: {
    color: colors['Grayiron/500'],
    marginBottom: metrics.spacing2,
    textAlign: 'center',
  },
  summaryContainer: {
    backgroundColor: 'white',
    padding: metrics.spacing2,
    borderRadius: metrics.radius2,
    marginHorizontal: metrics.spacing2,
    marginBottom: metrics.spacing2,
  },
  summaryTitle: {
    fontWeight: '600',
    marginBottom: metrics.spacing2,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: metrics.spacing1,
  },
  summaryLabel: {
    color: colors['Grayiron/500'],
  },
  summaryValue: {
    color: colors['Grayiron/700'],
  },
  bold: {
    fontWeight: '600',
  },
  checkoutButtonContainer: {
    padding: metrics.spacing2,
    backgroundColor: 'white',
  },
  privacyText: {
    fontSize: 11,
    color: colors['Grayiron/500'],
    textAlign: 'left',
    marginBottom: metrics.spacing2,
    lineHeight: 16,
  },
  checkoutButton: {
    backgroundColor: colors['Moss/500'],
  },
  gap: {
    height: 30,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: metrics.spacing2,
    color: colors['Grayiron/700'],
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: metrics.spacing2,
  },
  errorText: {
    color: colors['Danger/500'],
    marginBottom: metrics.spacing2,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: colors['Primary/300'],
    borderColor: colors['Primary/300'],
    marginTop: metrics.spacing1,
  },
});

export default PreviewPaymentScreen;