import React, { useState, useRef } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  View,
  Text,
  TextInput,
  ActivityIndicator,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import metrics from 'themes/metrics';
import colors from 'themes/colors';
import fonts from 'themes/fonts';
import { useChangePassword } from './hooks';
import HomeHeader from 'components/Header/HomeHeader';
import CustomAlert from 'utils/widgets/CustomAlert';

type RootStackParamList = {
  ChangePassword: undefined;
  Login: undefined;
};

type NavigationProps = NativeStackNavigationProp<RootStackParamList>;

interface AlertState {
  visible: boolean;
  type: 'success' | 'error' | 'confirm';
  title: string;
  message?: string;
  buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[];
}

const ChangePassword: React.FC = () => {
  const { loading, error, changePassword } = useChangePassword();
  const navigation = useNavigation<NavigationProps>();
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmationPassword, setConfirmationPassword] = useState('');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmationPassword, setShowConfirmationPassword] = useState(false);
  const [isChanging, setIsChanging] = useState(false);
  const [alert, setAlert] = useState<AlertState | null>(null);
  const [validationError, setValidationError] = useState<string | null>(null);
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const changeButtonAnim = useRef(new Animated.Value(1)).current;

  React.useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 6,
      tension: 60,
      useNativeDriver: true,
    }).start();
  }, [scaleAnim]);

  const handleButtonPressIn = () => {
    Animated.spring(changeButtonAnim, {
      toValue: 0.95,
      friction: 6,
      tension: 60,
      useNativeDriver: true,
    }).start();
  };

  const handleButtonPressOut = () => {
    Animated.spring(changeButtonAnim, {
      toValue: 1,
      friction: 6,
      tension: 60,
      useNativeDriver: true,
    }).start();
  };

  const showAlert = (alertState: AlertState) => {
    setAlert(alertState);
  };

  const hideAlert = () => {
    setAlert(null);
  };

  const validateInputs = () => {
    if (!currentPassword || !newPassword || !confirmationPassword) {
      setValidationError('Please fill in all password fields.');
      return false;
    }
    if (newPassword !== confirmationPassword) {
      setValidationError('New password and confirmation password do not match.');
      return false;
    }
    if (newPassword.length < 8) {
      setValidationError('New password must be at least 8 characters long.');
      return false;
    }
    if (!/[A-Z]/.test(newPassword) || !/[0-9]/.test(newPassword)) {
      setValidationError('New password must contain at least one uppercase letter and one number.');
      return false;
    }
    setValidationError(null);
    return true;
  };

  const handleChangePassword = async () => {
    if (!validateInputs()) {
      showAlert({
        visible: true,
        type: 'error',
        title: 'Error',
        message: validationError || 'Invalid input.',
        buttons: [{ text: 'Close', onPress: hideAlert }],
      });
      return;
    }

    setIsChanging(true);
    try {
      const response = await changePassword({
        body: {
          currentPassword,
          newPassword,
          confirmationPassword,
        },
      });
      console.log('handleChangePassword Response:', response); // Debug log
      showAlert({
        visible: true,
        type: 'success',
        title: 'Success',
        message: response?.message || 'Password changed successfully! You will be logged out.',
        buttons: [
          {
            text: 'Go to Login',
            onPress: () => {
              hideAlert();
              navigation.navigate('Login');
            },
          },
        ],
      });
      setCurrentPassword('');
      setNewPassword('');
      setConfirmationPassword('');
    } catch (err: any) {
      console.error('handleChangePassword Error:', err); // Debug log
      let errorMessage = err.message || 'Failed to change password. Please try again.';
      if (err.message.includes('Unauthorized')) {
        errorMessage = 'Session expired. Please log in again.';
      }
      showAlert({
        visible: true,
        type: 'error',
        title: 'Error',
        message: errorMessage,
        buttons: [
          {
            text: 'Close',
            onPress: hideAlert,
          },
          ...(errorMessage.includes('log in again')
            ? [{
                text: 'Log In',
                onPress: () => {
                  hideAlert();
                  navigation.navigate('Login');
                },
              }]
            : []),
        ],
      });
    } finally {
      setIsChanging(false);
    }
  };

  if (loading && !isChanging) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors['Primary/500']} />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  if (error && !isChanging) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons
          name="alert-circle-outline"
          size={40}
          color={colors['Danger/500']}
        />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => navigation.navigate('Login')}
          activeOpacity={0.7}
        >
          <Text style={styles.retryButtonText}>Go to Login</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.safeAreaView}>
      <HomeHeader backButton title="Change Password" noStretch />
      <View style={styles.contentContainer}>
        <Animated.View style={[styles.card, { transform: [{ scale: scaleAnim }] }]}>
          <MaterialCommunityIcons
            name="lock-outline"
            size={36}
            color={colors['Primary/600']}
            style={styles.icon}
          />
          <Text style={styles.title}>Change Password</Text>
          <Text style={styles.description}>
            Enter your current password and a new password to update your account.
          </Text>
          {validationError && (
            <Text style={styles.validationErrorText}>{validationError}</Text>
          )}
          <View style={[styles.inputContainer, validationError && styles.inputError]}>
            <TextInput
              style={styles.passwordInput}
              value={currentPassword}
              onChangeText={setCurrentPassword}
              placeholder="Current Password"
              placeholderTextColor={colors['Gray/400']}
              secureTextEntry={!showCurrentPassword}
              autoCapitalize="none"
              accessibilityLabel="Current password input"
            />
            <TouchableOpacity
              onPress={() => setShowCurrentPassword(!showCurrentPassword)}
              style={styles.eyeIcon}
            >
              <MaterialCommunityIcons
                name={showCurrentPassword ? 'eye-off' : 'eye'}
                size={24}
                color={colors['Gray/600']}
              />
            </TouchableOpacity>
          </View>
          <View style={[styles.inputContainer, validationError && styles.inputError]}>
            <TextInput
              style={styles.passwordInput}
              value={newPassword}
              onChangeText={setNewPassword}
              placeholder="New Password"
              placeholderTextColor={colors['Gray/400']}
              secureTextEntry={!showNewPassword}
              autoCapitalize="none"
              accessibilityLabel="New password input"
            />
            <TouchableOpacity
              onPress={() => setShowNewPassword(!showNewPassword)}
              style={styles.eyeIcon}
            >
              <MaterialCommunityIcons
                name={showNewPassword ? 'eye-off' : 'eye'}
                size={24}
                color={colors['Gray/600']}
              />
            </TouchableOpacity>
          </View>
          <View style={[styles.inputContainer, validationError && styles.inputError]}>
            <TextInput
              style={styles.passwordInput}
              value={confirmationPassword}
              onChangeText={setConfirmationPassword}
              placeholder="Confirm New Password"
              placeholderTextColor={colors['Gray/400']}
              secureTextEntry={!showConfirmationPassword}
              autoCapitalize="none"
              accessibilityLabel="Confirm password input"
            />
            <TouchableOpacity
              onPress={() => setShowConfirmationPassword(!showConfirmationPassword)}
              style={styles.eyeIcon}
            >
              <MaterialCommunityIcons
                name={showConfirmationPassword ? 'eye-off' : 'eye'}
                size={24}
                color={colors['Gray/600']}
              />
            </TouchableOpacity>
          </View>
          <Animated.View style={{ transform: [{ scale: changeButtonAnim }] }}>
            <TouchableOpacity
              style={[styles.changeButton, isChanging && styles.disabledButton]}
              onPress={handleChangePassword}
              disabled={isChanging}
              onPressIn={handleButtonPressIn}
              onPressOut={handleButtonPressOut}
              activeOpacity={0.7}
              accessibilityRole="button"
              accessibilityLabel="Change password"
            >
              <Text style={styles.changeButtonText}>
                {isChanging ? 'Changing...' : 'Change Password'}
              </Text>
            </TouchableOpacity>
          </Animated.View>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={() => navigation.goBack()}
            activeOpacity={0.7}
            accessibilityRole="button"
            accessibilityLabel="Cancel"
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </Animated.View>
      </View>
      {alert && (
        <CustomAlert
          visible={alert.visible}
          type={alert.type}
          title={alert.title}
          message={alert.message}
          buttons={alert.buttons}
          onDismiss={hideAlert}
          dismissable
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
    backgroundColor: colors['Gray/50'],
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: metrics.spacing3,
    paddingVertical: metrics.spacing4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  card: {
    backgroundColor: colors.white,
    borderRadius: metrics.radius3,
    padding: metrics.spacing3,
    width: '100%',
    maxWidth: 400,
    alignItems: 'center',
    shadowColor: colors['Gray/700'],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 4,
  },
  icon: {
    marginBottom: metrics.spacing2,
  },
  title: {
    ...fonts.style.h3,
    fontWeight: '600',
    color: colors['Gray/800'],
    marginBottom: metrics.spacing1,
  },
  description: {
    ...fonts.style.normal,
    color: colors['Gray/600'],
    textAlign: 'center',
    marginBottom: metrics.spacing3,
    lineHeight: 20,
  },
  inputContainer: {
    width: '100%',
    backgroundColor: colors['Gray/50'],
    borderRadius: metrics.radius2,
    borderWidth: 1,
    borderColor: colors['Gray/200'],
    marginBottom: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    flexDirection: 'row',
    alignItems: 'center',
  },
  inputError: {
    borderColor: colors['Danger/500'],
  },
  passwordInput: {
    ...fonts.style.normal,
    color: colors['Gray/800'],
    paddingVertical: metrics.spacing2,
    flex: 1,
  },
  eyeIcon: {
    padding: metrics.spacing1,
  },
  validationErrorText: {
    ...fonts.style.normal,
    color: colors['Danger/500'],
    marginBottom: metrics.spacing2,
    textAlign: 'center',
  },
  changeButton: {
    backgroundColor: colors['Primary/600'],
    paddingVertical: metrics.spacing2,
    borderRadius: metrics.radius2,
    width: '100%',
    alignItems: 'center',
    marginBottom: metrics.spacing2,
  },
  changeButtonText: {
    ...fonts.style.normal,
    color: colors.white,
    fontWeight: '600',
  },
  disabledButton: {
    backgroundColor: colors['Primary/400'],
    opacity: 0.6,
  },
  cancelButton: {
    paddingVertical: metrics.spacing2,
    borderRadius: metrics.radius2,
    width: '100%',
    alignItems: 'center',
  },
  cancelButtonText: {
    ...fonts.style.normal,
    color: colors['Primary/600'],
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors['Gray/50'],
  },
  loadingText: {
    ...fonts.style.normal,
    color: colors['Gray/600'],
    marginTop: metrics.spacing2,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: metrics.spacing3,
    backgroundColor: colors['Gray/50'],
  },
  errorText: {
    ...fonts.style.normal,
    color: colors['Danger/500'],
    textAlign: 'center',
    marginVertical: metrics.spacing2,
  },
  retryButton: {
    backgroundColor: colors['Primary/50'],
    paddingVertical: metrics.spacing2,
    paddingHorizontal: metrics.spacing3,
    borderRadius: metrics.radius2,
    alignItems: 'center',
  },
  retryButtonText: {
    ...fonts.style.normal,
    color: colors['Primary/600'],
    fontWeight: '600',
  },
});

export default ChangePassword;