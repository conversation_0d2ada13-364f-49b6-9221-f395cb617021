import React from 'react';
import { StyleSheet, Text, TouchableOpacity } from 'react-native';
import SimpleLineIcons from 'react-native-vector-icons/SimpleLineIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import { useUserStore } from 'stores/user';
import { STORAGE } from 'constants/authentication';
import { useCart } from 'hooks/contexts/CartContext';
 // Import useCart từ CartContext

const LogoutButton = () => {
  const setUser = useUserStore((state: any) => state?.setUser);
  const { setCartCount } = useCart(); 

  const logout = async () => {
    try {
      await AsyncStorage.multiRemove([
        STORAGE.ACCESS_TOKEN,
        STORAGE.REFRESH_TOKEN,
        STORAGE.EMAIL,
        'cartCount', 
      ]);
      setUser({}); 
      setCartCount(0); 
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <TouchableOpacity
      onPress={logout}
      style={[commonStyles.rowCenter, styles.container]}
    >
      <SimpleLineIcons
        style={styles.icon}
        color={colors['Grayiron/500']}
        name={'logout'}
        size={20}
      />
      <Text style={styles.title}>Đăng xuất</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: metrics.spacing4,
    marginBottom: metrics.spacing8,
  },
  icon: {
    marginRight: metrics.spacing3,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/500'],
  },
});

export default LogoutButton;