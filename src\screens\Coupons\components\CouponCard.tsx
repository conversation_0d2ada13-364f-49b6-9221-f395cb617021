import React from 'react';
import { StyleSheet, View, Text, Image, TouchableOpacity, Animated } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import { Coupon } from 'types/Coupon';

const mossColors = {
  primary: '#4A7043',
  primaryDark: '#3A5A34',
  primaryLight: '#6B8E23',
  accent: '#8BAF7A',
  text: '#2F3D2A',
  background: '#F9FBF7',
  disabled: '#A8B5A2',
  warning: '#C0392B',
};

interface CouponCardProps {
  coupon: Coupon;
  onGoToCart: () => void;
  onPress: () => void;
}

const CouponCard: React.FC<CouponCardProps> = ({ coupon, onGoToCart, onPress }) => {
  const scaleAnim = new Animated.Value(1);

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.98,
      friction: 8,
      tension: 100,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 8,
      tension: 100,
      useNativeDriver: true,
    }).start();
  };

  const formatDate = (dateStr: string): string => {
    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return 'Không có';
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      return `${day}/${month}`;
    } catch {
      return 'Không có';
    }
  };

  const getDiscountDisplay = (): string => {
    if (coupon.discountType === 'PERCENT' && coupon.discountPercent !== null) {
      return `${coupon.discountPercent}%`;
    }
    if (coupon.discountValue !== null) {
      return `${coupon.discountValue.toLocaleString()}đ`;
    }
    return 'Không có';
  };

  if (!coupon.id || !coupon.code) {
    console.warn('Bỏ qua hiển thị coupon không hợp lệ:', coupon);
    return null;
  }

  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
      <LinearGradient
        colors={coupon.applyStatus ? ['#FFFFFF', '#F5F7F2'] : ['#E8ECE3', '#D8DCD3']}
        style={styles.couponCard}
      >
        <View style={styles.imageContainer}>
          <Image source={{ uri: coupon.image }} style={styles.couponImage} resizeMode="contain" />
        </View>
        <View style={styles.couponContent}>
          <View style={styles.titleRow}>
            <Text style={styles.couponTitle} numberOfLines={1}>
              {coupon.title}
            </Text>
            <View style={styles.discountContainer}>
              <MaterialCommunityIcons name="tag" size={12} color={mossColors.primaryDark} />
              <Text style={styles.discountText}>{getDiscountDisplay()}</Text>
            </View>
          </View>
          <Text style={styles.couponDescription} numberOfLines={2}>
            {coupon.description}
          </Text>
          <View style={styles.couponDetails}>
            <View style={styles.detailItem}>
              <MaterialCommunityIcons name="calendar" size={12} color={mossColors.primaryDark} />
              <Text style={styles.couponDetail}>Hết hạn: {formatDate(coupon.expiredAt)}</Text>
            </View>
          </View>
          <Text style={styles.warningText} numberOfLines={1}>
            Đơn tối thiểu: {coupon.minimumPriceApply?.toLocaleString()}đ
          </Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.actionButton, styles.cartButton]}
              onPress={onGoToCart}
              onPressIn={handlePressIn}
              onPressOut={handlePressOut}
              activeOpacity={0.7}
            >
              <Animated.View style={[styles.buttonGradient, { transform: [{ scale: scaleAnim }] }]}>
                <LinearGradient
                  colors={['#4A7043', '#3A5A34']}
                  style={styles.buttonInner}
                >
                  <Text style={styles.buttonText}>Đi đến giỏ hàng</Text>
                </LinearGradient>
              </Animated.View>
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  couponCard: {
    flexDirection: 'row',
    borderRadius: metrics.radius2,
    marginBottom: metrics.spacing1_5,
    padding: metrics.spacing1_5,
    borderWidth: 1,
    borderColor: mossColors.accent + '33',
    elevation: 3,
    shadowColor: mossColors.primaryDark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    minHeight: 120,
    maxWidth: '100%',
    alignItems: 'center',
  },
  imageContainer: {
    borderRadius: metrics.radius1_5,
    overflow: 'hidden',
    marginRight: metrics.spacing1_5,
    backgroundColor: '#F2F4EF',
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    padding: metrics.spacing0_5,
  },
  couponImage: {
    width: 50,
    height: 50,
    borderRadius: metrics.radius1,
  },
  couponContent: {
    flex: 1,
    flexShrink: 1,
    justifyContent: 'space-between',
    position: 'relative',
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: metrics.spacing0_5,
  },
  couponTitle: {
    ...fonts.style.normal,
    color: mossColors.text,
    fontWeight: '600',
    fontSize: 13,
    lineHeight: 17,
    flexShrink: 1,
    maxWidth: '60%',
  },
  discountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: mossColors.accent + '22',
    borderRadius: metrics.radius1,
    paddingHorizontal: metrics.spacing1,
    paddingVertical: metrics.spacing0_5,
  },
  discountText: {
    ...fonts.style.description,
    color: mossColors.primaryDark,
    fontSize: 11,
    fontWeight: '600',
    marginLeft: metrics.spacing0_5,
  },
  couponDescription: {
    ...fonts.style.description,
    color: mossColors.text + 'A0',
    fontSize: 11,
    lineHeight: 15,
    marginVertical: metrics.spacing0_5,
  },
  couponDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: metrics.spacing0_5,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flexShrink: 1,
  },
  couponDetail: {
    ...fonts.style.description,
    color: mossColors.primaryDark,
    fontSize: 10,
    fontWeight: '500',
    marginLeft: metrics.spacing0_5,
  },
  warningText: {
    ...fonts.style.description,
    color: mossColors.warning,
    fontSize: 9,
    fontWeight: '500',
    marginTop: metrics.spacing0_5,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: metrics.spacing1,
    gap: metrics.spacing1,
  },
  actionButton: {
    borderRadius: metrics.radius1_5,
    overflow: 'hidden',
    width: 100,
    height: 32,
  },
  cartButton: {
    backgroundColor: mossColors.primary,
  },
  buttonGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonInner: {
    paddingVertical: metrics.spacing0_5,
    paddingHorizontal: metrics.spacing2,
    borderRadius: metrics.radius1_5,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
  },
  buttonText: {
    ...fonts.style.normal,
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 11,
    textAlign: 'center',
  },
});

export default CouponCard;