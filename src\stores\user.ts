import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  getUser,
  getUserFitness,
  patchUser,
  postUserFitness,
} from 'api/users';
import { STORAGE } from 'constants/authentication';
import Toast from 'react-native-toast-message';
import { create } from 'zustand';

export const useUserStore = create(set => ({
  user: {},
  userFitness: {},
  loading: false,
  setUser: (user: any) => {
    set({ user });
  },
  fetchUserFitness: async () => {
    set({ loading: true });
    try {
      const token = await AsyncStorage.getItem(STORAGE.ACCESS_TOKEN);
      if (token) {
        try {
          const response = await getUserFitness();
          console.log(response);
          
          if (response?.height) {
            set({ userFitness: response });
          }
        } catch (error) {
          console.log(error);
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      set({ loading: false });
    }
  },
  fetchUser: async () => {
    set({ loading: true });
    try {
      const token = await AsyncStorage.getItem(STORAGE.ACCESS_TOKEN);
      if (token) {
        try {
          const response = await getUser();
          if (response?.email) {
            set({ user: response });
          }
        } catch (error) {
          console.log(error);
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      set({ loading: false });
    }
  },
  updateUser: async (body: any) => {
    try {
      set({ loading: true });
      const response = await patchUser({
        body,
      });
      if (response?.code) {
        Toast.show({
          type: 'error',
          text1: response?.message || 'Có lỗi xảy ra, vui lòng thử lại',
        });
      } else {
        Toast.show({
          type: 'success',
          text1: 'Cập nhật thông tin thành công',
        });
        set({ user: response });
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: error?.message || 'Có lỗi xảy ra, vui lòng thử lại',
      });
      // console.log(error);
    } finally {
      set({ loading: false });
    }
  },
  updateUserFitness: async (body: any) => {
    try {
      set({ loading: true });
      const response = await postUserFitness({
        body,
      });
      
      if (response?.code) {
        Toast.show({
          type: 'error',
          text1: response?.message || 'Có lỗi xảy ra, vui lòng thử lại',
        });
      } else {
        Toast.show({
          type: 'success',
          text1: 'Cập nhật thông tin thành công',
        });
        set({ userFitness: response });
      }
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: error?.message || 'Có lỗi xảy ra, vui lòng thử lại',
      });
      // console.log(error);
    } finally {
      set({ loading: false });
    }
  },
}));
