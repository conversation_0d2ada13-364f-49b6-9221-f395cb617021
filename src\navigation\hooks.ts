/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect } from 'react';
import { useProductsStore } from 'stores/products';
import { useTopicStore } from 'stores/topics';
import { useUserStore } from 'stores/user';

export const useLaunching = () => {
  const fetchUser = useUserStore((state: any) => state?.fetchUser);
  const fetchTopics = useTopicStore((state: any) => state?.fetchTopics);
  const fetchCategories = useProductsStore(
    (state: any) => state?.fetchCategories,
  );

  useEffect(() => {
    fetchTopics();
    fetchUser();
    fetchCategories();
  }, []);
};
