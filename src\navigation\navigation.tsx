/* eslint-disable react/no-unstable-nested-components */
import React, { useState, useEffect } from 'react';
import * as eva from '@eva-design/eva';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { NavigationContainer, useNavigation } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { ApplicationProvider, IconRegistry } from '@ui-kitten/components';
import { EvaIconsPack } from '@ui-kitten/eva-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE } from 'constants/authentication';
import AllCategories from 'screens/Shop/components/ShopMenu/componets/AllCategories';
import Home from 'screens/Home/Home.screen';
import Account from 'screens/AccountTab/Account/Account.screen';
import Profile from 'screens/AccountTab/Profile/Profile.screen';
import AddressBook from 'screens/AccountTab/AddressBook/AddressBook.screen';
import AddressDetail from 'screens/AccountTab/AddressDetail/AddressDetail.screen';
import Icon from 'react-native-vector-icons/Ionicons';
import Post from 'screens/Post/Post.screen';
import Shop from 'screens/Shop/Shop.screen';
import Tool from 'screens/Tool/Tool.screen';
import TopicDetail from 'screens/TopicDetail/TopicDetail.screen';
import PostDetail from 'screens/PostDetail/PostDetail.screen';
import PreviewPayment from 'screens/Payment/PreviewPayment.screen';
import OrderScreen from 'screens/Order/Order.screen';
import { default as theme } from '../../theme.json';
import colors from 'themes/colors';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/vi';
import dayjs from 'dayjs';
import ProductDetail from 'screens/ProductDetail/ProductDetail.screen';
import Authentication from 'screens/Authentication/Authentication.screen';
import { useLaunching } from './hooks';
import ProductFilter from 'screens/ProductFilter/ProductFilter.screen';
import Cart from 'screens/Cart/Cart.screen';
import WebviewScreen from 'screens/WebviewScreen/WebviewScreen.screen';
import HealthProfile from 'screens/AccountTab/HealthProfile/HealthProfile.screen';
import OrderDetailScreen from 'screens/Order/components/OrderDetail/OrderDetail.screen';
import MerchantScreen from 'screens/Merchant/Merchant.screen';
import AccountManager from 'screens/AccountTab/Account/components/AccountItems/components/AccountManager';
import ConfirmDeleteAccount from 'screens/AccountTab/Account/components/AccountItems/components/AccountManager/components/ConfirmDeleteAccount';
import DeleteAccount from 'screens/AccountTab/Account/components/AccountItems/components/AccountManager/components/DeleteAccount';
import CouponsScreen from 'screens/Coupons/Coupons.screen';
import ChangePassword from 'screens/AccountTab/Account/components/AccountItems/components/AccountManager/components/ChangePassword';
import SystemReportScreen from 'screens/SystemReport/SystemReport.screen';

dayjs.extend(relativeTime);
dayjs.locale('vi');

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();

function MyTabs() {
  return (
    <Tab.Navigator>
      <Tab.Screen
        name="Trang chủ"
        component={Home}
        options={{
          headerShown: false,
          tabBarIcon: ({ color }: any) => {
            return <Icon name={'home'} size={25} color={color} />;
          },
          tabBarActiveTintColor: colors['Moss/500'],
          tabBarInactiveTintColor: colors['Grayiron/500'],
        }}
      />
      <Tab.Screen
        name="Bài viết"
        component={Post}
        options={{
          headerShown: false,
          tabBarIcon: ({ color }: any) => {
            return <Icon name={'reader'} size={25} color={color} />;
          },
          tabBarActiveTintColor: colors['Moss/500'],
          tabBarInactiveTintColor: colors['Grayiron/500'],
        }}
      />
      <Tab.Screen
        name="Cửa hàng"
        component={Shop}
        options={{
          headerShown: false,
          tabBarIcon: ({ color }: any) => {
            return <Icon name={'storefront'} size={25} color={color} />;
          },
          tabBarActiveTintColor: colors['Moss/500'],
          tabBarInactiveTintColor: colors['Grayiron/500'],
        }}
      />
      {/* <Tab.Screen
        name="Công cụ"
        component={Tool}
        options={{
          headerShown: false,
          tabBarIcon: ({ color }: any) => {
            return <Icon name={'calculator'} size={25} color={color} />;
          },
          tabBarActiveTintColor: colors['Moss/500'],
          tabBarInactiveTintColor: colors['Grayiron/500'],
        }}
      /> */}
      <Tab.Screen
        name="Tài khoản"
        component={Account}
        options={{
          headerShown: false,
          tabBarIcon: ({ color }: any) => {
            return <Icon name={'person'} size={25} color={color} />;
          },
          tabBarActiveTintColor: colors['Moss/500'],
          tabBarInactiveTintColor: colors['Grayiron/500'],
        }}
      />
    </Tab.Navigator>
  );
}

const MainNavigation = () => {
  const [initialRoute, setInitialRoute] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkInitialRoute = async () => {
      try {
        const storedEmail = await AsyncStorage.getItem(STORAGE.EMAIL);
        const accessToken = await AsyncStorage.getItem(STORAGE.ACCESS_TOKEN);

        if (storedEmail === '<EMAIL>' && accessToken) {
          setInitialRoute('SystemReportScreen');
        } else {
          setInitialRoute('AppTab');
        }
      } catch (error) {
        console.error('Error checking initial route:', error);
        setInitialRoute('AppTab');
      } finally {
        setIsLoading(false);
      }
    };

    checkInitialRoute();
  }, []);

  useLaunching();

  if (isLoading || !initialRoute) {
    return null; // Hoặc có thể return một loading screen
  }

  return (
    <NavigationContainer>
      <ApplicationProvider {...eva} theme={{ ...eva.light, ...theme }}>
        <IconRegistry icons={EvaIconsPack} />
        <Stack.Navigator
          screenOptions={{ headerShown: false }}
          initialRouteName={initialRoute}
        >
          <Stack.Screen
            name="AppTab"
            options={{ headerShown: false }}
            component={MyTabs}
          />
          <Stack.Screen
            name="PostDetail"
            component={PostDetail}
          />
          <Stack.Screen
            name="TopicDetail"
            component={TopicDetail}
          />
          <Stack.Screen
            name="AllCategories"
            component={AllCategories}
          />
          <Stack.Screen
            name="CouponsScreen"
            component={CouponsScreen}
          />
   <Stack.Screen
            name="Order"
            component={OrderScreen}
          />
          <Stack.Screen
            name="ProductDetail"
            component={ProductDetail}
          />
          <Stack.Screen
            name="AccountManager"
            component={AccountManager}
          />
          <Stack.Screen
            name="ConfirmDeleteAccount"
            component={ConfirmDeleteAccount}
          />
          <Stack.Screen
            name="DeleteAccount"
            component={DeleteAccount}
          />
           <Stack.Screen
            name="ChangePassword"
            component={ChangePassword}
          />
          <Stack.Screen
            name="PreviewPayment"
            component={PreviewPayment}
          />
          <Stack.Screen
            name="Authentication"
            component={Authentication}
          />
          <Stack.Screen
            name="ProductFilter"
            component={ProductFilter}
          />
          <Stack.Screen
            name="Cart"
            component={Cart}
          />
          <Stack.Screen
            name="Merchant"
            component={MerchantScreen}
          />
          <Stack.Screen
            name="Profile"
            component={Profile}
          />
          <Stack.Screen
            name="HealthProfile"
            component={HealthProfile}
          />
          <Stack.Screen
            name="AddressBook"
            component={AddressBook}
          />
          <Stack.Screen
            name="OrderDetail"
            component={OrderDetailScreen}
          />
          <Stack.Screen
            name="AddressDetail"
            component={AddressDetail}
          />
          <Stack.Screen
            name="SystemReportScreen"
            component={SystemReportScreen}
          />
          <Stack.Screen
            name="WebviewScreen"
            component={WebviewScreen}
          />
        </Stack.Navigator>
      </ApplicationProvider>
    </NavigationContainer>
  );
};

export default MainNavigation;