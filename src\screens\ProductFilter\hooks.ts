import { getFilterProducts } from 'api/products';
import { useEffect, useState, useCallback } from 'react';
import { useProductsStore } from 'stores/products';

export const useFilterProducts = () => {
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState([]);
  
  // Lấy filter từ store
  const filter = useProductsStore((state: any) => state?.filter || {});
  const setFilter = useProductsStore((state: any) => state?.setFilter);

  // Hàm fetch sản phẩm
  const fetchProducts = useCallback(async () => {
    if (!filter) return;
    
    try {
      setLoading(true);
      console.log('Fetching products with filter:', filter);
      
      const response = await getFilterProducts({
        ...filter,
        categoryId: filter.categoryId
      });
      
      //console.log('API response:', response);
      
      if (Array.isArray(response)) {
        setProducts(response);
      } else if (response && typeof response === 'object') {
        // Nếu response là object có thể chứa mảng sản phẩm trong một thuộc tính
        const productArray = response.data || response.products || response.items || [];
        setProducts(Array.isArray(productArray) ? productArray : []);
      } else {
        console.warn('Unexpected response format:', response);
        setProducts([]);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  }, [filter]);

  // Gọi API khi filter thay đổi
  useEffect(() => {
    fetchProducts();
  }, [filter?.sort, filter?.categoryId, filter?.keywords, fetchProducts]);

  // Hàm update filter với nhiều tùy chọn
  const updateFilter = useCallback((newFilterValues: any) => {
    if (setFilter) {
      setFilter({
        ...filter,
        ...newFilterValues,
      });
    }
  }, [filter, setFilter]);

  return {
    loading,
    products,
    filter,
    setFilter: updateFilter,
    refetch: fetchProducts,
  };
};