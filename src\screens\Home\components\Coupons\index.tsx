import React, { useEffect } from 'react';
import { View, ScrollView, StyleSheet, Dimensions, Animated, TouchableOpacity, Image, ImageBackground } from 'react-native';
import { Text, Card } from '@ui-kitten/components';
import Icon from 'react-native-vector-icons/FontAwesome6';
import LinearGradient from 'react-native-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import { useCoupons } from './hooks';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import { Coupon } from 'types/coupon';

// Screen dimensions
const { width: screenWidth } = Dimensions.get('window');
const couponWidth = screenWidth * 0.7;
const couponHeight = couponWidth * 0.54;

const Coupons = () => {
  const { coupons, loading, error } = useCoupons(0, 'HATHYO', 0, 5);
  const navigation = useNavigation();
  const scaleAnim = React.useRef(new Animated.Value(0.9)).current;
  const fadeAnim = React.useRef(new Animated.Value(0)).current;

  useEffect(() => {
    console.log('Coupons state:', { loading, error, coupons });
  }, [loading, error, coupons]);

  useEffect(() => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 6,
        tension: 50,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      })
    ]).start();
  }, [scaleAnim, fadeAnim]);

  const handleBuyNow = (coupon: Coupon) => {
    navigation.navigate('Cart');
  };

  const handleViewMore = () => {
    navigation.navigate('CouponsScreen');
  };

  const formatPrice = (price: number) => {
    if (price >= 1000000) {
      return `${(price / 1000000).toFixed(0)}M`;
    } else if (price >= 1000) {
      return `${(price / 1000).toFixed(0)}K`;
    }
    return price.toString();
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    return `${day}/${month}`;
  };

  const renderCoupon = (coupon: Coupon, index: number) => {
    const animatedStyle = {
      transform: [{ scale: scaleAnim }],
      opacity: fadeAnim,
    };

    return (
      <Animated.View key={coupon.id} style={[styles.couponContainer, animatedStyle]}>
        <TouchableOpacity 
          style={styles.couponTouchable}
          onPress={() => handleBuyNow(coupon)}
          activeOpacity={0.9}
        >
          <LinearGradient
            colors={[colors['Primary/100'], colors['white']]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.couponGradient}
          >
            {/* Coupon Image */}
            <View style={styles.imageContainer}>
              <Image
                source={{ uri: coupon.image }}
                style={styles.couponImage}
                resizeMode="contain"
              />
            </View>

            {/* Main Content */}
            <View style={styles.couponContent}>
              {/* Header Row */}
              <View style={styles.headerRow}>
                <View style={styles.discountBadge}>
                  <Text style={styles.discountText}>
                    {coupon.discountType === 'PERCENT'
                      ? `${coupon.discountPercent}%`
                      : formatPrice(coupon.discountValue || 0)}
                  </Text>
                </View>
                <View style={styles.statusDot} />
              </View>

              {/* Title */}
              <Text style={styles.titleText} numberOfLines={1} ellipsizeMode="tail">
                {coupon.title}
              </Text>

              {/* Details */}
              <View style={styles.detailsRow}>
                <Text style={styles.detailText}>
                  Từ {formatPrice(coupon.minimumPriceApply)}
                </Text>
                <Text style={styles.detailText}>
                  HSD: {formatDate(coupon.expiredAt)}
                </Text>
              </View>

              {/* Action Button */}
              <TouchableOpacity style={styles.actionButton}>
                <Text style={styles.actionButtonText}>Dùng ngay</Text>
              </TouchableOpacity>
            </View>

            {/* Decorative Elements */}
            <View style={styles.decorativeCircles}>
              <View style={[styles.circle, styles.circleLeft]} />
              <View style={[styles.circle, styles.circleRight]} />
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  if (!loading && !error && coupons.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.headerContainer}>
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <Icon name="fire" size={metrics.icons.small} color={colors['Danger/600']} />
            <Text style={styles.sectionTitle}>Ưu đãi hấp dẫn</Text>
          </View>
          {!loading && !error && coupons.length > 0 && (
            <TouchableOpacity
              onPress={handleViewMore}
              style={styles.viewMoreButton}
              accessibilityLabel="Xem thêm các coupon khác"
            >
              <Text style={styles.viewMoreText}>Xem thêm</Text>
              <Icon name="chevron-right" size={metrics.icons.tiny} color={colors['Primary/600']} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Loading State */}
      {loading && (
        <View style={styles.loadingContainer}>
          <Icon name="spinner" size={metrics.icons.medium} color={colors['Primary/600']} />
          <Text style={styles.loadingText}>Đang tải coupon...</Text>
        </View>
      )}

      {/* Error State */}
      {error && (
        <View style={styles.errorContainer}>
          <Icon name="exclamation-triangle" size={metrics.icons.medium} color={colors['Danger/600']} />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      {/* Coupons List */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        decelerationRate="fast"
        snapToInterval={couponWidth + metrics.spacing3}
        snapToAlignment="start"
      >
        {coupons.map((coupon: Coupon, index: number) => renderCoupon(coupon, index))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: metrics.spacing3,
  },
  headerContainer: {
    marginHorizontal: metrics.spacing4,
    marginBottom: metrics.spacing2,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: metrics.spacing2,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: fonts.size.h3,
    fontWeight: '700',
    color: colors['Grayiron/700'],
    marginLeft: metrics.spacing2,
  },
  viewMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors['Primary/100'],
    paddingHorizontal: metrics.spacing2,
    paddingVertical: metrics.spacing1,
    borderRadius: metrics.radius3,
  },
  viewMoreText: {
    fontSize: fonts.size.small,
    fontWeight: '600',
    color: colors['Primary/600'],
    marginRight: metrics.spacing1,
  },
  couponContainer: {
    width: couponWidth,
    height: couponHeight,
    marginLeft: metrics.spacing4,
  },
  couponTouchable: {
    flex: 1,
    borderRadius: metrics.radius4,
    elevation: 4,
    shadowColor: colors['Primary/400'],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  couponGradient: {
    flex: 1,
    borderRadius: metrics.radius4,
    overflow: 'hidden',
    position: 'relative',
  },
  imageContainer: {
    position: 'absolute',
    top: metrics.spacing2,
    right: metrics.spacing2,
    width: 50,
    height: 40,
    borderRadius: metrics.radius2,
    overflow: 'hidden',
    backgroundColor: colors['white'],
  },
  couponImage: {
    width: '100%',
    height: '100%',
    opacity: 0.9,
  },
  couponContent: {
    flex: 1,
    padding: metrics.spacing3,
    justifyContent: 'space-between',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: metrics.spacing2,
  },
  discountBadge: {
    backgroundColor: colors['Primary/600'],
    paddingHorizontal: metrics.spacing2,
    paddingVertical: metrics.spacing1,
    borderRadius: metrics.radius3,
  },
  discountText: {
    fontSize: fonts.size.h3,
    fontWeight: '800',
    color: colors['white'],
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors['Success/500'],
  },
  titleText: {
    fontSize: fonts.size.h3,
    fontWeight: '700',
    color: colors['Grayiron/700'],
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: metrics.spacing2,
  },
  detailText: {
    fontSize: fonts.size.small,
    fontWeight: '600',
    color: colors['Grayiron/600'],
  },
  actionButton: {
    backgroundColor: colors['Moss/500'],
    paddingVertical: metrics.spacing2,
    borderRadius: metrics.radius3,
    alignItems: 'center',
    marginTop: metrics.spacing2,
  },
  actionButtonText: {
    color: colors['white'],
    fontSize: fonts.size.small,
    fontWeight: '700',
  },
  decorativeCircles: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  circle: {
    position: 'absolute',
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors['Gray/50'],
    top: '50%',
    marginTop: -6,
  },
  circleLeft: {
    left: -6,
  },
  circleRight: {
    right: -6,
  },
  scrollContent: {
    paddingRight: metrics.spacing4,
    paddingVertical: metrics.spacing2,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: metrics.spacing3,
  },
  loadingText: {
    color: colors['Primary/600'],
    fontSize: fonts.size.medium,
    fontWeight: '600',
    marginTop: metrics.spacing1,
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: metrics.spacing3,
    backgroundColor: colors['Danger/50'],
    marginHorizontal: metrics.spacing4,
    borderRadius: metrics.radius3,
    marginBottom: metrics.spacing2,
  },
  errorText: {
    color: colors['Danger/700'],
    fontSize: fonts.size.medium,
    fontWeight: '600',
    marginTop: metrics.spacing1,
  },
});

export default Coupons;