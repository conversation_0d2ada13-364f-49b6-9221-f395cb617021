import { useEffect, useState, useCallback, useRef } from 'react';
import { previewPayment } from 'api/payments';
import { getCollectedCoupons } from 'api/coupons';
import { usePaymentAddress } from '../PaymentAdress/hooks';
import { Coupon } from 'type/Coupon';

interface CartItem {
  id: number;
  cartId: number;
  variant: {
    id: number;
    title: string;
    price: number;
    anchoPrice: number;
    imageUrl: string | null;
  };
  quantity: number;
  totalPrice: number;
}

interface Cart {
  id: number;
  merchant: {
    id: number;
    storeName: string;
    logo: string;
  };
  productsPrice: number;
  discountProductsPrice: number;
  totalProductsPrice: number;
  shippingPrice: number;
  discountShippingPrice: number;
  totalShippingPrice: number;
  totalPrice: number;
  cartItems: CartItem[];
}

interface CartData {
  carts: Cart[];
  productsPrice: number;
  discountProductsPrice: number;
  totalProductsPrice: number;
  shippingPrice: number;
  discountShippingPrice: number;
  totalShippingPrice: number;
  totalPrice: number;
}

export const useListProductPayment = () => {
  const { defaultAddress, loading: addressLoading, error: addressError } = usePaymentAddress();
  const [cartData, setCartData] = useState<CartData | null>(null);
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const hasFetchedCoupons = useRef(false);
  const lastTotalPrice = useRef<number | null>(null);
  const isUnauthorized = useRef(false);
  const requestLock = useRef(false);

  const fetchCartData = async (addressId: string, coupon: Coupon | null) => {
    if (requestLock.current || isUnauthorized.current) return;

    requestLock.current = true;
    try {
      setLoading(true);
      setError(null);

      console.log('Calling previewPayment with:', {
        addressId,
        userCouponId: coupon?.id || 0,
        couponHathyoCode: coupon?.code || '',
      });
      const response = await previewPayment(addressId, coupon?.id || 0, coupon?.code || '');

      if (!response || !response.carts || !Array.isArray(response.carts)) {
        throw new Error('Dữ liệu giỏ hàng không hợp lệ');
      }

      setCartData(response);
      console.log('Preview payment response:', response);
    } catch (err: any) {
      const errorMessage = err?.message || 'Không thể tải dữ liệu giỏ hàng';
      if (errorMessage.includes('401 NOT_AUTHORIZED')) {
        isUnauthorized.current = true;
        setError('Phiên đăng nhập hết hạn. Vui lòng đăng nhập lại.');
      } else {
        setError(errorMessage);
      }
      setCartData(null);
      console.error('Error fetching payment preview:', errorMessage);
    } finally {
      setLoading(false);
      requestLock.current = false;
    }
  };

  const fetchCoupons = async (checkPrice: number = 0) => {
    if (hasFetchedCoupons.current && lastTotalPrice.current === checkPrice) {
      return coupons;
    }

    try {
      console.log('Calling getCollectedCoupons API:', { checkPrice });
      const couponResponse = await getCollectedCoupons(checkPrice);
      const fetchedCoupons: Coupon[] = couponResponse?.coupons?.map((c: any) => ({
        id: c.id,
        code: c.code || '',
        type: c.type || 'HATHYO',
        discountType: c.discountType || 'PERCENT',
        discountPercent: c.discountPercent ?? null,
        discountValue: c.discountValue ?? null,
        minimumPriceApply: c.minimumPriceApply ?? 0,
        maxDiscountPrice: c.maxDiscountPrice ?? null,
        title: c.title || 'Không có tiêu đề',
        description: c.description || 'Không có mô tả',
        image: c.image || 'https://via.placeholder.com/50',
        expiredAt: c.expiredAt || new Date().toISOString(),
        applyStatus: c.applyStatus ?? null,
      })) || [];

      setCoupons(fetchedCoupons);
      hasFetchedCoupons.current = true;
      lastTotalPrice.current = checkPrice;
      console.log('getCollectedCoupons response:', couponResponse);
      return fetchedCoupons;
    } catch (err: any) {
      const errorMessage = err?.message || 'Không thể tải danh sách coupon';
      if (errorMessage.includes('401 NOT_AUTHORIZED')) {
        isUnauthorized.current = true;
        setError('Phiên đăng nhập hết hạn. Vui lòng đăng nhập lại.');
      } else {
        setError(errorMessage);
      }
      setCoupons([]);
      console.error('Error fetching coupons:', errorMessage);
      return [];
    }
  };

  const refresh = useCallback(async () => {
    if (addressLoading || addressError || !defaultAddress || isUnauthorized.current || requestLock.current) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const checkPrice = cartData?.totalPrice || 0;
      await fetchCoupons(checkPrice);
      await fetchCartData(defaultAddress.id.toString(), selectedCoupon);
    } catch (err: any) {
      setError(err?.message || 'Không thể làm mới dữ liệu');
    } finally {
      setLoading(false);
    }
  }, [defaultAddress, addressLoading, addressError, cartData?.totalPrice, selectedCoupon]);

  const updatePreviewWithCoupon = useCallback(
    async (addressId: string, coupon: Coupon | null) => {
      if (
        (selectedCoupon?.id === coupon?.id && selectedCoupon?.code === coupon?.code) ||
        isUnauthorized.current ||
        requestLock.current
      ) {
        return;
      }

      setSelectedCoupon(coupon);
      await fetchCartData(addressId, coupon);
    },
    [selectedCoupon]
  );

  const resetUnauthorized = useCallback(() => {
    isUnauthorized.current = false;
    setError(null);
    refresh();
  }, [refresh]);

  useEffect(() => {
    refresh();
  }, [refresh]);

  return {
    cartData,
    coupons,
    selectedCoupon,
    updatePreviewWithCoupon,
    refresh,
    resetUnauthorized,
    loading: loading || addressLoading,
    error,
  };
};

export const usePaymentPreview = () => {
  return useListProductPayment();
};