export interface Order {
    id: number;
    userId: string;
    userNote: string;
    orderCode: string;
    customerName: string;
    customerPhone: string;
    customerAddress: string;
    productsPrice: number;
    discountProductsPrice: number;
    totalProductsPrice: number;
    shippingId: number;
    shippingFee: number;
    totalShippingPrice: number;
    discountShippingPrice: number;
    totalPrice: number;
    coupon: string | null;
    globalCoupon: string | null;
    createdAt: string;
    updatedAt: string;
    status: 'PENDING' | 'ACCEPTED' | 'IN_TRANSIT' | 'DELIVERED' | 'RECEIVED' | 'CANCELLED';
    orderItems: OrderItem[];
    merchant: Merchant;
  }
  
  export interface OrderItem {
    id: number;
    orderId: number;
    productId: number;
    productTitle: string;
    productVariantTitle: string;
    productVariantImage: string;
    skuCode: string;
    productPrice: number;
    discountProductPrice: number;
    totalPrice: number;
    quantity: number;
    createdAt: string;
    returned: boolean;
    rated: boolean;
    chooseReturn: boolean;
  }
  
  export interface Merchant {
    id: number;
    email: string;
    fullName: string;
    userId: string;
    merchantType: string;
    merchantStatus: string;
    merchantCode: string;
    storeName: string;
    logo: string;
    city: string;
    district: string;
    ward: string;
    address: string;
    phoneNo: string;
    identityNumber: string;
    identityImageFront: string;
    identityImageBack: string;
    taxNumber: string;
    businessLicense: string;
    createdAt: string;
    updatedAt: string;
    rating: number;
    numOfTotalProducts: number;
    numOfFollowers: number;
    responseRate: number;
    description: string;
    agreed: boolean;
  }