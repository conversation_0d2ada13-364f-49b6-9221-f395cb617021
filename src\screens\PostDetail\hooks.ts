import { useEffect, useState } from 'react';
import { getPostDetail } from 'api/posts';

// <PERSON><PERSON>nh <PERSON>a interface cho Post
interface Post {
  id: string | number;
  title: string;
  content: string;
  permalink?: string;
  thumbnail?: string;
  author: string;
  createdAt: string;
  views?: number;
  tags?: { id: string | number; name: string }[];
  indexOfContent?: { heading: string; content: string }[];
  relatedPosts?: { id: number; title: string; author: string; thumbnail: string; topic?: { name: string } }[];
}

export const usePostDetail = ({ id }: { id?: string | number }) => {
  const [loading, setLoading] = useState(false);
  const [post, setPost] = useState<Post | null>(null);
  const [error, setError] = useState<string | null>(null);

  const fetchPost = async () => {
    if (!id) {
      setError('Không có ID bài viết');
      setPost(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      console.log('usePostDetail - Fetching post with ID:', id);
      const response = await getPostDetail({ id });

      // Validate and format response data
      if (response && typeof response === 'object') {
        const formattedPost: Post = {
          id: response.id || id,
          title: response.title || 'Tiêu đề không có',
          content: response.content || '',
          permalink: response.permalink || response.thumbnail,
          thumbnail: response.thumbnail,
          author: response.author || 'Tác giả ẩn danh',
          createdAt: response.createdAt || new Date().toISOString(),
          views: typeof response.views === 'number' ? response.views : undefined,
          tags: Array.isArray(response.tags) ? response.tags : [],
          indexOfContent: Array.isArray(response.indexOfContent) ? response.indexOfContent : [],
          relatedPosts: Array.isArray(response.relatedPosts) ? response.relatedPosts : [],
        };
        setPost(formattedPost);
      } else {
        throw new Error('Dữ liệu bài viết không hợp lệ');
      }
    } catch (error: any) {
      console.error('usePostDetail - Error fetching post:', error);
      setError(error?.message || 'Không thể tải bài viết');
      setPost(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPost();
  }, [id]); 

  return {
    loading,
    post,
    error,
  };
};