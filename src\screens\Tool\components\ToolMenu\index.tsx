import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

import MenuButton from 'components/MenuButton';

import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';

const itemWidth =
  (metrics.fullWidth - metrics.spacing4 * 2 - metrics.spacing2 * 3) / 4;
const ToolMenu = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Công cụ Vui<PERSON>hoe</Text>
      <View style={styles.row}>
        <MenuButton
          customStyles={{ width: itemWidth }}
          imgIcon={
            <MaterialIcons
              name="sports-score"
              size={24}
              color={colors['Moss/400']}
            />
          }
          title="Bài tập"
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          iconName="heart-circle-outline"
          title="Hồ sơ SK"
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          imgIcon={
            <MaterialCommunityIcons
              name="run-fast"
              size={24}
              color={colors['Moss/400']}
            />
          }
          title="Dinh dưỡng"
        />
        <MenuButton
          customStyles={{ width: itemWidth }}
          imgIcon={
            <MaterialCommunityIcons
              name="virus-outline"
              size={24}
              color={colors['Moss/400']}
            />
          }
          title="Tinh thần"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingBottom: metrics.spacing2,
  },
  row: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
});

export default ToolMenu;
