import { StyleSheet } from 'react-native';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';
import metrics from 'themes/metrics';

const styles = StyleSheet.create({
  container: {
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'white',
    flexDirection: 'column',
    borderRadius: metrics.radius4,
    borderWidth: 1,
    borderColor: 'rgba(17, 66, 0, 0.1)',
  },
  postImage: {
    width: '100%',
    height: metrics.fullWidth / 2 - metrics.spacing4 * 3,
    borderRadius: metrics.radius4,
  },
  content: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    flex: 1,
    padding: metrics.spacing2,
  },
  title: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors['Gray/900'],
    marginBottom: metrics.spacing1,
    minHeight: 33,
  },
  border: {
    borderRadius: metrics.radius4,
  },
  price: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors['Warning/400'],
    marginRight: metrics.spacing4,
  },
  anchoPrice: {
    fontSize: 12,
    fontWeight: '500',
    color: colors['Grayiron/300'],
    textDecorationLine: 'line-through',
  },
  button: {
    width: '100%',
    backgroundColor: colors['Moss/500'],
  },
  rowCenter: {
    ...commonStyles.rowCenter,
    marginBottom: metrics.spacing1,
  },
  marginBottom: {
    marginBottom: metrics.spacing1,
  },
  badgeContainer: {
    width: 83,
    height: 30,
    position: 'absolute',
    left: -12,
    top: metrics.spacing3,
    paddingTop: 3,
    alignItems: 'center',
  },
  badgeImage: {
    width: 83,
    height: 30,
    resizeMode: 'stretch',
    alignSelf: 'flex-start',
  },
  badgeTitle: {
    color: 'white',
    fontSize: 11,
    fontWeight: '500',
  },
});

export default styles;