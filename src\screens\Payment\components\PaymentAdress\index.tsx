import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text } from '@ui-kitten/components';
import { useNavigation } from '@react-navigation/native';
import { usePaymentAddress } from './hooks';
import metrics from 'themes/metrics';
import colors from 'themes/colors';
import Icon from 'react-native-vector-icons/FontAwesome';

const PaymentAddress = () => {
  const navigation = useNavigation();
  const { addresses, defaultAddress, loading, error } = usePaymentAddress();
  
  const navigateToAddressBook = () => {
    navigation.navigate('AddressBook');
  };

  const getAddressToDisplay = () => {
    if (!addresses || addresses.length === 0) {
      return null;
    }
    
    if (defaultAddress) {
      return defaultAddress;
    }
    
    return addresses[0];
  };

  const displayAddress = getAddressToDisplay();

  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <Text category="s1" style={styles.title}>
          <PERSON><PERSON><PERSON> chỉ nhận hàng
        </Text>
        {displayAddress && (
          <TouchableOpacity onPress={navigateToAddressBook}>
            <Text style={styles.changeButtonText}>Thay đổi</Text>
          </TouchableOpacity>
        )}
      </View>
      
      {loading ? (
        <View style={styles.centeredContent}>
          <Text style={styles.messageText}>Đang tải địa chỉ...</Text>
        </View>
      ) : displayAddress ? (
        <View style={styles.addressContent}>
          <Text style={styles.receiverName}>{displayAddress.receiverName}</Text>
          
          <View style={styles.infoRow}>
            <Icon name="phone" size={14} color="#0B8249" style={styles.icon} />
            <Text style={styles.phoneNumber}>(+84) {displayAddress.receiverPhoneNumber}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Icon name="map-marker" size={14} color="#0B8249" style={styles.icon} />
            <Text style={styles.addressText}>
              {`${displayAddress.customerStreetAddress}, ${displayAddress.customerWard}, ${displayAddress.customerDistrict}, ${displayAddress.customerProvince}`}
            </Text>
          </View>
        </View>
      ) : (
        <View style={styles.centeredContent}>
          <Text style={styles.messageText}>
            {error ? 'Trước tiên, bạn phải thêm địa chỉ thanh toán' : 'Trước tiên, bạn phải thêm địa chỉ thanh toán'}
          </Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={navigateToAddressBook}
          >
            <Text style={styles.addButtonText}>Thêm địa chỉ</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    marginTop: 16,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontWeight: 'bold',
    fontSize: 16,
    color: '#1A1A1A',
  },
  addressContent: {
    // flex: 1,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  icon: {
    marginRight: 8,
  },
  receiverName: {
    fontSize: 15,
    color: '#1A1A1A',
    marginBottom: 8,
  },
  phoneNumber: {
    fontSize: 14,
    color: '#666666',
  },
  addressText: {
    fontSize: 14,
    color: '#1A1A1A',
    lineHeight: 20,
    flexShrink: 1,
  },
  centeredContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  messageText: {
    color: '#666666',
    marginBottom: 12,
    textAlign: 'center',
    fontSize: 14,
  },
  changeButtonText: {
    color: colors['Warning/500'],
    fontWeight: '500',
    fontSize: 14,
  },
  addButton: {
    backgroundColor: colors['Primary/300'],
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  addButtonText: {
    color: 'white',
    fontWeight: '500',
    fontSize: 14,
  },
});

export default PaymentAddress;