import React, { useEffect, useRef } from 'react';
import { View, ImageBackground, StyleSheet, Dimensions, Animated, TouchableOpacity } from 'react-native';
import { Text } from '@ui-kitten/components';
import Icon from 'react-native-vector-icons/FontAwesome6';
import { BlurView } from '@react-native-community/blur';
import { useQuotes } from './hooks';
import commonStyles from 'themes/commonStyles';
import metrics from 'themes/metrics';
import LinearGradient from 'react-native-linear-gradient';
import Clipboard from '@react-native-clipboard/clipboard';
import Toast from 'react-native-toast-message';

const { width: screenWidth } = Dimensions.get('window');
const bannerWidth = screenWidth - metrics.spacing4 * 2;
const bannerHeight = bannerWidth * 0.6;

const Banner = () => {
  const { quotes, loading, refreshQuote } = useQuotes();
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 6,
        tension: 50,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        delay: 200,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleCopy = () => {
    const quoteText = `${quotes?.quote}\n- ${quotes?.author}`;
    Clipboard.setString(quoteText);
    Toast.show({
      type: 'success',
      text1: 'Đã sao chép vào clipboard',
      visibilityTime: 1500,
    });
  };

  const handleRefresh = async () => {
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 0.5,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
    
    await refreshQuote();
  };

  return (
    <Animated.View 
      style={[
        styles.container, 
        { 
          transform: [{ scale: scaleAnim }],
          opacity: fadeAnim,
        }
      ]}
    >
      <ImageBackground
        style={styles.imageContainer}
        imageStyle={styles.imageStyle}
        resizeMode="cover"
        source={require('assets/images/banner.png')}
      >
       
        
        <View style={styles.glassContainer}>
          <LinearGradient
            colors={['#4c5446bf', '#3f413eb5']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.gradientFrame}
          >
            <Animated.View 
              style={[
                styles.contentContainer,
                { transform: [{ translateY: slideAnim }] }
              ]}
            >
              <View style={styles.textContainer}>
                {loading ? (
                  <View style={styles.loadingContainer}>
                    <Animated.View style={styles.loadingDot} />
                    <Text style={styles.loadingText}>Đang tải nguồn cảm hứng...</Text>
                  </View>
                ) : (
                  <>
                    <View style={styles.iconContainer}>
                      <View style={styles.iconGlow} />
                      <Icon 
                        name="quote-left" 
                        size={16} 
                        color="#ffffffff" 
                        style={styles.quoteIcon} 
                      />
                    </View>
                    
                    <Text
                      style={styles.quoteText}
                      numberOfLines={4}
                      ellipsizeMode="tail"
                    >
                      {quotes?.quote || 'Mỗi khoảnh khắc đều là một khởi đầu mới.'}
                    </Text>
                    
                    <View style={styles.authorContainer}>
                      <View style={styles.authorLine} />
                      <Text style={styles.authorText}>
                        {quotes?.author || 'T.S. Eliot'}
                      </Text>
                    </View>
                  </>
                )}
              </View>

              <View style={styles.buttonRow}>
                <TouchableOpacity 
                  style={styles.smallButton}
                  onPress={handleCopy}
                  activeOpacity={0.7}
                >
                  <Icon name="copy" size={12} color="#FFD700" />
                </TouchableOpacity>
                
                <TouchableOpacity 
                  style={styles.smallButton}
                  onPress={handleRefresh}
                  activeOpacity={0.7}
                >
                  <Icon name="arrows-rotate" size={12} color="#FFD700" />
                </TouchableOpacity>
              </View>
            </Animated.View>
          </LinearGradient>
        </View>
        
        <View style={styles.decorativeContainer}>
          <View style={[styles.floatingDot, styles.dot1]} />
          <View style={[styles.floatingDot, styles.dot2]} />
          <View style={[styles.floatingDot, styles.dot3]} />
        </View>
      </ImageBackground>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: bannerWidth,
    height: bannerHeight,
    marginHorizontal: metrics.spacing4,
    borderRadius: 20,
    overflow: 'hidden',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.6,
    shadowRadius: 16,
    elevation: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  imageStyle: {
    borderRadius: 20,
  },
  gradientOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  glassContainer: {
    width: 298,
    height: 166,
    borderRadius: 20,
    overflow: 'hidden',
  },
  gradientFrame: {
    flex: 1,
    padding: 12,
    borderTopLeftRadius: 32,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 32,
    borderBottomLeftRadius: 8,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  iconContainer: {
    position: 'relative',
    alignSelf: 'flex-start',
    marginBottom: 4,
  },
  iconGlow: {
    position: 'absolute',
    top: -4,
    left: -4,
    right: -4,
    bottom: -4,
    backgroundColor: '#FFD700',
    borderRadius: 12,
    opacity: 0.2,
  },
  quoteIcon: {
    textShadowColor: 'rgba(255, 215, 0, 0.5)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 4,
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  loadingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FFD700',
    marginBottom: metrics.spacing1,
  },
  loadingText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    fontStyle: 'italic',
  },
  quoteText: {
    fontSize: 16,
    lineHeight: 22,
    textAlign: 'center',
    fontWeight: '500',
    color: '#FFFFFF',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    letterSpacing: 0.3,
    paddingHorizontal: 8,
  },
  authorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 4,
  },
  authorLine: {
    width: 20,
    height: 1,
    backgroundColor: '#FFD700',
    marginRight: 8,
  },
  authorText: {
    fontSize: 12,
    fontWeight: '500',
    fontStyle: 'italic',
    color: '#FFD700',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  decorativeContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    pointerEvents: 'none',
  },
  floatingDot: {
    position: 'absolute',
    borderRadius: 50,
    backgroundColor: 'rgba(255, 215, 0, 0.3)',
  },
  dot1: {
    width: 6,
    height: 6,
    top: '10%',
    right: '15%',
  },
  dot2: {
    width: 4,
    height: 4,
    top: '80%',
    left: '20%',
  },
  dot3: {
    width: 8,
    height: 8,
    top: '20%',
    left: '12%',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 4,
    gap: 8,
  },
  smallButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255, 215, 0, 0.3)',
  },
});

export default Banner;