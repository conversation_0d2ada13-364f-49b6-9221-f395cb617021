import React from 'react';
import { SafeAreaView, StyleSheet, ScrollView, View } from 'react-native';

import HomeHeader from 'components/Header/HomeHeader';
import ToolMenu from './components/ToolMenu';
import metrics from 'themes/metrics';
import HealthProfile from './components/HealthProfile';
import BMITool from './components/BMITool';
import ActivityVideos from './components/ActivityVideos';

const Tool = () => {
  return (
    <>
      <HomeHeader noStretch />
      <SafeAreaView style={styles.safeAreaView}>
        <ScrollView style={styles.outerWrapper}>
          <ToolMenu />
          <HealthProfile />
          <BMITool />
          <ActivityVideos />
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: { flex: 1 },
  outerWrapper: {
    flex: 1,
    paddingTop: metrics.spacing4,
  },
});

export default Tool;
