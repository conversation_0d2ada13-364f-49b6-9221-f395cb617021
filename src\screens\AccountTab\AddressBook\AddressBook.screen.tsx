import React, { useMemo } from 'react';
import { SafeAreaView, StyleSheet, ScrollView, ActivityIndicator, View, RefreshControl, Text } from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import HomeHeader from 'components/Header/HomeHeader';
import metrics from 'themes/metrics';
import AddressItems from './components/AddressItems';
import commonStyles from 'themes/commonStyles';
import useAddress from './hook';
import { Address } from 'types/address';
import CommonButton from 'components/CommonButton';

const AddressBookScreen = () => {
  const { addresses = [], loading, error, refresh, removeAddress } = useAddress(); // Default to empty array
  const navigation = useNavigation();

  // Fetch addresses every time the screen is focused
  useFocusEffect(
    React.useCallback(() => {
      refresh();
    }, [refresh])
  );

  const renderContent = useMemo(() => {
    if (loading && addresses.length === 0) {
      return (
        <View style={[styles.center, styles.outerWrapper]}>
          <ActivityIndicator size="large" />
        </View>
      );
    }
    if (error && addresses.length === 0) {
      return (
        <View style={[styles.center, styles.outerWrapper]}>
          <Text style={commonStyles.errorText}>{error}</Text>
          <CommonButton onPress={refresh}>Thử lại</CommonButton>
        </View>
      );
    }
    return (
      <ScrollView
        style={styles.outerWrapper}
        refreshControl={
          <RefreshControl
            refreshing={loading && addresses.length > 0}
            onRefresh={refresh}
          />
        }
      >
        <AddressItems
          addresses={addresses}
          loading={loading}
          onItemPress={(address: Address) =>
            navigation.navigate('AddressDetail', { addressId: address.id })
          }
          onAddNewPress={() => navigation.navigate('AddressDetail')}
          onDeletePress={(id: string) => removeAddress(id)}
        />
      </ScrollView>
    );
  }, [loading, error, addresses, refresh, navigation, removeAddress]);

  return (
    <>
      <HomeHeader
        rightComponent={null}
        backButton
        title="Địa chỉ của tôi"
        noStretch
      />
      <SafeAreaView style={styles.safeAreaView}>
        {renderContent}
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
  },
  center: {
    ...commonStyles.rowCenter,
    flex: 1,
  },
  outerWrapper: {
    flex: 1,
    paddingTop: metrics.spacing4,
  },
});

export default AddressBookScreen;