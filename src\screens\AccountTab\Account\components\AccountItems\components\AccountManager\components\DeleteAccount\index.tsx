import React, { useState, useRef } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  View,
  Text,
  TextInput,
  ActivityIndicator,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

import metrics from 'themes/metrics';
import colors from 'themes/colors';
import fonts from 'themes/fonts';
import { useDeleteAccount } from './hooks';
import HomeHeader from 'components/Header/HomeHeader';
import CustomAlert from 'utils/widgets/CustomAlert';

type RootStackParamList = {
  DeleteAccount: undefined;
  Login: undefined;
};

type NavigationProps = NativeStackNavigationProp<RootStackParamList>;

interface AlertState {
  visible: boolean;
  type: 'success' | 'error' | 'confirm';
  title: string;
  message?: string;
  buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[];
}

const DeleteAccount: React.FC = () => {
  const { loading, error, deleteAccount, countdown, canResend, resendOtp, userEmail } = useDeleteAccount();
  const navigation = useNavigation<NavigationProps>();
  const [verificationCode, setVerificationCode] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [alert, setAlert] = useState<AlertState | null>(null);
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const deleteButtonAnim = useRef(new Animated.Value(1)).current;
  const resendButtonAnim = useRef(new Animated.Value(1)).current;

  React.useEffect(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 6,
      tension: 60,
      useNativeDriver: true,
    }).start();
  }, [scaleAnim]);

  const handleButtonPressIn = (anim: Animated.Value) => {
    Animated.spring(anim, {
      toValue: 0.92,
      friction: 6,
      tension: 60,
      useNativeDriver: true,
    }).start();
  };

  const handleButtonPressOut = (anim: Animated.Value) => {
    Animated.spring(anim, {
      toValue: 1,
      friction: 6,
      tension: 60,
      useNativeDriver: true,
    }).start();
  };

  const showAlert = (alertState: AlertState) => {
    setAlert(alertState);
  };

  const hideAlert = () => {
    setAlert(null);
  };

  const handleDelete = () => {
    console.log('handleDelete - verificationCode:', verificationCode);

    if (!verificationCode || verificationCode.trim().length === 0) {
      showAlert({
        visible: true,
        type: 'error',
        title: 'Lỗi',
        message: 'Vui lòng nhập mã xác nhận',
        buttons: [{ text: 'Đóng', onPress: hideAlert }],
      });
      return;
    }

    showAlert({
      visible: true,
      type: 'confirm',
      title: 'Xác nhận xóa tài khoản',
      message: 'Hành động này không thể hoàn tác. Bạn có chắc chắn muốn xóa tài khoản?',
      buttons: [
        { text: 'Hủy', style: 'cancel', onPress: hideAlert },
        { text: 'Xóa', style: 'destructive', onPress: handleConfirmDelete },
      ],
    });
  };

  const handleConfirmDelete = async () => {
    hideAlert();
    setIsDeleting(true);
    try {
      const trimmedVerificationCode = verificationCode.trim();
      console.log('handleConfirmDelete - Using verification code as deletedKey:', trimmedVerificationCode);

      await deleteAccount({ body: { deletedKey: trimmedVerificationCode } });
      showAlert({
        visible: true,
        type: 'success',
        title: 'Thành công',
        message: 'Tài khoản đã được xóa thành công!',
        buttons: [{ text: 'Đăng nhập', onPress: () =>  navigation.navigate('Login') }],
      });
    } catch (err) {
      console.error('Error deleting account:', err);
      showAlert({
        visible: true,
        type: 'error',
        title: 'Lỗi',
        message: 'Mã xác nhận không đúng hoặc không thể xóa tài khoản. Vui lòng thử lại.',
        buttons: [{ text: 'Đóng', onPress: hideAlert }],
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleResendCode = () => {
    if (canResend) {
      resendOtp();
      setVerificationCode('');
    } else {
      showAlert({
        visible: true,
        type: 'confirm',
        title: 'Thông báo',
        message: `Vui lòng chờ ${countdown} giây để gửi lại mã xác nhận.`,
        buttons: [{ text: 'Đóng', onPress: hideAlert }],
      });
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors['Primary/500']} />
        <Text style={styles.loadingText}>Đang tải...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons
          name="alert-circle-outline"
          size={40}
          color={colors['Danger/500']}
        />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <Text style={styles.retryButtonText}>Quay lại</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.safeAreaView}>
      <HomeHeader backButton 
      title='Xóa tài khoản' noStretch />
      <View style={styles.contentContainer}>
        <Animated.View style={[styles.card, { transform: [{ scale: scaleAnim }] }]}>
          {/* Header Section */}
          <View style={styles.headerSection}>
            <View style={styles.iconContainer}>
              <MaterialCommunityIcons
                name="shield-lock"
                size={32}
                color={colors['Danger/500']}
              />
            </View>
            <Text style={styles.title}>Xác nhận xóa tài khoản</Text>
            <Text style={styles.subtitle}>Bước cuối cùng</Text>
          </View>

          {/* Email Info Section */}
          <View style={styles.emailSection}>
            <MaterialCommunityIcons
              name="email-outline"
              size={20}
              color={colors['Primary/600']}
              style={styles.emailIcon}
            />
            <View style={styles.emailInfo}>
              <Text style={styles.emailLabel}>Mã xác nhận đã được gửi đến:</Text>
              {userEmail && (
                <Text style={styles.emailText}>{userEmail}</Text>
              )}
            </View>
          </View>

          {/* Code Input Section */}
          <View style={styles.codeSection}>
            <Text style={styles.codeLabel}>Nhập mã xác nhận từ email</Text>
            <View style={styles.inputContainer}>
              <TextInput
                style={styles.codeInput}
                value={verificationCode}
                onChangeText={setVerificationCode}
                placeholder="Nhập mã xác nhận từ email"
                placeholderTextColor={colors['Gray/400']}
                autoFocus
                autoCapitalize="none"
                autoCorrect={false}
                accessibilityLabel="Verification code input"
              />
              <MaterialCommunityIcons
                name="key-variant"
                size={20}
                color={colors['Gray/400']}
                style={styles.inputIcon}
              />
            </View>
          </View>

          {/* Resend Section */}
          <Animated.View style={[styles.resendSection, { transform: [{ scale: resendButtonAnim }] }]}>
            <TouchableOpacity
              style={[styles.resendButton, !canResend && styles.disabledButton]}
              onPress={handleResendCode}
              disabled={!canResend}
              onPressIn={() => handleButtonPressIn(resendButtonAnim)}
              onPressOut={() => handleButtonPressOut(resendButtonAnim)}
              activeOpacity={0.7}
            >
              <MaterialCommunityIcons
                name="refresh"
                size={16}
                color={canResend ? colors['Primary/600'] : colors['Gray/400']}
                style={styles.resendIcon}
              />
              <Text style={[styles.resendButtonText, !canResend && styles.disabledText]}>
                {canResend ? 'Gửi lại mã' : `Gửi lại sau ${countdown}s`}
              </Text>
            </TouchableOpacity>
          </Animated.View>

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => navigation.goBack()}
              activeOpacity={0.8}
            >
              <Text style={styles.cancelButtonText}>Hủy bỏ</Text>
            </TouchableOpacity>

            <Animated.View style={[styles.deleteButtonContainer, { transform: [{ scale: deleteButtonAnim }] }]}>
              <TouchableOpacity
                style={[styles.deleteButton, (isDeleting || !verificationCode.trim()) && styles.disabledButton]}
                onPress={handleDelete}
                disabled={isDeleting || !verificationCode.trim()}
                onPressIn={() => handleButtonPressIn(deleteButtonAnim)}
                onPressOut={() => handleButtonPressOut(deleteButtonAnim)}
                activeOpacity={0.8}
              >
                {isDeleting ? (
                  <ActivityIndicator size="small" color={colors.white} />
                ) : (
                  <MaterialCommunityIcons
                    name="delete-forever"
                    size={18}
                    color={colors.white}
                    style={styles.deleteIcon}
                  />
                )}
                <Text style={styles.deleteButtonText}>
                  {isDeleting ? 'Đang xóa...' : 'Xóa tài khoản'}
                </Text>
              </TouchableOpacity>
            </Animated.View>
          </View>
        </Animated.View>
      </View>
      {alert && (
        <CustomAlert
          visible={alert.visible}
          type={alert.type}
          title={alert.title}
          message={alert.message}
          buttons={alert.buttons}
          onDismiss={hideAlert}
          dismissable
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
    backgroundColor: colors['Gray/50'],
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  card: {
    backgroundColor: colors.white,
    borderRadius: metrics.radius4,
    padding: metrics.spacing6,
    width: '100%',
    maxWidth: 400,
    shadowColor: colors['Gray/900'],
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 24,
    elevation: 8,
  },
  headerSection: {
    alignItems: 'center',
    marginBottom: metrics.spacing5,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors['Danger/50'],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: metrics.spacing3,
  },
  title: {
    ...fonts.style.h2,
    fontWeight: '700',
    color: colors['Gray/900'],
    textAlign: 'center',
    marginBottom: metrics.spacing1,
    fontSize: 22,
  },
  subtitle: {
    ...fonts.style.normal,
    color: colors['Danger/600'],
    textAlign: 'center',
    fontWeight: '500',
    fontSize: 14,
  },
  emailSection: {
    flexDirection: 'row',
    backgroundColor: colors['Primary/50'],
    borderRadius: metrics.radius3,
    padding: metrics.spacing3,
    marginBottom: metrics.spacing4,
    borderWidth: 1,
    borderColor: colors['Primary/100'],
  },
  emailIcon: {
    marginRight: metrics.spacing2,
    marginTop: 2,
  },
  emailInfo: {
    flex: 1,
  },
  emailLabel: {
    ...fonts.style.description,
    color: colors['Gray/600'],
    marginBottom: metrics.spacing1,
    fontSize: 13,
  },
  emailText: {
    ...fonts.style.normal,
    color: colors['Primary/700'],
    fontWeight: '600',
    fontSize: 15,
  },
  codeSection: {
    width: '100%',
    marginBottom: metrics.spacing4,
  },
  codeLabel: {
    ...fonts.style.normal,
    color: colors['Gray/700'],
    fontWeight: '600',
    marginBottom: metrics.spacing2,
    textAlign: 'center',
    fontSize: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors['Gray/50'],
    borderRadius: metrics.radius3,
    borderWidth: 2,
    borderColor: colors['Gray/200'],
    paddingHorizontal: metrics.spacing3,
  },
  codeInput: {
    ...fonts.style.normal,
    flex: 1,
    color: colors['Gray/800'],
    paddingVertical: metrics.spacing3,
    textAlign: 'center',
    fontSize: 18,
    fontWeight: '600',
    letterSpacing: 3,
  },
  inputIcon: {
    marginLeft: metrics.spacing2,
  },
  resendSection: {
    alignItems: 'center',
    marginBottom: metrics.spacing5,
  },
  resendButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: metrics.spacing2,
    paddingHorizontal: metrics.spacing3,
  },
  resendIcon: {
    marginRight: metrics.spacing1,
  },
  resendButtonText: {
    ...fonts.style.normal,
    color: colors['Primary/600'],
    fontWeight: '500',
    fontSize: 14,
  },
  disabledText: {
    color: colors['Gray/400'],
  },
  buttonContainer: {
    flexDirection: 'row',
    width: '100%',
    gap: metrics.spacing3,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: colors['Gray/100'],
    paddingVertical: metrics.spacing3,
    borderRadius: metrics.radius3,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: colors['Gray/200'],
  },
  cancelButtonText: {
    ...fonts.style.normal,
    color: colors['Gray/700'],
    fontWeight: '600',
    fontSize: 16,
  },
  deleteButtonContainer: {
    flex: 1,
  },
  deleteButton: {
    backgroundColor: colors['Danger/500'],
    paddingVertical: metrics.spacing3,
    borderRadius: metrics.radius3,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors['Danger/500'],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  deleteIcon: {
    marginRight: metrics.spacing1,
  },
  deleteButtonText: {
    ...fonts.style.normal,
    color: colors.white,
    fontWeight: '600',
    fontSize: 16,
  },
  disabledButton: {
    opacity: 0.6,
    shadowOpacity: 0,
    elevation: 0,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors['Gray/50'],
  },
  loadingText: {
    ...fonts.style.normal,
    color: colors['Gray/600'],
    marginTop: metrics.spacing2,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: metrics.spacing3,
    backgroundColor: colors['Gray/50'],
  },
  errorText: {
    ...fonts.style.normal,
    color: colors['Danger/500'],
    textAlign: 'center',
    marginVertical: metrics.spacing2,
  },
  retryButton: {
    backgroundColor: colors['Primary/50'],
    paddingVertical: metrics.spacing2,
    paddingHorizontal: metrics.spacing3,
    borderRadius: metrics.radius2,
    alignItems: 'center',
  },
  retryButtonText: {
    ...fonts.style.normal,
    color: colors['Primary/600'],
    fontWeight: '600',
  },
  debugText: {
    ...fonts.style.description,
    color: colors['Primary/600'],
    textAlign: 'center',
    marginBottom: metrics.spacing2,
    fontSize: 12,
    fontWeight: '500',
    backgroundColor: colors['Primary/50'],
    padding: metrics.spacing2,
    borderRadius: metrics.radius2,
  },

});

export default DeleteAccount;