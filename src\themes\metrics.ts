import { Dimensions } from 'react-native';

const metrics = {
  spacing0_5: 1,
  spacing1: 4,
  spacing1_5: 6,
  spacing2: 8,
  spacing3: 12,
  spacing4: 16,
  spacing5: 20,
  spacing6: 24,
  spacing7: 28,
  spacing8: 32,
  radius1_5: 6,
  radius1: 4,
  radius2: 8,
  radius3: 12,
  radius4: 16,
  radius5: 20,
  radius6: 24,
  radius7: 28,
  radius8: 32,
  icons: {
    tiny: 15,
    small: 20,
    medium: 30,
    large: 45,
    xl: 50,
  },
  images: {
    small: 20,
    medium: 40,
    large: 60,
    logo: 200,
  },
  fullWidth: Dimensions.get('screen').width,
  fullHeight: Dimensions.get('screen').height,
};

export default metrics;
