import React from 'react';
import { SafeAreaView, StyleSheet, ScrollView } from 'react-native';

import HomeHeader from 'components/Header/HomeHeader';
import PostMenu from './components/PostMenu';
import HorizontalPost from './components/HorizontalPost';
import HotPost from './components/HotPosts';
import PostByTopic from './components/PostByTopic';
import metrics from 'themes/metrics';

const Post = () => {
  return (
    <>
      <HomeHeader noStretch />
      <SafeAreaView style={styles.safeAreaView}>
        <ScrollView style={styles.outerWrapper}>
          <HorizontalPost />
          <PostMenu id={0} />
          <HotPost />
          <PostByTopic title="Dinh dưỡng" id={2} />
          <PostByTopic title="Tinh thần" id={3} />
          <PostByTopic title="Vận động" id={4} />
          <PostByTopic title="Bệnh lí" id={1} />
        </ScrollView>
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  safeAreaView: { flex: 1 },
  outerWrapper: {
    flex: 1,
    paddingTop: metrics.spacing4,
  },
});

export default Post;
