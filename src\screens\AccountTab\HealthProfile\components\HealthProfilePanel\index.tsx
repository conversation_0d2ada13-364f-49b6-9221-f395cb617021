import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import metrics from 'themes/metrics';
import commonStyles from 'themes/commonStyles';
import CommonButton from 'components/CommonButton';
import FormInput from 'components/FormInput';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useForm } from 'react-hook-form';
import { useUserStore } from 'stores/user';
import { pick } from 'lodash';
import { Text } from 'react-native';
import fonts from 'themes/fonts';

const schema = yup.object({
  height: yup.string().required('Nhập chiều cao'),
  weight: yup.string().required('Nhập cân nặng'),
  activityLevel: yup.string().required('Chọn mức độ vận động'),
  met: yup.string().required('Chọn loại hình vận động'),
  duration: yup.string().required('Chọn thời gian vận động'),
});

const HealthProfilePanel = () => {
  const user = useUserStore((state: any) => state?.user);
  const userFitness = useUserStore((state: any) => state?.userFitness);
  const loading = useUserStore((state: any) => state?.loading);

  const updateUserFitness = useUserStore(
    (state: any) => state?.updateUserFitness,
  );
  const {
    control,
    handleSubmit,
    formState: { errors },
    // setError,
  } = useForm({
    defaultValues: {
      ...pick(userFitness, [
        'height',
        'weight',
        'age',
        'activityLevel',
        'met',
        'duration',
      ]),
      gender: user?.gender,
    },
    resolver: yupResolver(schema),
  });

  const onSubmit = (values: any) => {
    updateUserFitness({ ...values });
  };

  console.log(userFitness);
  

  return (
    <>
      <ScrollView style={styles.outerWrapper}>
        <View style={styles.introContainer}>
          <Text style={styles.label}>
            Chúng tôi mang đến cho bạn một bộ sưu tập các công cụ tính toán khoa
            học, tiện lợi và dễ sử dụng, được thiết kế để giúp bạn:
          </Text>
          <Text style={styles.text}>
            . Theo dõi và cải thiện sức khỏe: Từ cân nặng lý tưởng, lượng calo
            tiêu thụ, đến lượng protein và nước uống hàng ngày.
          </Text>
          <Text style={styles.text}>
            . Tối ưu hóa việc tập luyện: Đo lường hiệu quả luyện tập với các
            công cụ như tính lượng calo đốt cháy và tốc độ chạy bộ.
          </Text>
          <Text style={styles.text}>
            . Kiểm soát chế độ dinh dưỡng: Xác định nhu cầu dinh dưỡng cá nhân
            để đạt mục tiêu tăng cơ, giảm cân, hoặc duy trì sức khỏe.
          </Text>
        </View>
        <View style={styles.container}>
          <View style={[commonStyles.rowSpaceBetween, commonStyles.gap4]}>
            <View style={styles.rowForm}>
              <FormInput
                label="Chiều cao (cm)"
                control={control}
                required
                placeholder="165"
                name="height"
                errors={errors?.height as any}
                disabled={loading}
              />
            </View>
            <View style={styles.rowForm}>
              <FormInput
                label="Cân nặng (kg)"
                control={control}
                required
                placeholder="65"
                name="weight"
                errors={errors?.weight as any}
                disabled={loading}
              />
            </View>
          </View>

          <View style={styles.rowForm}>
            <FormInput
              label="Độ tuổi"
              control={control}
              required
              placeholder="Nhập độ tuổi"
              name="age"
              errors={errors?.age as any}
              disabled={loading}
            />
          </View>

          <View style={styles.rowForm}>
            <FormInput
              label="Giới tính"
              control={control}
              required
              placeholder="Giới tính"
              name="gender"
              errors={errors?.gender as any}
              inputType="select"
              options={[
                { label: 'Nam', value: 'MALE' },
                { label: 'Nữ', value: 'FEMALE' },
              ]}
              disabled
            />
          </View>
          <View style={styles.rowForm}>
            <FormInput
              label="Mức độ vận động"
              control={control}
              required
              placeholder="Chọn mức độ vận động"
              name="activityLevel"
              errors={errors?.activityLevel as any}
              inputType="select"
              options={[
                {
                  label: 'Ít vận động (Hầu như không tập thể dục)-St Jeor',
                  value: 1.2,
                },
                {
                  label: 'Hoạt động nhẹ (Tập thể dục nhẹ 1–3 ngày/tuần)',
                  value: 1.375,
                },
                {
                  label:
                    'Hoạt động vừa phải (Tập thể dục vừa phải 3–5 ngày/tuần)',
                  value: 1.55,
                },
                {
                  label:
                    'Hoạt động cao (Tập thể dục cường độ cao 6–7 ngày/tuần)',
                  value: 1.725,
                },
                {
                  label:
                    'Hoạt động rất cao (Tập thể dục rất nặng hoặc làm công việc lao động)',
                  value: 1.9,
                },
              ]}
              disabled={loading}
            />
          </View>
          <View style={styles.rowForm}>
            <FormInput
              label="Loại hình vận động"
              control={control}
              required
              placeholder="Chọn loại hình vận động"
              name="met"
              errors={errors?.met as any}
              inputType="select"
              options={[
                {
                  label: 'Nghỉ ngơi (Resting - Không hoạt động)',
                  value: 1,
                },
                {
                  label: 'Đi bộ nhẹ nhàng (Light walking)',
                  value: 3.5,
                },
                {
                  label: 'Chạy cường độ cao (High-intensity running)',
                  value: 9.8,
                },
                {
                  label: 'Làm việc nhà nhẹ (Light household activities)',
                  value: 4.0,
                },
                {
                  label: 'Đạp xe nhanh (Fast cycling)',
                  value: 8.0,
                },
                {
                  label: 'Tập yoga hoặc vận động nhẹ (Yoga or light exercise)',
                  value: 3.0,
                },
                {
                  label: 'Bơi lội vừa phải (Moderate swimming)',
                  value: 6.0,
                },
              ]}
              disabled={loading}
            />
          </View>
          <View style={styles.rowForm}>
            <FormInput
              label="Thời gian tập luyện (phút)"
              control={control}
              required
              placeholder="Chọn thời gian tập luyện"
              name="duration"
              errors={errors?.duration as any}
              inputType="input"
              disabled={loading}
            />
          </View>
        </View>
      </ScrollView>
      <CommonButton loading={loading} onPress={handleSubmit(onSubmit)}>
        Lưu
      </CommonButton>
    </>
  );
};

const styles = StyleSheet.create({
  outerWrapper: {
    flex: 1,
    paddingTop: metrics.spacing4,
    backgroundColor: 'white',
  },
  container: {
    marginHorizontal: metrics.spacing2,
    paddingHorizontal: metrics.spacing2,
    paddingBottom: metrics.spacing2,
  },
  introContainer: {
    paddingHorizontal: metrics.spacing4,
    paddingBottom: metrics.spacing2,
  },
  label: {
    ...commonStyles.label,
    fontSize: fonts.size.medium,
  },
  text: {
    ...commonStyles.mainText,
    fontSize: fonts.size.small,
    paddingLeft: metrics.spacing3,
  },
  formItem: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: metrics.spacing4,
    gap: metrics.spacing4,
  },
  input: {
    flex: 1,
    backgroundColor: 'white',
    marginVertical: metrics.spacing2,
  },
  rowForm: {
    marginVertical: metrics.spacing2,
    flex: 1,
  },
  datePicker: {
    ...commonStyles.datePicker,
    marginVertical: metrics.spacing2,
  },
  avatarContainer: {
    ...commonStyles.rowCenter,
    marginVertical: metrics.spacing4,
  },
  avatar: {
    width: 80,
    height: 80,
  },
});

export default HealthProfilePanel;
