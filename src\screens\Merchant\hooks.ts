// screens/Merchant/hooks/useMerchant.ts
import { useState, useCallback } from 'react';
import { getMerchantById } from 'api/merchant';
import { getFilterProducts } from 'api/products';
import { Merchant } from 'types/order';
import { Product } from 'types/product';
import { PRODUCT_SORT } from 'themes/constant';
import debounce from 'lodash/debounce';

export const useMerchant = (merchantId: string) => {
  const [merchant, setMerchant] = useState<Merchant | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [page, setPage] = useState<number>(1);
  const [sort, setSort] = useState(PRODUCT_SORT.createdDesc.key); 
  const [keyword, setKeyword] = useState<string>('');

  const fetchMerchant = async () => {
    try {
      const response = await getMerchantById(merchantId);
      setMerchant(response);
    } catch (err) {
      setError('Không thể tải thông tin cửa hàng');
    }
  };

  const fetchProducts = useCallback(
    async (pageNum: number, sortValue?: string, searchKeyword?: string) => {
      try {
        setLoading(true);
        const params: any = {
          query: searchKeyword || null,
          merchantId, 
          page: pageNum,
          limit: 10,
        };

        if (!searchKeyword) {
          params.sort = sortValue || sort;
        }

        const response = await getFilterProducts(params);
        const productList = response?.products || [];
        if (Array.isArray(productList)) {
          setProducts((prev) => (pageNum === 1 ? productList : [...prev, ...productList]));
          setHasMore(response.currentPage < response.totalPages);
        } else {
          console.warn('Products is not an array:', productList);
          setProducts([]);
          setHasMore(false);
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Không thể tải sản phẩm');
      } finally {
        setLoading(false);
      }
    },
    [merchantId, sort]
  );

  const refresh = useCallback(async () => {
    setLoading(true);
    setPage(1);
    setProducts([]);
    setHasMore(true);
    setKeyword('');
    try {
      await Promise.all([fetchMerchant(), fetchProducts(1, sort)]);
      setError(null);
    } finally {
      setLoading(false);
    }
  }, [fetchProducts, sort]);

  const loadMore = useCallback(async () => {
    if (!hasMore || loading) return;
    setLoading(true);
    try {
      await fetchProducts(page + 1, sort, keyword);
      setPage((prev) => prev + 1);
    } finally {
      setLoading(false);
    }
  }, [hasMore, loading, page, sort, keyword, fetchProducts]);

  const sortProducts = useCallback(
    async (sortKey: string) => {
      setSort(sortKey);
      setLoading(true);
      setPage(1);
      setProducts([]);
      setHasMore(true);
      try {
        await fetchProducts(1, sortKey, keyword);
        setError(null);
      } finally {
        setLoading(false);
      }
    },
    [fetchProducts, keyword]
  );

  const searchProducts = useCallback(
    debounce((searchKeyword: string) => {
      setKeyword(searchKeyword);
      setPage(1);
      setProducts([]);
      setHasMore(true);
      fetchProducts(1, sort, searchKeyword);
    }, 300),
    [fetchProducts, sort]
  );

  return {
    merchant,
    products,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    sort,
    keyword,
    sortProducts,
    searchProducts,
  };
};