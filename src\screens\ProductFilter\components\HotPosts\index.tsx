import React from 'react';
import { View, StyleSheet } from 'react-native';

import metrics from 'themes/metrics';
import PostCard from 'components/PostCard';
import { Text } from '@ui-kitten/components';
import fonts from 'themes/fonts';
import colors from 'themes/colors';

import { useNavigation } from '@react-navigation/native';
import { usePostsByTopicId } from '../ProductByTopic/hooks';
import { map } from 'lodash';

const HotPost = () => {
  const navigation = useNavigation();
  const { posts } = usePostsByTopicId({ id: 4, page: 1, size: 3 });
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Gợi ý cho bạn</Text>
        {map(posts, (post: any) => (
          <PostCard
            key={post.id}
            title={post.title}
            author={post.author}
            uri={post.thumbnail}
            avatarUri={post.thumbnail}
            topicName={post?.topic?.name}
            createdAt={post?.createdAt}
            onPress={() => navigation.navigate('PostDetail', { id: post.id })}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: metrics.spacing2,
  },
  content: {
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing4,
    backgroundColor: 'white',
  },
  row: {
    justifyContent: 'space-around',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexDirection: 'row',
    gap: metrics.spacing2,
    marginBottom: metrics.spacing3,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  tag: {
    marginRight: metrics.spacing2,
  },
  tags: {
    marginBottom: metrics.spacing4,
  },
});

export default HotPost;
