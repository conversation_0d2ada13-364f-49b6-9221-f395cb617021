import { filter } from 'lodash';
import { apis } from 'utils/axios';
import cities from 'assets/city.json';
import districts from 'assets/district.json';
import wards from 'assets/ward.json';
import { Address } from 'types/address';

export const getCities = async () => {
  try {
    return cities;
  } catch (e) {
    throw new Error(`Lỗi khi lấy danh sách tỉnh/thành phố: ${e}`);
  }
};

export const getDistricts = async (cityId: string | number) => {
  try {
    return filter(districts, { city_id: Number(cityId) });
  } catch (e) {
    throw new Error(`Lỗi khi lấy danh sách quận/huyện: ${e}`);
  }
};

export const getWards = async (districtId: string | number) => {
  try {
    return filter(wards, { district_id: Number(districtId) });
  } catch (e) {
    throw new Error(`Lỗi khi lấy danh sách phường/xã: ${e}`);
  }
};

export const listAddress = async (p0: { signal: AbortSignal | undefined; }) => {
  try {
    const response = await apis().get('/addresses', {
      params: { size: 10, page: 0 },
    });
    console.log("Danh sách địa chỉ: ", response.data);
    return response?.data;
  } catch (e) {
    throw new Error(`Lỗi khi lấy danh sách địa chỉ: ${e}`);
  }
};

export const getAddressDetail = async (id: number) => {
  try {
    const response = await apis().get(`/addresses/${id}`);
    console.log("Địa chỉ chi tiết: ", response.data);
    const data = response?.data;
    // Validate response data
    if (!data || !data.id) {
      throw new Error('Dữ liệu địa chỉ không hợp lệ');
    }
    return data as Address;
  } catch (e) {
    console.error("Lỗi khi lấy địa chỉ chi tiết: ", e);
    throw new Error(`Lỗi khi lấy địa chỉ chi tiết: ${e}`);
  }
};

export const createAddress = async (body: Omit<Address, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) => {
  try {
    console.log("Payload tạo địa chỉ: ", body);
    const response = await apis().post('/addresses', body);
    console.log("Kết quả tạo địa chỉ mới: ", response.data);
    return response.data;
  } catch (e) {
    console.error("Lỗi khi tạo địa chỉ: ", e);
    throw new Error(`Lỗi khi tạo địa chỉ: ${e}`);
  }
};

export const patchAddress = async (body: Address & { id: number }) => {
  try {
    if (!body.id) {
      throw new Error('ID là bắt buộc để cập nhật địa chỉ');
    }
    const { id, userId, createdAt, updatedAt, ...updateData } = body;
    console.log("Payload cập nhật địa chỉ: ", updateData);
    const response = await apis().patch(`/addresses/${id}`, updateData);
    console.log("Kết quả cập nhật địa chỉ: ", response.data);
    return response.data;
  } catch (e) {
    console.error("Lỗi khi cập nhật địa chỉ: ", e);
    throw new Error(`Lỗi khi cập nhật địa chỉ: ${e}`);
  }
};

export const deleteAddress = async (id: number) => {
  try {
    console.log("Xóa địa chỉ với ID: ", id);
    const response = await apis().delete(`/addresses/${id}`);
    console.log("Kết quả xóa địa chỉ: ", response.data);
    return response?.data;
  } catch (e) {
    console.error("Lỗi khi xóa địa chỉ: ", e);
    throw new Error(`Lỗi khi xóa địa chỉ: ${e}`);
  }
};


