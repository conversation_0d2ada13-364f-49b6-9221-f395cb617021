import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Image,
} from 'react-native';
import { Text, Avatar } from '@ui-kitten/components';
import { map } from 'lodash';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import Rating from 'components/Rating';
import { useProductRating } from './hook';
import LinearGradient from 'react-native-linear-gradient';

// Type definitions
interface RatingItem {
  id: string;
  userAvatar?: string;
  userName?: string;
  rate: number;
  comment: string;
  createdAt: string;
  imageUrls?: string;
}

interface CommentProps {
  id: string;
}

const Comment: React.FC<CommentProps> = ({ id }) => {
  const {
    ratings,
    loading,
    loadMoreRatings,
    isLoadingMore,
    hasMore,
  } = useProductRating({ id });

  return (
    <View style={styles.container}>
      {/* Comment List */}
      <Text style={styles.sectionTitle}>Đ<PERSON>h gi<PERSON> sản phẩm ({ratings.length})</Text>
      {loading && !ratings.length ? (
        <Text style={styles.loadingText}>Đang tải...</Text>
      ) : (
        <ScrollView
          style={styles.ratings}
          vertical
          bounces
          showsVerticalScrollIndicator={false}
          onMomentumScrollEnd={() => {
            if (hasMore && !isLoadingMore) loadMoreRatings();
          }}
        >
          {map(ratings, (ratingItem: RatingItem) => (
            <LinearGradient
              key={ratingItem.id}
              colors={[colors.white, colors['Gray/50']]}
              style={styles.ratingItem}
            >
              <View style={styles.ratingHeader}>
                <View style={styles.userInfo}>
                  <Avatar
                    size="small"
                    source={
                      ratingItem.userAvatar
                        ? { uri: ratingItem.userAvatar }
                        : require('assets/images/logo.png')
                    }
                    style={styles.avatar}
                  />
                  <View style={styles.userText}>
                    <Text style={styles.username}>
                      {ratingItem.userName || 'Người dùng ẩn danh'}
                    </Text>
                    <Rating value={ratingItem.rate} size="small" readonly />
                  </View>
                </View>
                <Text style={styles.createdAt}>
                  {new Date(ratingItem.createdAt).toLocaleDateString('vi-VN')}
                </Text>
              </View>
              <Text style={styles.commentText}>{ratingItem.comment}</Text>
              {ratingItem.imageUrls && (
                <Image
                  source={{ uri: ratingItem.imageUrls }}
                  style={styles.commentImage}
                  resizeMode="cover"
                />
              )}
            </LinearGradient>
          ))}
          {isLoadingMore && (
            <Text style={styles.loadingMoreText}>Đang tải thêm...</Text>
          )}
          {!ratings.length && (
            <Text style={styles.noRatings}>Chưa có đánh giá nào</Text>
          )}
          {ratings.length > 0 && !hasMore && (
            <Text style={styles.endText}>Đã hiển thị tất cả đánh giá</Text>
          )}
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: metrics.spacing5,
    paddingTop: metrics.spacing4,
    backgroundColor: colors['Gray/100'],
    borderRadius: 16,
  },
  sectionTitle: {
    fontSize: fonts.size.h3 + 2,
    fontWeight: '700',
    color: colors['Gray/900'],
    marginBottom: metrics.spacing3,
    paddingHorizontal: metrics.spacing4,
  },
  ratings: {
    paddingHorizontal: metrics.spacing4,
    paddingBottom: metrics.spacing4,
  },
  ratingItem: {
    padding: metrics.spacing3,
    borderRadius: 16,
    marginBottom: metrics.spacing3,
    elevation: 3,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
  ratingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: metrics.spacing2,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    borderWidth: 1,
    borderColor: colors['Grayiron/200'],
  },
  userText: {
    marginLeft: metrics.spacing2,
  },
  username: {
    fontSize: fonts.size.regular + 1,
    fontWeight: '700',
    color: colors['Gray/900'],
    marginBottom: metrics.spacing1,
  },
  createdAt: {
    fontSize: fonts.size.small,
    color: colors['Grayiron/600'],
    fontWeight: '500',
  },
  commentText: {
    color: colors['Gray/800'],
    fontSize: fonts.size.regular,
    lineHeight: 24,
  },
  commentImage: {
    width: '100%',
    height: 200,
    marginTop: metrics.spacing2,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors['Gray/200'],
  },
  loadingText: {
    color: colors['Grayiron/500'],
    fontSize: fonts.size.regular,
    textAlign: 'center',
    paddingVertical: metrics.spacing4,
  },
  loadingMoreText: {
    color: colors['Grayiron/500'],
    fontSize: fonts.size.regular,
    textAlign: 'center',
    paddingVertical: metrics.spacing2,
  },
  noRatings: {
    color: colors['Grayiron/500'],
    fontSize: fonts.size.regular + 1,
    textAlign: 'center',
    paddingVertical: metrics.spacing4,
    fontWeight: '500',
  },
  endText: {
    color: colors['Grayiron/600'],
    fontSize: fonts.size.small + 1,
    textAlign: 'center',
    paddingVertical: metrics.spacing2,
    fontWeight: '500',
  },
});

export default Comment;