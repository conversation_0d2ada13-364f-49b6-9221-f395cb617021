import { useEffect, useState } from 'react';
import { listAddress } from 'api/addressBooks';

export const usePaymentAddress = () => {
  const [addresses, setAddresses] = useState([]);
  const [defaultAddress, setDefaultAddress] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchAddresses = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await listAddress();
      
      if (!response || !response.addresses) {
        throw new Error('Định dạng phản hồi không hợp lệ');
      }
      
      const addressList = response.addresses;
      setAddresses(addressList);
      
      if (addressList.length > 0) {
        // Tìm địa chỉ mặc định
        const defaultAddr = addressList.find((addr) => addr.isDefault);
        // Nếu có địa chỉ mặc định thì dùng, nếu không thì dùng địa chỉ đầu tiên
        setDefaultAddress(defaultAddr || addressList[0]);
      } else {
        setDefaultAddress(null);
      }
    } catch (err) {
      // Xử lý lỗi một cách nhẹ nhàng và đưa ra thông báo phù hợp
      const errorMessage = err?.response?.data?.message || 
                           err?.message || 
                           'Không thể tải danh sách địa chỉ';
      
      console.error('Error fetching addresses:', errorMessage);
      setError(errorMessage);
      
      // Đặt addresses thành mảng rỗng để tránh lỗi
      setAddresses([]);
      setDefaultAddress(null);
    } finally {
      setLoading(false);
    }
  };

  // Hàm làm mới địa chỉ - có thể gọi sau khi thêm địa chỉ mới
  const refreshAddresses = () => {
    fetchAddresses();
  };

  // Tải địa chỉ khi component được mount
  useEffect(() => {
    fetchAddresses();
  }, []);

  return {
    addresses,
    defaultAddress,
    loading,
    error,
    refreshAddresses
  };
};