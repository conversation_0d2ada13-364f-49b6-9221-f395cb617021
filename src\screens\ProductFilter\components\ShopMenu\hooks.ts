/* eslint-disable react-hooks/exhaustive-deps */
import { find, map, pick } from 'lodash';
import { useProductsStore } from 'stores/products';
import { flatTopics } from 'utils/index';

export const useCategoriesMenu = ({ id }: any) => {
  const categories = useProductsStore((state: any) => state?.categories);

  const flatted = flatTopics(categories);
  if (id) {
    const topic = find(flatted, { id });

    return {
      categories: topic?.childCategories,
    };
  } else {
    return {
      categories: map(categories, item => pick(item, ['id', 'name'])),
    };
  }
};
