/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useRef, useEffect } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import LinearGradient from 'react-native-linear-gradient';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { PieChart } from 'react-native-chart-kit';

import { useSystemReport } from './hooks';
import CustomAlert from 'utils/widgets/CustomAlert';
import colors from 'themes/colors';
import fonts from 'themes/fonts';
import metrics from 'themes/metrics';
import { STORAGE } from 'constants/authentication';
import { checkAuthentication } from 'utils/authUtils';

const { width } = Dimensions.get('window');
const chartWidth = width - 40;

type RootStackParamList = {
  SystemReportScreen: undefined;
  AppTab: undefined;
  Authentication: undefined;
};

type NavigationProps = NativeStackNavigationProp<RootStackParamList>;

const SystemReportScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProps>();
  const { systemData, loading, error, fetchSystemData } = useSystemReport();
  const [refreshing, setRefreshing] = useState(false);
  const [alert, setAlert] = useState<{
    visible: boolean;
    type: 'success' | 'error' | 'info' | 'confirm';
    title: string;
    message: string;
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[];
  } | null>(null);

  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.98)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 100,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
    return () => {
      fadeAnim.setValue(0);
      scaleAnim.setValue(0.98);
      slideAnim.setValue(50);
    };
  }, [fadeAnim, scaleAnim, slideAnim]);

  useEffect(() => {
    const checkAuth = async () => {
      const { isAuthenticated } = await checkAuthentication();
      if (!isAuthenticated) {
        showAlert(
          'info',
          'Yêu cầu đăng nhập',
          'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại để xem báo cáo hệ thống.',
          [
            {
              text: 'Đăng nhập',
              style: 'default',
              onPress: async () => {
                try {
                  await AsyncStorage.multiRemove([
                    STORAGE.ACCESS_TOKEN,
                    STORAGE.REFRESH_TOKEN,
                    STORAGE.EMAIL
                  ]);
                  hideAlert();
                  navigation.reset({
                    index: 0,
                    routes: [{ name: 'Authentication' }],
                  });
                } catch (error) {
                  console.error('Logout error:', error);
                }
              }
            }
          ]
        );
      }
    };

    checkAuth();
  }, [navigation]);

  useEffect(() => {
    if (error) {
      showAlert('error', 'Lỗi', error, [{ text: 'OK', onPress: hideAlert }]);
    }
  }, [error]);

  const showAlert = (
    type: 'success' | 'error' | 'info' | 'confirm',
    title: string,
    message: string,
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[]
  ) => {
    setAlert({ visible: true, type, title, message, buttons });
  };

  const hideAlert = () => {
    setAlert(null);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchSystemData();
    setRefreshing(false);
  };

  const handleLogout = () => {
    showAlert(
      'confirm',
      'Đăng xuất',
      'Bạn có chắc chắn muốn đăng xuất khỏi hệ thống?',
      [
        { text: 'Hủy', style: 'cancel', onPress: hideAlert },
        {
          text: 'Đăng xuất',
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.multiRemove([
                STORAGE.ACCESS_TOKEN,
                STORAGE.REFRESH_TOKEN,
                STORAGE.EMAIL,
              ]);
              hideAlert();
              navigation.reset({
                index: 0,
                routes: [{ name: 'Authentication' }],
              });
            } catch (error) {
              console.error('Logout error:', error);
            }
          },
        },
      ]
    );
  };

  const formatNumber = (num: number) => num.toLocaleString('vi-VN');
  const formatCurrency = (num: number) => `${num.toLocaleString('vi-VN')} VNĐ`;
  const formatShortNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const chartConfig = {
    backgroundColor: '#ffffff',
    backgroundGradientFrom: '#ffffff',
    backgroundGradientTo: '#f8f9fa',
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(76, 175, 80, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(55, 71, 79, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: '#4CAF50',
    },
    propsForBackgroundLines: {
      strokeDasharray: '',
      stroke: '#e0e0e0',
      strokeWidth: 1,
    },
  };

  const getOrderStatusData = () => {
    if (!systemData) return [];
    return [
      {
        name: 'Thành công',
        population: systemData.tongSoDonHangThanhCong,
        color: '#4CAF50',
        legendFontColor: '#37474F',
        legendFontSize: 12,
      },
      {
        name: 'Thất bại',
        population: systemData.tongSoDonHangKhongThanhCong,
        color: '#F44336',
        legendFontColor: '#37474F',
        legendFontSize: 12,
      },
    ];
  };

  const renderSummaryCards = () => (
    <View style={styles.summaryContainer}>
      <View style={styles.summaryRow}>
        <View style={[styles.summaryCard, { backgroundColor: '#E3F2FD' }]}>
          <MaterialCommunityIcons name="eye" size={24} color="#2196F3" />
          <Text style={[styles.summaryValue, { color: '#2196F3' }]}>
            {formatShortNumber(systemData?.soLuongTruyCap || 0)}
          </Text>
          <Text style={styles.summaryLabel}>Truy cập</Text>
        </View>
        <View style={[styles.summaryCard, { backgroundColor: '#FFF3E0' }]}>
          <MaterialCommunityIcons name="account-group" size={24} color="#FF9800" />
          <Text style={[styles.summaryValue, { color: '#FF9800' }]}>
            {formatShortNumber(systemData?.soNguoiBan || 0)}
          </Text>
          <Text style={styles.summaryLabel}>Người bán</Text>
        </View>
      </View>
      <View style={styles.summaryRow}>
        <View style={[styles.summaryCard, { backgroundColor: '#F3E5F5' }]}>
          <MaterialCommunityIcons name="package-variant" size={24} color="#9C27B0" />
          <Text style={[styles.summaryValue, { color: '#9C27B0' }]}>
            {formatShortNumber(systemData?.tongSoSanPham || 0)}
          </Text>
          <Text style={styles.summaryLabel}>Sản phẩm</Text>
        </View>
        <View style={[styles.summaryCard, { backgroundColor: '#FFF8E1' }]}>
          <MaterialCommunityIcons name="currency-usd" size={24} color="#FFC107" />
          <Text style={[styles.summaryValue, { color: '#FFC107' }]}>
            {formatShortNumber(systemData?.tongGiaTriGiaoDich || 0)}
          </Text>
          <Text style={styles.summaryLabel}>Doanh thu</Text>
        </View>
      </View>
    </View>
  );

  const renderChart = () => {
    if (!systemData) return null;

    return (
      <Animated.View
        style={[
          styles.chartContainer,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        <LinearGradient
          colors={['rgba(255,255,255,0.95)', 'rgba(255,255,255,0.85)']}
          style={styles.chartCard}
        >
          <View style={styles.chartContent}>
            <Text style={styles.chartTitle}>Tỉ lệ thành công đơn hàng</Text>
            <PieChart
              data={getOrderStatusData()}
              width={chartWidth - 40}
              height={220}
              chartConfig={chartConfig}
              accessor="population"
              backgroundColor="transparent"
              paddingLeft="15"
              absolute
            />
          </View>
        </LinearGradient>
      </Animated.View>
    );
  };

  const renderDetailedStats = () => (
    <Animated.View
      style={[
        styles.detailsContainer,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      <LinearGradient
        colors={['rgba(255,255,255,0.95)', 'rgba(255,255,255,0.85)']}
        style={styles.detailsCard}
      >
        <Text style={styles.detailsTitle}>Chi tiết thống kê</Text>
        <View style={styles.detailsGrid}>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Người bán mới</Text>
            <Text style={[styles.detailValue, { color: '#4CAF50' }]}>
              {formatNumber(systemData?.soNguoiBanMoi || 0)}
            </Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Sản phẩm mới</Text>
            <Text style={[styles.detailValue, { color: '#00BCD4' }]}>
              {formatNumber(systemData?.soSanPhamMoi || 0)}
            </Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Giao dịch</Text>
            <Text style={[styles.detailValue, { color: '#FF5722' }]}>
              {formatNumber(systemData?.soLuongGiaoDich || 0)}
            </Text>
          </View>
          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Tỷ lệ thành công</Text>
            <Text style={[styles.detailValue, { color: '#4CAF50' }]}>
              {systemData
                ? `${(
                    (systemData.tongSoDonHangThanhCong /
                      (systemData.tongSoDonHangThanhCong +
                        systemData.tongSoDonHangKhongThanhCong)) *
                    100
                  ).toFixed(1)}%`
                : '0%'}
            </Text>
          </View>
        </View>
      </LinearGradient>
    </Animated.View>
  );

  if (loading && !systemData) {
    return (
      <SafeAreaView style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors['Primary/600']} />
        <Text style={styles.loadingText}>Đang tải báo cáo hệ thống...</Text>
      </SafeAreaView>
    );
  }

  if (error && !systemData) {
    return (
      <SafeAreaView style={styles.errorContainer}>
        <MaterialCommunityIcons
          name="alert-circle-outline"
          size={60}
          color={colors['Danger/600']}
          style={styles.errorIcon}
        />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={fetchSystemData}
          activeOpacity={0.7}
        >
          <MaterialCommunityIcons
            name="refresh"
            size={18}
            color={colors.white}
            style={styles.buttonIcon}
          />
          <Text style={styles.retryButtonText}>Thử lại</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeAreaView}>
      <LinearGradient
        colors={['#0A6D3D', '#0B8249', '#0B8249']}
        style={styles.headerGradient}
      >
        <View style={styles.headerContainer}>
          <View style={styles.headerTop}>
            <Text style={styles.headerTitle}>Phân tích hệ thống</Text>
            <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
              <MaterialCommunityIcons name="logout" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          </View>
          <View style={styles.headerStats}>
            <MaterialCommunityIcons name="chart-box-outline" size={32} color="#FFFFFF" />
            <Text style={styles.headerSubtitle}>Báo cáo hệ thống chi tiết</Text>
          </View>
        </View>
      </LinearGradient>

      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#3949AB']}
            tintColor="#3949AB"
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {systemData && (
          <>
            {renderSummaryCards()}
            {renderChart()}
            {renderDetailedStats()}
          </>
        )}
      </ScrollView>

      {alert && (
        <CustomAlert
          visible={alert.visible}
          type={alert.type}
          title={alert.title}
          message={alert.message}
          buttons={alert.buttons}
          onDismiss={hideAlert}
          dismissable={alert.type !== 'success'}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeAreaView: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  headerGradient: {
    paddingTop: 20,
    paddingBottom: 30,
  },
  headerContainer: {
    paddingHorizontal: 20,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  headerTitle: {
    ...fonts.style.normal,
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: '700',
    flex: 1,
  },
  logoutButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerStats: {
    alignItems: 'center',
    marginTop: 10,
  },
  headerSubtitle: {
    ...fonts.style.normal,
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
    marginTop: 8,
    opacity: 0.9,
  },
  scrollContainer: {
    padding: 20,
    paddingBottom: 30,
  },
  summaryContainer: {
    marginBottom: 20,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryCard: {
    flex: 1,
    marginHorizontal: 6,
    padding: 20,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  summaryValue: {
    ...fonts.style.normal,
    fontSize: 20,
    fontWeight: '700',
    marginVertical: 8,
  },
  summaryLabel: {
    ...fonts.style.normal,
    fontSize: 12,
    color: '#37474F',
    fontWeight: '500',
  },
  chartContainer: {
    marginBottom: 20,
  },
  chartCard: {
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    overflow: 'hidden',
  },
  chartTitle: {
    ...fonts.style.normal,
    fontSize: 16,
    fontWeight: '600',
    color: '#37474F',
    marginBottom: 16,
    textAlign: 'center',
  },
  chartContent: {
    alignItems: 'center',
    paddingBottom: 20,
  },
  detailsContainer: {
    marginBottom: 20,
  },
  detailsCard: {
    borderRadius: 16,
    padding: 20,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  detailsTitle: {
    ...fonts.style.normal,
    fontSize: 18,
    fontWeight: '700',
    color: '#37474F',
    marginBottom: 16,
    textAlign: 'center',
  },
  detailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  detailItem: {
    width: '48%',
    backgroundColor: '#F8F9FA',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    alignItems: 'center',
  },
  detailLabel: {
    ...fonts.style.normal,
    fontSize: 12,
    color: '#37474F',
    fontWeight: '500',
    marginBottom: 4,
  },
  detailValue: {
    ...fonts.style.normal,
    fontSize: 16,
    fontWeight: '700',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  loadingText: {
    ...fonts.style.description,
    color: '#3949AB',
    marginTop: 16,
    fontSize: 16,
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#F8F9FA',
  },
  errorIcon: {
    marginBottom: 16,
  },
  errorText: {
    ...fonts.style.description,
    color: '#F44336',
    textAlign: 'center',
    marginBottom: 24,
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 24,
  },
  retryButton: {
    flexDirection: 'row',
    backgroundColor: '#3949AB',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: '#3949AB',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  retryButtonText: {
    ...fonts.style.normal,
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 14,
    marginLeft: 8,
  },
  buttonIcon: {
    marginRight: 4,
  },
});

export default SystemReportScreen;