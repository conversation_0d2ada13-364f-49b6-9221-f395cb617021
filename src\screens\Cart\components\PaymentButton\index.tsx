import React from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import metrics from 'themes/metrics';
import { Text } from '@ui-kitten/components';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import { sumBy } from 'lodash';
import commonStyles from 'themes/commonStyles';
import CommonButton from 'components/CommonButton';
import numeral from 'numeral';

const PaymentButton = ({ selected, syncSelectedItems }) => {
  const navigation = useNavigation();

  const total = sumBy(
    selected,
    (product) =>
      parseFloat(product?.price) * parseFloat(product?.quantity),
  );

  const handlePress = async () => {
    if (selected.length === 0) {
      Alert.alert('Thông báo', 'Vui lòng chọn sản phẩm');
      return;
    }

    const success = await syncSelectedItems();
    if (success) {
      navigation.navigate('PreviewPayment');
    } else {
      console.error('Failed to sync selected items');
      Alert.alert('Lỗi', 'Không thể đồng bộ sản phẩm đã chọn');
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.row}>
        <View style={styles.col}>
          <Text style={commonStyles.mainText}>Thành tiền</Text>
          <Text style={styles.title}>{numeral(total).format('0')}đ</Text>
        </View>
        <CommonButton  customStyles={{
            backgroundColor: colors['Moss/500'] || '#4CAF50',
            borderColor: colors['Moss/500'] || '#4CAF50',
          }} onPress={handlePress}>Mua hàng</CommonButton>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: metrics.spacing4,
    backgroundColor: 'white',
  },
  col: {
    flexDirection: 'column',
  },
  row: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Moss/600'],
    marginTop: metrics.spacing2,
  },
});

export default PaymentButton;