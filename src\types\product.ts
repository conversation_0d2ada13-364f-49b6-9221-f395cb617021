import { Merchant } from "./order";

export interface Product {
  id: number;
  shopCategoryId: number;
  merchantId: number;
  productCode: string;
  title: string;
  rating: number;
  countNumOfRating: number;
  countNumOfOrders: number;
  price: number;
  anchoPrice: number;
  discountPercent: number;
  shortDescription: string;
  fullDescription: string;
  brandName: string;
  unit: string;
  placeOfOrigin: string;
  mainImageUrl: string;
  otherImageUrls: string[];
  comments: null;
  merchant: Merchant;
  suggestedProducts: null;
  variants: null;
  mainAttribute: null;
  secondAttribute: null;
  createdAt: string;
  updatedAt: string;
  status: string;
  rejectionReason: null;
}
