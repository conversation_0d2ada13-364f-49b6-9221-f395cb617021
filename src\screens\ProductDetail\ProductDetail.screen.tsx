import React, { useState, useEffect } from 'react';
import {
  SafeAreaView,
  StyleSheet,
  ScrollView,
  Dimensions,
  View,
  TouchableOpacity,
  Text,
  ActivityIndicator,
  Modal,
  Pressable,
} from 'react-native';
import { useProductDetail } from './hooks';
import ProductImages from './components/ProductImages';
import ProductPrices from './components/ProductPrices';
import Comment from './components/Comment';
import ProductDescription from './components/ProductDescription';
import MerchantInfo from './components/MerchantInfo';
import RelateProduct from './components/RelateProduct';
import HomeHeader from 'components/Header/HomeHeader';
import CustomAlert from 'utils/widgets/CustomAlert';
import colors from 'themes/colors';
import metrics from 'themes/metrics';
import { addToCart } from 'api/cart';
import { useCart } from 'hooks/contexts/CartContext';

import { useAuthCheck } from 'utils/authUtils';

const { width } = Dimensions.get('window');

interface ProductDetailProps {
  route: {
    params?: {
      id?: string | number;
    };
  };
  navigation: any;
}

const ProductDetail: React.FC<ProductDetailProps> = ({ route, navigation }) => {
  const id = route?.params?.id;
  const { product, loading, error } = useProductDetail({ id });
  const [selectedVariant, setSelectedVariant] = useState<any>(null);
  const [quantity, setQuantity] = useState(1);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'cart' | 'buy' | null>(null);
  const [modalLoading, setModalLoading] = useState(false);
  const [alert, setAlert] = useState<{
    visible: boolean;
    type: 'success' | 'error' | 'info' | 'confirm';
    title: string;
    message: string;
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[];
  } | null>(null);
  const { setCartCount, fetchCartCount } = useCart();

  const showAlert = (
    type: 'success' | 'error' | 'info' | 'confirm',
    title: string,
    message: string,
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[]
  ) => {
    setAlert({ visible: true, type, title, message, buttons });
  };

  const hideAlert = () => {
    setAlert(null);
  };

  const { requireAuth } = useAuthCheck(navigation, showAlert, hideAlert);

  useEffect(() => {
    if (product?.variants?.length > 0) {
      setSelectedVariant(product.variants[0]);
    }
  }, [product]);

  const handleVariantSelect = (variant: any) => {
    setSelectedVariant(variant);
  };

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity > 0) {
      setQuantity(newQuantity);
    }
  };

  const openModal = (type: 'cart' | 'buy') => {
    requireAuth(() => {
      setModalType(type);
      setModalVisible(true);
    }, type === 'cart' ? 'Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng' : 'Vui lòng đăng nhập để mua hàng');
  };

  const closeModal = () => {
    setModalVisible(false);
  };

  const handleAddToCart = async () => {
    if (!selectedVariant) {
      Toast.show({
        type: 'info',
        text1: 'Vui lòng chọn loại sản phẩm',
        position: 'top',
      });
      return;
    }

    setModalLoading(true);
    try {
      const cartItem = {
        id: product.id,
        merchantId: product.merchantId,
        mainAttributeValueId: selectedVariant.mainAttributeValueId,
        secondAttributeValueId: selectedVariant.secondAttributeValueId,
        quantity: quantity,
        variantId: selectedVariant.id,
        price: selectedVariant.price,
      };

      const response = await addToCart(cartItem);
      
      if (response) {
        Toast.show({
          type: 'success',
          text1: 'Thêm vào giỏ hàng thành công',
          position: 'top',
        });
        
        // OPTION 1: Increment count directly (faster)
        //setCartCount(prev => prev + quantity);
        
        // OPTION 2: Fetch fresh count from server (more accurate)
         await fetchCartCount();
        
        closeModal();
      }
    } catch (e) {
      Toast.show({
        type: 'error',
        text1: 'Thêm vào giỏ hàng thất bại',
        text2: e.message || 'Vui lòng thử lại sau',
        position: 'top',
      });
    } finally {
      setModalLoading(false);
    }
  };

  const handleBuyNow = () => {
    if (!selectedVariant) {
      Toast.show({
        type: 'info',
        text1: 'Vui lòng chọn loại sản phẩm',
        position: 'top',
      });
      return;
    }
    
    closeModal();
    console.log('Mua hàng ngay:', {
      productId: product.id,
      variantId: selectedVariant.id,
      quantity,
    });
  };

  if (loading) {
    return (
      <>
        <HomeHeader noStretch backButton />
        <SafeAreaView style={styles.container}>
          <View style={styles.centerContent}>
            <ActivityIndicator size="large" color={colors['Moss/500']} />
            <Text style={styles.statusText}>Đang tải sản phẩm...</Text>
          </View>
        </SafeAreaView>
      </>
    );
  }

  if (error || !product?.id) {
    return (
      <>
        <HomeHeader noStretch backButton />
        <SafeAreaView style={styles.container}>
          <View style={styles.centerContent}>
            <Text style={[styles.statusText, styles.errorText]}>
              {error || 'Không tìm thấy sản phẩm.'}
            </Text>
          </View>
        </SafeAreaView>
      </>
    );
  }

  const renderVariantOptions = () => {
    if (!product?.mainAttribute) return null;
    
    return (
      <View style={styles.modalSection}>
        <Text style={styles.modalSectionTitle}>{product.mainAttribute.name}:</Text>
        <View style={styles.variantOptions}>
          {product.variants.map((variant: any) => {
            const isSelected = selectedVariant?.id === variant.id;
            return (
              <TouchableOpacity
                key={variant.id}
                style={[
                  styles.variantButton,
                  isSelected && styles.selectedVariantButton,
                ]}
                onPress={() => handleVariantSelect(variant)}
              >
                <Text
                  style={[
                    styles.variantText,
                    isSelected && styles.selectedVariantText,
                  ]}
                >
                  {variant.title.split('-')[1].trim()}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  return (
    <>
      <HomeHeader noStretch backButton />
      <SafeAreaView style={styles.container}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          <ProductImages
            uri={product?.otherImageUrls}
            style={styles.productImages}
          />
          
          <View style={styles.detailsContainer}>
            <ProductPrices {...product} />

            <View style={styles.divider} />
            
            <ProductDescription {...product} />
            <MerchantInfo merchantId={product?.merchantId} />
            <Comment id={product.id} />
            <RelateProduct />
          </View>
        </ScrollView>
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.addToCartButton]}
            onPress={() => openModal('cart')}
            activeOpacity={0.7}
          >
            <Text style={styles.addToCartText}>Thêm vào giỏ</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, styles.buyNowButton]}
            onPress={() => openModal('buy')}
            activeOpacity={0.7}
          >
            <Text style={styles.buyNowText}>Mua ngay</Text>
          </TouchableOpacity>
        </View>

        <Modal
          animationType="slide"
          transparent={true}
          visible={modalVisible}
          onRequestClose={closeModal}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalTitle}>
                  {modalType === 'cart' ? 'Thêm vào giỏ hàng' : 'Mua ngay'}
                </Text>
                <TouchableOpacity onPress={closeModal} style={styles.closeButton}>
                  <Text style={styles.closeButtonText}>✕</Text>
                </TouchableOpacity>
              </View>
              
              <View style={styles.modalBody}>
                <View style={styles.productPreview}>
                  <View style={styles.productImageContainer}>
                    <ProductImages
                      uri={[product?.otherImageUrls[0]]}
                      style={styles.productThumbnail}
                    />
                  </View>
                  <View style={styles.productInfo}>
                    <Text style={styles.productTitle} numberOfLines={2}>
                      {product.title}
                    </Text>
                    <Text style={styles.productPrice}>
                      {selectedVariant ? selectedVariant.price.toLocaleString() : product.price.toLocaleString()} đ
                    </Text>
                  </View>
                </View>
                
                {renderVariantOptions()}
                
                <View style={styles.modalSection}>
                  <Text style={styles.modalSectionTitle}>Số lượng:</Text>
                  <View style={styles.quantityControls}>
                    <TouchableOpacity
                      style={styles.quantityButton}
                      onPress={() => handleQuantityChange(quantity - 1)}
                    >
                      <Text style={styles.quantityButtonText}>-</Text>
                    </TouchableOpacity>
                    <Text style={styles.quantityValue}>{quantity}</Text>
                    <TouchableOpacity
                      style={styles.quantityButton}
                      onPress={() => handleQuantityChange(quantity + 1)}
                    >
                      <Text style={styles.quantityButtonText}>+</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
              
              <TouchableOpacity
                style={[
                  styles.modalButton,
                  modalType === 'cart' ? styles.cartActionButton : styles.buyActionButton
                ]}
                onPress={modalType === 'cart' ? handleAddToCart : handleBuyNow}
                disabled={modalLoading}
              >
                {modalLoading ? (
                  <ActivityIndicator size="small" color={modalType === 'cart' ? colors['Moss/700'] : colors.white} />
                ) : (
                  <Text style={modalType === 'cart' ? styles.cartActionText : styles.buyActionText}>
                    {modalType === 'cart' ? 'Thêm vào giỏ hàng' : 'Mua ngay'}
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
      {alert && (
        <CustomAlert
          visible={alert.visible}
          type={alert.type}
          title={alert.title}
          message={alert.message}
          buttons={alert.buttons}
          onDismiss={hideAlert}
          dismissable={alert.type !== 'success'}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: metrics.spacing4,
  },
  statusText: {
    fontSize: 16,
    marginTop: metrics.spacing3,
    color: colors['Gray/700'],
    textAlign: 'center',
  },
  errorText: {
    color: colors['Danger/600'],
  },
  scrollContent: {
    paddingBottom: 80,
  },
  productImages: {
    width: width,
    height: width * 0.8,
    resizeMode: 'contain',
  },
  detailsContainer: {
    padding: metrics.spacing3,
  },
  divider: {
    height: 6,
    backgroundColor: colors['Gray/50'],
    marginVertical: metrics.spacing4,
    marginHorizontal: -metrics.spacing3,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    paddingHorizontal: metrics.spacing3,
    paddingVertical: metrics.spacing2,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors['Gray/100'],
    elevation: 3,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: -1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  button: {
    flex: 1,
    height: 44,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    margin: metrics.spacing1,
  },
  addToCartButton: {
    backgroundColor: colors['Moss/50'],
    borderWidth: 1,
    borderColor: colors['Moss/500'],
  },
  buyNowButton: {
    backgroundColor: colors['Moss/500'],
  },
  addToCartText: {
    fontSize: 15,
    fontWeight: '600',
    color: colors['Moss/700'],
  },
  buyNowText: {
    fontSize: 15,
    fontWeight: '600',
    color: colors.white,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: metrics.spacing4 + 16,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing3,
    borderBottomWidth: 1,
    borderBottomColor: colors['Gray/100'],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors['Gray/900'],
  },
  closeButton: {
    padding: metrics.spacing2,
  },
  closeButtonText: {
    fontSize: 16,
    color: colors['Gray/700'],
    fontWeight: '600',
  },
  modalBody: {
    padding: metrics.spacing4,
  },
  productPreview: {
    flexDirection: 'row',
    marginBottom: metrics.spacing4,
    alignItems: 'center',
  },
  productImageContainer: {
    width: 80,
    height: 80,
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors['Gray/100'],
  },
  productThumbnail: {
    width: 80,
    height: 80,
    resizeMode: 'cover',
  },
  productInfo: {
    flex: 1,
    marginLeft: metrics.spacing3,
  },
  productTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors['Gray/800'],
    marginBottom: metrics.spacing2,
  },
  productPrice: {
    fontSize: 18,
    fontWeight: '700',
    color: colors['Warning/500'],
  },
  modalSection: {
    marginBottom: metrics.spacing4,
  },
  modalSectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: colors['Gray/800'],
    marginBottom: metrics.spacing3,
  },
  variantOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: metrics.spacing2,
  },
  variantButton: {
    paddingHorizontal: metrics.spacing3,
    paddingVertical: metrics.spacing2,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors['Gray/200'],
    backgroundColor: colors.white,
    marginBottom: metrics.spacing2,
  },
  selectedVariantButton: {
    borderColor: colors['Moss/500'],
    backgroundColor: colors['Moss/50'],
  },
  variantText: {
    fontSize: 14,
    color: colors['Gray/700'],
  },
  selectedVariantText: {
    color: colors['Moss/700'],
    fontWeight: '500',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors['Gray/100'],
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: metrics.spacing2,
  },
  quantityButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors['Gray/700'],
  },
  quantityValue: {
    fontSize: 16,
    fontWeight: '500',
    color: colors['Gray/900'],
    minWidth: 30,
    textAlign: 'center',
  },
  modalButton: {
    marginHorizontal: metrics.spacing4,
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartActionButton: {
    backgroundColor: colors['Moss/50'],
    borderWidth: 1,
    borderColor: colors['Moss/500'],
  },
  buyActionButton: {
    backgroundColor: colors['Moss/500'],
  },
  cartActionText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors['Moss/700'],
  },
  buyActionText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});

export default ProductDetail;