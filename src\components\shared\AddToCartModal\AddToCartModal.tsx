import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Text,
  Modal,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
  Easing,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import metrics from 'themes/metrics';
import colors from 'themes/colors';
import Product from 'components/Product';
import CustomAlert from 'utils/widgets/CustomAlert';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAuthCheck } from 'utils/authUtils';
import { useAddToCart } from './hooks';

interface AddToCartModalProps {
  visible: boolean;
  product: any;
  onClose: () => void;
  onAddToCartSuccess?: () => void;
}

const AddToCartModal = ({ 
  visible, 
  product, 
  onClose,
  onAddToCartSuccess 
}: AddToCartModalProps) => {
  const navigation = useNavigation<any>();
  const [selectedVariant, setSelectedVariant] = useState<any>(null);
  const [quantity, setQuantity] = useState(1);
  const [successAnimation] = useState(new Animated.Value(0));
  const [alert, setAlert] = useState<{
    visible: boolean;
    type: 'success' | 'error' | 'info' | 'confirm';
    title: string;
    message: string;
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[];
  } | null>(null);

  const showAlert = (
    type: 'success' | 'error' | 'info' | 'confirm',
    title: string,
    message: string,
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[]
  ) => {
    setAlert({ visible: true, type, title, message, buttons });
  };

  const hideAlert = () => {
    setAlert(null);
  };

  const { handleAddToCart, addingToCart } = useAddToCart(navigation, showAlert, hideAlert);
  const { requireAuth } = useAuthCheck(navigation, showAlert, hideAlert);

  useEffect(() => {
    if (product?.variants?.length > 0) {
      setSelectedVariant(product.variants[0]);
    } else {
      setSelectedVariant(null);
    }
    setQuantity(1);
  }, [product]);

  const runSuccessAnimation = () => {
    successAnimation.setValue(0);
    Animated.timing(successAnimation, {
      toValue: 1,
      duration: 800,
      easing: Easing.elastic(1),
      useNativeDriver: true,
    }).start();
  };

  const handleAddToCartAction = async () => {
    if (!product) return;

    requireAuth(async () => {
      const success = await handleAddToCart(product, quantity, selectedVariant);
      if (success) {
        runSuccessAnimation();
        setTimeout(() => {
          onClose();
          onAddToCartSuccess?.();
        }, 1000);
      }
    }, 'Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng');
  };

  const handleQuantityChange = (newQuantity: number) => {
    const maxQuantity = selectedVariant?.stock || 99;
    if (newQuantity > 0 && newQuantity <= maxQuantity) {
      setQuantity(newQuantity);
    }
  };

  const handleVariantSelect = (variant: any) => {
    setSelectedVariant(variant);
    setQuantity(1);
  };

  const renderStockInfo = () => {
    if (!selectedVariant?.stock) return null;
    return (
      <Text style={styles.stockText}>
        Còn lại: {selectedVariant.stock} sản phẩm
      </Text>
    );
  };

  const renderVariantOptions = () => {
    if (!product?.variants || product.variants.length <= 1) return null;

    return (
      <View style={styles.modalSection}>
        <Text style={styles.modalSectionTitle}>
          {product?.mainAttribute?.name || 'Tùy chọn'}:
        </Text>
        <View style={styles.variantOptions}>
          {product.variants.map((variant: any) => {
            const isSelected = selectedVariant?.id === variant.id;
            const isOutOfStock = variant.stock === 0;
            
            return (
              <TouchableOpacity
                key={variant.id}
                style={[
                  styles.variantButton,
                  isSelected && styles.selectedVariantButton,
                  isOutOfStock && styles.outOfStockVariantButton,
                ]}
                onPress={() => !isOutOfStock && handleVariantSelect(variant)}
                disabled={isOutOfStock}
              >
                <Text
                  style={[
                    styles.variantText,
                    isSelected && styles.selectedVariantText,
                    isOutOfStock && styles.outOfStockVariantText,
                  ]}
                >
                  {variant.title.split('-').pop()?.trim()}
                  {isOutOfStock && ' (Hết hàng)'}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  const animatedStyle = {
    transform: [{
      scale: successAnimation.interpolate({
        inputRange: [0, 0.5, 1],
        outputRange: [1, 1.2, 1]
      })
    }],
    opacity: successAnimation.interpolate({
      inputRange: [0, 0.5, 1],
      outputRange: [1, 0.8, 1]
    })
  };

  if (!product) return null;

  return (
    <>
      <Modal
        animationType="slide"
        transparent={true}
        visible={visible}
        onRequestClose={onClose}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Thêm vào giỏ hàng</Text>
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Icon name="close" size={24} color={colors['Gray/700']} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <View style={styles.productPreview}>
                <View style={styles.productImageContainer}>
                  <Animated.View style={animatedStyle}>
                    <Product
                      uri={product?.mainImageUrl}
                      style={styles.productThumbnail}
                      hideDetails
                    />
                  </Animated.View>
                </View>
                <View style={styles.productInfo}>
                  <Text style={styles.productTitle} numberOfLines={2}>
                    {product?.title}
                  </Text>
                  <Text style={styles.productPrice}>
                    {selectedVariant
                      ? selectedVariant.price.toLocaleString()
                      : product?.price?.toLocaleString()}{' '}
                    đ
                  </Text>
                  {renderStockInfo()}
                </View>
              </View>

              {renderVariantOptions()}

              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Số lượng:</Text>
                <View style={styles.quantityControls}>
                  <TouchableOpacity
                    style={[
                      styles.quantityButton,
                      quantity <= 1 && styles.disabledQuantityButton
                    ]}
                    onPress={() => handleQuantityChange(quantity - 1)}
                    disabled={quantity <= 1}
                  >
                    <Text style={styles.quantityButtonText}>-</Text>
                  </TouchableOpacity>
                  <Text style={styles.quantityValue}>{quantity}</Text>
                  <TouchableOpacity
                    style={[
                      styles.quantityButton,
                      quantity >= (selectedVariant?.stock || 99) && styles.disabledQuantityButton
                    ]}
                    onPress={() => handleQuantityChange(quantity + 1)}
                    disabled={quantity >= (selectedVariant?.stock || 99)}
                  >
                    <Text style={styles.quantityButtonText}>+</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>

            <TouchableOpacity
              style={[
                styles.modalButton,
                (addingToCart || (selectedVariant?.stock === 0)) && styles.disabledButton
              ]}
              onPress={handleAddToCartAction}
              disabled={addingToCart || (selectedVariant?.stock === 0)}
            >
              {addingToCart ? (
                <ActivityIndicator size="small" color={colors.white} />
              ) : (
                <Text style={styles.modalButtonText}>
                  {selectedVariant?.stock === 0 ? 'Hết hàng' : 'Thêm vào giỏ hàng'}
                </Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      
      {alert && (
        <CustomAlert
          visible={alert.visible}
          type={alert.type}
          title={alert.title}
          message={alert.message}
          buttons={alert.buttons}
          onDismiss={hideAlert}
          dismissable={alert.type !== 'success'}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: metrics.spacing4,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing3,
    borderBottomWidth: 1,
    borderBottomColor: colors['Gray/100'],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors['Gray/900'],
  },
  closeButton: {
    padding: metrics.spacing1,
  },
  modalBody: {
    padding: metrics.spacing4,
  },
  productPreview: {
    flexDirection: 'row',
    marginBottom: metrics.spacing4,
    alignItems: 'center',
  },
  productImageContainer: {
    width: 80,
    height: 80,
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors['Gray/100'],
  },
  productThumbnail: {
    width: 80,
    height: 80,
    resizeMode: 'cover',
  },
  productInfo: {
    flex: 1,
    marginLeft: metrics.spacing3,
  },
  productTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors['Gray/800'],
    marginBottom: metrics.spacing2,
  },
  productPrice: {
    fontSize: 18,
    fontWeight: '700',
    color: colors['Warning/500'],
  },
  modalSection: {
    marginBottom: metrics.spacing4,
  },
  modalSectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: colors['Gray/800'],
    marginBottom: metrics.spacing3,
  },
  variantOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: metrics.spacing2,
  },
  variantButton: {
    paddingHorizontal: metrics.spacing3,
    paddingVertical: metrics.spacing2,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors['Gray/200'],
    backgroundColor: colors.white,
    marginBottom: metrics.spacing2,
  },
  selectedVariantButton: {
    borderColor: colors['Moss/500'],
    backgroundColor: colors['Moss/50'],
  },
  variantText: {
    fontSize: 14,
    color: colors['Gray/700'],
  },
  selectedVariantText: {
    color: colors['Moss/700'],
    fontWeight: '500',
  },
  outOfStockVariantButton: {
    borderColor: colors['Gray/300'],
    backgroundColor: colors['Gray/100'],
  },
  outOfStockVariantText: {
    color: colors['Gray/500'],
    textDecorationLine: 'line-through',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors['Gray/100'],
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: metrics.spacing2,
  },
  disabledQuantityButton: {
    opacity: 0.5,
  },
  quantityButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors['Gray/700'],
  },
  quantityValue: {
    fontSize: 16,
    fontWeight: '500',
    color: colors['Gray/900'],
    minWidth: 30,
    textAlign: 'center',
  },
  modalButton: {
    marginHorizontal: metrics.spacing4,
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors['Moss/500'],
    flexDirection: 'row',
    gap: metrics.spacing2,
  },
  disabledButton: {
    backgroundColor: colors['Gray/400'],
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  stockText: {
    fontSize: 12,
    color: colors['Gray/600'],
    marginTop: 4,
  },
});

export default AddToCartModal;
