import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Icon from 'react-native-vector-icons/AntDesign';
import metrics from 'themes/metrics';
import commonStyles from 'themes/commonStyles';
import colors from 'themes/colors';
import { CheckBox } from '@ui-kitten/components';
import FastImage from 'react-native-fast-image';

type Props = {
  checked?: boolean;
  uri?: string;
  title: string;
  anchoPrice: string | number;
  price: string | number;
  qty?: number;
  setQty?: (a: number) => void;
  setChecked?: (checked: boolean) => void;
  onDelete?: () => void;
  variant?: string;
  category?: string;
  onChangeVariant?: () => void;
  onPress?: () => void; 
};

function ProductCard({
  anchoPrice,
  price,
  checked,
  uri,
  title,
  qty = 1,
  setQty,
  setChecked,
  onDelete,
  variant,
  category,
  onChangeVariant,
  onPress, // Added onPress prop
}: Props) {
  const plus = () => {
    if (setQty) setQty(qty + 1);
  };

  const minus = () => {
    if (qty <= 1 || !setQty) return;
    setQty(qty - 1);
  };

  return (
    <TouchableOpacity 
      style={styles.card}
      onPress={onPress}
      activeOpacity={onPress ? 0.7 : 1}
      disabled={!onPress}
    >
      <View style={styles.container}>
        {/* Checkbox */}
        <View style={styles.checkboxContainer}>
          <CheckBox
            onChange={setChecked}
            status="basic"
            checked={checked}
          />
        </View>

        {/* Hình ảnh và thông tin sản phẩm */}
        <View style={styles.main}>
          <FastImage
            style={styles.productImage}
            source={
              uri ? { uri } : require('assets/images/product-fallback-image.png')
            }
            resizeMode={FastImage.resizeMode.cover}
          />
          <View style={styles.info}>
            <Text
              style={[commonStyles.mainText, styles.titleText]}
              numberOfLines={2}
              ellipsizeMode="tail"
            >
              {title}
            </Text>
            
            {/* Category display */}
            {category ? (
              <Text
                style={styles.categoryText}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {category}
              </Text>
            ) : null}
            
            {/* Variant display with change option */}
            {variant ? (
              <View style={styles.variantRow}>
                <Text
                  style={styles.variantText}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {variant}
                </Text>
                {onChangeVariant && (
                  <TouchableOpacity 
                    onPress={(e) => {
                      e.stopPropagation(); // Prevent triggering the parent onPress
                      onChangeVariant();
                    }} 
                    style={styles.changeVariantBtn}
                  >
                    <Text style={styles.changeVariantText}>Thay đổi</Text>
                  </TouchableOpacity>
                )}
              </View>
            ) : null}
            
            <View style={styles.priceRow}>
              <Text style={[commonStyles.hintPriceText, styles.hintPriceText]}>
                {anchoPrice}đ
              </Text>
              <Text style={[commonStyles.priceText, styles.priceText]}>
                {price}đ
              </Text>
            </View>
            <View style={styles.quantityRow}>
              <TouchableOpacity
                onPress={(e) => {
                  e.stopPropagation(); // Prevent triggering the parent onPress
                  minus();
                }}
                style={[styles.quantityButton, qty <= 1 && styles.disabledButton]}
                disabled={qty <= 1}
              >
                <Icon
                  size={14}
                  color={
                    qty <= 1
                      ? colors['Grayiron/400']
                      : colors['Grayiron/600']
                  }
                  name="minus"
                />
              </TouchableOpacity>
              <Text style={styles.quantityInput}>{qty}</Text>
              <TouchableOpacity
                onPress={(e) => {
                  e.stopPropagation(); // Prevent triggering the parent onPress
                  plus();
                }}
                style={styles.quantityButton}
              >
                <Icon
                  size={14}
                  color={colors['Grayiron/600']}
                  name="plus"
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Icon xóa */}
        {onDelete && (
          <TouchableOpacity
            onPress={(e) => {
              e.stopPropagation(); // Prevent triggering the parent onPress
              onDelete();
            }}
            style={styles.deleteButton}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Icon size={18} color={colors['Danger/600']} name="delete" />
          </TouchableOpacity>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
    marginVertical: metrics.spacing2,
    padding: metrics.spacing3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkboxContainer: {
    justifyContent: 'center',
    marginRight: metrics.spacing2,
    
  },
  main: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: metrics.radius2,
    marginRight: metrics.spacing3,
  },
  info: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  titleText: {
    fontSize: 14,
    lineHeight: 18,
  },
  categoryText: {
    fontSize: 12,
    color: colors['Primary/600'],
    marginTop: metrics.spacing1,
  },
  variantRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: metrics.spacing1,
  },
  variantText: {
    fontSize: 12,
    color: colors['Grayiron/600'],
    flex: 1,
  },
  changeVariantBtn: {
    paddingHorizontal: metrics.spacing2,
    paddingVertical: metrics.spacing1 / 2,
  },
  changeVariantText: {
    fontSize: 12,
    color: colors['Primary/600'],
    fontWeight: '500',
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: metrics.spacing1,
  },
  hintPriceText: {
    textDecorationLine: 'line-through',
    marginRight: metrics.spacing2,
  },
  priceText: {
    fontWeight: '600',
  },
  quantityRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: metrics.spacing2,
  },
  quantityButton: {
    width: 28,
    height: 28,
    borderWidth: 1,
    borderColor: colors['Grayiron/200'],
    borderRadius: metrics.radius2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    borderColor: colors['Gray/100'],
    backgroundColor: colors['Grayiron/50'],
  },
  quantityInput: {
    width: 40,
    textAlign: 'center',
    fontSize: 14,
    color: colors['Gray/800'],
    marginHorizontal: metrics.spacing1,
  },
  deleteButton: {
    padding: metrics.spacing2,
    justifyContent: 'center',
  },
});

export default ProductCard;