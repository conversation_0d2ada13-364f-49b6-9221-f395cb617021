import { useState, useEffect, useCallback } from 'react';
import { debounce } from 'lodash';
import { listAddress, deleteAddress } from 'api/addressBooks';
import { Address } from 'types/address';

interface UseAddressReturn {
  addresses: Address[];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  removeAddress: (id: string) => Promise<void>;
}

const useAddress = (): UseAddressReturn => {
  const [state, setState] = useState<{
    addresses: Address[];
    loading: boolean;
    error: string | null;
  }>({
    addresses: [],
    loading: true,
    error: null,
  });

  const fetchAddresses = useCallback(async (signal?: AbortSignal) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const response = await listAddress({ signal });
      // Ensure addresses is always an array
      const addresses = Array.isArray(response?.addresses) ? response.addresses : [];
      setState({
        addresses,
        loading: false,
        error: null,
      });
    } catch (err) {
      let errorMessage = 'Lỗi không xác định';
      if (err instanceof Error) {
        errorMessage = err.message;
        if ('response' in err) {
          const apiError = err as any;
          if (apiError.response?.status === 401) {
            errorMessage = 'Phiên đăng nhập hết hạn. Vui lòng đăng nhập lại.';
          } else if (apiError.response?.status === 500) {
            errorMessage = 'Lỗi máy chủ. Vui lòng thử lại sau.';
          } else {
            errorMessage = apiError.response?.data?.message || errorMessage;
          }
        }
      }
      setState({
        addresses: [], // Always reset to empty array
        loading: false,
        error: errorMessage,
      });
    }
  }, []);

  const removeAddress = useCallback(async (id: string) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      await deleteAddress(id);
      setState((prev) => ({
        ...prev,
        addresses: prev.addresses.filter((address) => address.id !== id),
        loading: false,
        error: null,
      }));
    } catch (err) {
      let errorMessage = 'Không thể xóa địa chỉ';
      if (err instanceof Error) {
        errorMessage = err.message;
        if ('response' in err) {
          const apiError = err as any;
          if (apiError.response?.status === 401) {
            errorMessage = 'Phiên đăng nhập hết hạn. Vui lòng đăng nhập lại.';
          } else if (apiError.response?.status === 500) {
            errorMessage = 'Lỗi máy chủ. Vui lòng thử lại sau.';
          } else {
            errorMessage = apiError.response?.data?.message || errorMessage;
          }
        }
      }
      setState((prev) => ({
        ...prev,
        loading: false,
        error: errorMessage,
      }));
    }
  }, []);

  useEffect(() => {
    const controller = new AbortController();
    fetchAddresses(controller.signal);
    return () => {
      controller.abort();
    };
  }, [fetchAddresses]);

  const refresh = useCallback(
    debounce(async () => {
      await fetchAddresses();
    }, 300),
    [fetchAddresses]
  );

  return {
    addresses: state.addresses,
    loading: state.loading,
    error: state.error,
    refresh,
    removeAddress,
  };
};

export default useAddress;