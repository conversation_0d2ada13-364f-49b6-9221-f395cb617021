import { useEffect, useState } from 'react';
import { Alert } from 'react-native';
import { 
  getUserCart, 
  getCartItemCount, 
  deleteItemFromCart, 
  addToCart, 
  updateCartItem, 
  chooseAllItem, 
  chooseItemByMerchantId, 
  chooseItemByCartItemId, 
  chooseItemByProductId 
} from 'api/cart';
import { setStorageCarts, getCarts as getStorageCarts } from 'utils/cart';
import { flatten, map, filter } from 'lodash';
import { useCart } from 'hooks/contexts/CartContext';
import Toast from 'react-native-toast-message';

interface MerchantResponse {
  id: string;
  storeName: string;
  logo: string;
}

interface CartItem {
  id: string;
  title: string;
  price: number;
  anchoPrice: number;
  quantity: number;
  mainImageUrl: string;
  merchantId: string;
  merchantResponse: MerchantResponse;
  variants: any[];
  choose: boolean;
  productId: number; // Thêm productId để điều hướng
}

interface RecentProduct {
  id: string;
}

interface CartResponse {
  carts: {
    merchant: MerchantResponse;
    cartItemResponses: {
      id: string;
      product: {
        id: number;
        title: string;
        price: number;
        anchoPrice?: number;
        imageUrl: string;
      };
      quantity: number;
      variants: any[];
      choose: boolean;
    }[];
    choose: boolean;
  }[];
}

export const useUserCart = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [selected, setSelected] = useState<CartItem[]>([]);
  const [products, setProducts] = useState<RecentProduct[]>([]);
  const [itemCount, setItemCount] = useState<number>(0);
  const [page, setPage] = useState<number>(0);
  const [size] = useState<number>(20);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [updatingItemId, setUpdatingItemId] = useState<string | null>(null);
  const { fetchCartCount } = useCart();

  const recent: RecentProduct[] = [];

  const fetchCart = async () => {
    try {
      setLoading(true);
      const response: CartResponse = await getUserCart(0, 20);
      const carts = response?.carts || [];

      const cartItems: CartItem[] = flatten(
        map(carts, cart =>
          map(cart.cartItemResponses, item => ({
            id: item.id,
            title: item.product.title || 'Sản phẩm không tên',
            price: item.product.price || 0,
            anchoPrice: item.product.anchoPrice || item.product.price || 0,
            quantity: item.quantity || 1,
            mainImageUrl: item.product.imageUrl || '',
            merchantId: cart.merchant.id.toString(),
            merchantResponse: {
              id: cart.merchant.id.toString(),
              storeName: cart.merchant.storeName || 'Cửa hàng không xác định',
              logo: cart.merchant.logo || '',
            },
            variants: item.variants || [],
            choose: item.choose || false,
            productId: item.product.id, // Thêm productId
          }))
        )
      );

      setCart(cartItems);
      setStorageCarts(cartItems);
      setSelected(cartItems.filter(item => item.choose));
      await fetchCartCount();

      const mapCartId = map(cartItems, item => item.id);
      const filteredRecent = filter(recent, item => !mapCartId.includes(item?.id));
      setProducts(filteredRecent);
    } catch (error) {
      console.error('Lỗi khi lấy dữ liệu giỏ hàng:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = async (item: any) => {
    try {
      setLoading(true);
      const response = await addToCart(item);
      if (response) {
        await fetchCart();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error adding to cart:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateCartItem = async (cartItemId: string, newQuantity: number) => {
    try {
      if (newQuantity <= 0) {
        return false;
      }
      
      setUpdatingItemId(cartItemId);
      
      const itemToUpdate = cart.find(item => item.id === cartItemId);
      if (!itemToUpdate) return false;
      
      const updateData = {
        cartItemId: cartItemId,
        quantity: newQuantity,
      };
      
      const response = await updateCartItem(updateData);
      
      if (response) {
        const updatedCart = cart.map(item => 
          item.id === cartItemId ? { ...item, quantity: newQuantity } : item
        );
        setCart(updatedCart);
        setStorageCarts(updatedCart);
        
        setSelected(prev => 
          prev.map(item => 
            item.id === cartItemId ? { ...item, quantity: newQuantity } : item
          )
        );
        
        return true;
      }
      return false;
    } catch (error) {
      console.error('Lỗi khi cập nhật số lượng sản phẩm:', error);
      Alert.alert('Lỗi', 'Không thể cập nhật số lượng sản phẩm');
      return false;
    } finally {
      setUpdatingItemId(null);
    }
  };

  const handleDeleteCartItem = async (cartItemId: string) => {
    try {
      setLoading(true);
      const response = await deleteItemFromCart(cartItemId);
      if (response) {
        const newCart = cart.filter(item => item.id !== cartItemId);
        setCart(newCart);
        setStorageCarts(newCart);
        setSelected(prev => prev.filter(item => item.id !== cartItemId));
        await fetchCartCount();
        Toast.show({
          type: 'success',
          text1: 'Đã xóa thành công sản phẩm',
          position: 'top',
        });
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error deleting cart item:', error);
      Alert.alert('Lỗi', 'Không thể xóa sản phẩm khỏi giỏ hàng');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleSelectAll = (isSelected: boolean) => {
    console.log('Selecting all:', isSelected);
    setSelected(isSelected ? [...cart] : []);
  };

  const handleSelectByMerchant = (merchantId: string, isSelected: boolean) => {
    console.log('Selecting merchant:', merchantId, isSelected);
    if (isSelected) {
      const merchantProducts = cart.filter(item => item.merchantId === merchantId);
      setSelected(prev => {
        const newSelected = [
          ...prev.filter(p => p.merchantId !== merchantId), // Loại bỏ sản phẩm của merchant này
          ...merchantProducts,
        ];
        console.log('Updated selected:', newSelected.map(item => ({ id: item.id, title: item.title })));
        return newSelected;
      });
    } else {
      setSelected(prev => {
        const newSelected = prev.filter(item => item.merchantId !== merchantId);
        console.log('Updated selected:', newSelected.map(item => ({ id: item.id, title: item.title })));
        return newSelected;
      });
    }
  };

  const handleSelectByCartItemId = (cartItemId: string, isSelected: boolean) => {
    console.log('Selecting cart item:', cartItemId, isSelected);
    const productToToggle = cart.find(item => item.id === cartItemId);
    if (isSelected && productToToggle) {
      setSelected(prev => {
        const newSelected = [...prev.filter(p => p.id !== cartItemId), { ...productToToggle, choose: true }];
        console.log('Updated selected:', newSelected.map(item => ({ id: item.id, title: item.title })));
        return newSelected;
      });
    } else {
      setSelected(prev => {
        const newSelected = prev.filter(item => item.id !== cartItemId);
        console.log('Updated selected:', newSelected.map(item => ({ id: item.id, title: item.title })));
        return newSelected;
      });
    }
  };

  const syncSelectedItems = async () => {
    try {
      setLoading(true);
      console.log('Syncing selected items:', selected.map(item => ({ id: item.id, title: item.title })));
      if (selected.length === cart.length && cart.length > 0) {
        await chooseAllItem(true);
      } else if (selected.length === 0) {
        await chooseAllItem(false);
      } else {
        for (const item of cart) {
          const isSelected = selected.some(s => s.id === item.id);
          await chooseItemByCartItemId(item.id, isSelected);
        }
      }
      return true;
    } catch (error) {
      console.error('Error syncing selected items:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchCart();
    setRefreshing(false);
  };

  const loadCartFromStorage = async () => {
    const storedCarts: CartItem[] = await getStorageCarts();
    if (storedCarts && storedCarts.length > 0) {
      setCart(storedCarts);
    }
  };

  useEffect(() => {
    loadCartFromStorage();
    fetchCart();
    return () => {
      chooseAllItem(false).catch(error => {
        console.error('Error resetting selection:', error);
      });
    };
  }, []);

  return {
    loading,
    cart,
    setCart,
    selected,
    setSelected,
    products,
    setProducts,
    itemCount,
    refreshing,
    updatingItemId,
    handleRefresh,
    handleAddToCart,
    handleUpdateCartItem,
    handleDeleteCartItem,
    handleSelectAll,
    handleSelectByMerchant,
    handleSelectByCartItemId,
    syncSelectedItems,
  };
};

export default useUserCart;