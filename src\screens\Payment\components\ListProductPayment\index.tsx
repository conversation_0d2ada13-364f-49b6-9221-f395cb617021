import React from 'react';
import { View, StyleSheet, Image, TextInput, Animated } from 'react-native';
import { Text, Divider } from '@ui-kitten/components';
import metrics from 'themes/metrics';
import colors from 'themes/colors';
import commonStyles from 'themes/commonStyles';

interface Variant {
  id: number;
  title: string;
  price: number;
  anchoPrice: number;
  imageUrl: string | null;
}

interface CartItem {
  id: number;
  cartId: number;
  variant: Variant;
  quantity: number;
  totalPrice: number;
}

interface Cart {
  id: number;
  merchant: {
    id: number;
    storeName: string;
    logo: string;
  };
  productsPrice: number;
  discountProductsPrice: number;
  totalProductsPrice: number;
  shippingPrice: number;
  discountShippingPrice: number;
  totalShippingPrice: number;
  totalPrice: number;
  cartItems: CartItem[];
}

interface CartData {
  carts: Cart[];
  productsPrice: number;
  discountProductsPrice: number;
  totalProductsPrice: number;
  shippingPrice: number;
  discountShippingPrice: number;
  totalShippingPrice: number;
  totalPrice: number;
}

interface ListProductPaymentProps {
  cartData: CartData | null;
  onUserNoteChange: (cartId: number, note: string) => void;
  loading?: boolean; // Add loading prop
}

const ListProductPayment: React.FC<ListProductPaymentProps> = ({ cartData, onUserNoteChange, loading = false }) => {
  // Animation for skeleton loading
  const fadeAnim = React.useRef(new Animated.Value(0.4)).current;

  React.useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0.4,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    );
    if (loading) {
      animation.start();
    }
    return () => animation.stop();
  }, [loading, fadeAnim]);

  // Skeleton loading UI
  const renderSkeleton = () => (
    <View style={styles.container}>
      {[...Array(2)].map((_, index) => (
        <View key={index} style={styles.merchantCard}>
          <View style={styles.merchantHeader}>
            <Animated.View style={[styles.merchantLogo, { opacity: fadeAnim, backgroundColor: colors['Grayiron/200'] }]} />
            <Animated.View style={[styles.skeletonText, { width: '60%', opacity: fadeAnim }]} />
          </View>
          <Divider style={styles.divider} />
          {[...Array(2)].map((_, itemIndex) => (
            <React.Fragment key={itemIndex}>
              <View style={styles.productContainer}>
                <Animated.View style={[styles.productImage, { opacity: fadeAnim, backgroundColor: colors['Grayiron/200'] }]} />
                <View style={styles.productDetails}>
                  <Animated.View style={[styles.skeletonText, { width: '80%', opacity: fadeAnim }]} />
                  <Animated.View style={[styles.skeletonText, { width: '40%', marginTop: metrics.spacing1, opacity: fadeAnim }]} />
                  <Animated.View style={[styles.skeletonText, { width: '30%', marginTop: metrics.spacing1, opacity: fadeAnim }]} />
                </View>
              </View>
              {itemIndex < 1 && <Divider style={styles.divider} />}
            </React.Fragment>
          ))}
          <Divider style={styles.divider} />
          <View style={styles.noteContainer}>
            <Animated.View style={[styles.skeletonText, { width: '20%', opacity: fadeAnim }]} />
            <Animated.View style={[styles.noteInput, { opacity: fadeAnim, backgroundColor: colors['Grayiron/200'] }]} />
          </View>
          <Divider style={styles.divider} />
          {[...Array(3)].map((_, sumIndex) => (
            <View key={sumIndex} style={styles.merchantSummary}>
              <Animated.View style={[styles.skeletonText, { width: '40%', opacity: fadeAnim }]} />
              <Animated.View style={[styles.skeletonText, { width: '30%', opacity: fadeAnim }]} />
            </View>
          ))}
        </View>
      ))}
    </View>
  );

  if (loading) {
    return renderSkeleton();
  }

  if (!cartData || !cartData.carts || cartData.carts.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.emptyText}>Đang tải sản phẩm...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {cartData.carts.map((cart) => (
        <View key={cart.id} style={styles.merchantCard}>
          <View style={styles.merchantHeader}>
            <Image
              source={{ uri: cart.merchant.logo || 'https://via.placeholder.com/32' }}
              style={styles.merchantLogo}
              resizeMode="contain"
            />
            <Text style={[commonStyles.mainText, styles.merchantName]}>
              {cart.merchant.storeName}
            </Text>
          </View>
          <Divider style={styles.divider} />
          {cart.cartItems.map((item, index) => (
            <React.Fragment key={item.id}>
              <View style={styles.productContainer}>
                <Image
                  source={{ uri: item.variant.imageUrl || 'https://via.placeholder.com/80' }}
                  style={styles.productImage}
                  resizeMode="contain"
                />
                <View style={styles.productDetails}>
                  <Text style={styles.productTitle} numberOfLines={2}>
                    {item.variant.title}
                  </Text>
                  <Text style={styles.quantity}>x{item.quantity}</Text>
                  <Text style={styles.price}>
                    {item.totalPrice.toLocaleString('vi-VN')} VNĐ
                  </Text>
                </View>
              </View>
              {index < cart.cartItems.length - 1 && <Divider style={styles.divider} />}
            </React.Fragment>
          ))}
          <Divider style={styles.divider} />
          <View style={styles.noteContainer}>
            <Text style={styles.noteLabel}>Ghi chú:</Text>
            <TextInput
              style={styles.noteInput}
              placeholder="Nhập ghi chú"
              multiline
              onChangeText={(text) => onUserNoteChange(cart.id, text)}
            />
          </View>
          <Divider style={styles.divider} />
          <View style={styles.merchantSummary}>
            <Text style={styles.summaryLabel}>Tổng sản phẩm:</Text>
            <Text style={styles.summaryValue}>
              {cart.totalProductsPrice.toLocaleString('vi-VN')} VNĐ
            </Text>
          </View>
          <View style={styles.merchantSummary}>
            <Text style={styles.summaryLabel}>Phí vận chuyển:</Text>
            <Text style={styles.summaryValue}>
              {cart.totalShippingPrice.toLocaleString('vi-VN')} VNĐ
            </Text>
          </View>
          <View style={styles.merchantSummary}>
            <Text style={[styles.summaryLabel, { fontWeight: 'bold' }]}>
              Tổng cộng:
            </Text>
            <Text style={[styles.summaryValue, { fontWeight: 'bold', color: colors['Primary/300'] }]}>
              {cart.totalPrice.toLocaleString('vi-VN')} VNĐ
            </Text>
          </View>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: metrics.spacing2,
    backgroundColor: colors['Grayiron/50'],
  },
  merchantCard: {
    backgroundColor: 'white',
    borderRadius: metrics.radius2,
    marginBottom: metrics.spacing2,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.08,
    shadowRadius: 3,
  },
  merchantHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: metrics.spacing2,
  },
  merchantLogo: {
    width: 32,
    height: 32,
    marginRight: metrics.spacing2,
    borderRadius: metrics.radius1,
  },
  merchantName: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
  },
  divider: {
    marginHorizontal: metrics.spacing2,
    backgroundColor: colors['Grayiron/200'],
  },
  productContainer: {
    flexDirection: 'row',
    padding: metrics.spacing2,
    alignItems: 'center',
  },
  productImage: {
    width: 80,
    height: 80,
    marginRight: metrics.spacing2,
    borderRadius: metrics.radius1,
    backgroundColor: colors['Grayiron/50'],
  },
  productDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  productTitle: {
    fontSize: 14,
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing1,
    fontWeight: '500',
  },
  quantity: {
    fontSize: 12,
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing1,
  },
  price: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors['Primary/300'],
  },
  noteContainer: {
    padding: metrics.spacing2,
  },
  noteLabel: {
    fontSize: 14,
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing1,
    fontWeight: '500',
  },
  noteInput: {
    borderWidth: 1,
    borderColor: colors['Grayiron/200'],
    borderRadius: metrics.radius1,
    padding: metrics.spacing1,
    fontSize: 12,
    color: colors['Grayiron/700'],
    minHeight: 40,
    textAlignVertical: 'top',
  },
  merchantSummary: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: metrics.spacing2,
  },
  summaryLabel: {
    fontSize: 14,
    color: colors['Grayiron/600'],
  },
  summaryValue: {
    fontSize: 14,
    color: colors['Grayiron/700'],
  },
  emptyText: {
    textAlign: 'center',
    color: colors['Grayiron/600'],
    fontSize: 16,
    padding: metrics.spacing2,
  },
  skeletonText: {
    height: 14,
    backgroundColor: colors['Grayiron/200'],
    borderRadius: metrics.radius1,
  },
});

export default ListProductPayment;