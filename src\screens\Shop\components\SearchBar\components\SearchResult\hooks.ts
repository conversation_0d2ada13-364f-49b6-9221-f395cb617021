import { useState, useEffect } from 'react';

export const useLoadMore = ({ initialItems = [], itemsPerPage = 10 }) => {
  const [page, setPage] = useState(1);
  const [displayedItems, setDisplayedItems] = useState([]);
  const [hasMore, setHasMore] = useState(false);

  useEffect(() => {
    const firstPageItems = initialItems.slice(0, itemsPerPage);
    setDisplayedItems(firstPageItems);
    setPage(1);
    setHasMore(initialItems.length > itemsPerPage);
  }, [initialItems, itemsPerPage]);

  const loadMore = () => {
    const nextPage = page + 1;
    const newItems = initialItems.slice(0, nextPage * itemsPerPage);
    setDisplayedItems(newItems);
    setPage(nextPage);
    setHasMore(initialItems.length > nextPage * itemsPerPage);
  };

  return {
    displayedItems,
    hasMore,
    loadMore,
    page,
  };
};