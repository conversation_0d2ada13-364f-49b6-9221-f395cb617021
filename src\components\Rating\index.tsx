/* eslint-disable react-native/no-inline-styles */
import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native';
import range from 'lodash/range';
import { Icon } from '@ui-kitten/components';
import metrics from 'themes/metrics';
import commonStyles from 'themes/commonStyles';
import colors from 'themes/colors';

type Props = {
  disabled?: boolean;
  loading?: boolean;
  children?: any;
  onPress?: any;
  value?: any;
  onChange?: any;
  size?: 'medium' | 'large' | 'small' | 'huge';
  customStyles?: ViewStyle;
};

function Rating({ customStyles, value = 5, onChange, size }: Props) {
  const [val, setVal] = useState(value);
  let iconSizeStyle = styles.iconMedium;
  switch (size) {
    case 'small':
      iconSizeStyle = styles.iconSmall;
      break;
    case 'large':
      iconSizeStyle = styles.iconLarge;
      break;
    case 'huge':
      iconSizeStyle = styles.iconHuge;
      break;
    default:
      break;
  }

  const onChangeRating = (newRate: number) => {
    onChange?.(newRate);
    onChange && setVal(newRate);
  };

  return (
    <View
      style={[
        { paddingVertical: metrics.spacing1 },
        commonStyles.row,
        customStyles,
      ]}>
      {range(1, 6).map(item => (
        <TouchableOpacity
          activeOpacity={!onChange ? 1 : undefined}
          style={{ marginLeft: item === 1 ? 0 : metrics.spacing1 }}
          onPress={() => onChangeRating(item)}
          key={item}>
          <Icon
            style={[iconSizeStyle]}
            fill={item <= val ? colors['Warning/300'] : '#8F9BB3'}
            name="star"
          />
        </TouchableOpacity>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  iconSmall: {
    width: 10,
    height: 10,
  },
  iconMedium: {
    width: 13,
    height: 13,
  },
  iconLarge: {
    width: 16,
    height: 16,
  },
  iconHuge: {
    width: 20,
    height: 20,
  },
});

export default Rating;
