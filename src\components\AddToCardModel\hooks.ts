import { useState } from 'react';
import { useCart } from 'hooks/contexts/CartContext';
import { addToCart } from 'api/cart';
import Toast from 'react-native-toast-message';
import { checkAuthentication } from 'utils/authUtils';

export const useAddToCart = (
  navigation?: any,
  showAlert?: (
    type: 'success' | 'error' | 'info' | 'confirm',
    title: string,
    message: string,
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[]
  ) => void,
  hideAlert?: () => void
) => {
  const { fetchCartCount } = useCart();
  const [addingToCart, setAddingToCart] = useState(false);

  const handleAddToCart = async (product: any, quantity: number, variant: any) => {
    const { isAuthenticated } = await checkAuthentication();

    if (!isAuthenticated) {
      if (showAlert && hideAlert && navigation) {
        showAlert(
          'info',
          '<PERSON>êu cầu đăng nhập',
          'Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng',
          [
            { text: 'Hủy', style: 'cancel', onPress: hideAlert },
            {
              text: 'Đăng nhập',
              style: 'default',
              onPress: () => {
                hideAlert();
                navigation.navigate('Authentication');
              }
            }
          ]
        );
      } else {
        Toast.show({
          type: 'error',
          text1: 'Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng',
          position: 'top',
        });
      }
      return false;
    }

    setAddingToCart(true);

    try {
      const cartItem = {
        id: product.id,
        merchantId: product.merchantId,
        quantity,
        price: variant ? variant.price : product.price,
        mainAttributeValueId: variant?.mainAttributeValueId,
        secondAttributeValueId: variant?.secondAttributeValueId,
        variantId: variant?.id,
      };

      const response = await addToCart(cartItem);
      
      if (response?.success) {
        await fetchCartCount();
        Toast.show({
          type: 'success',
          text1: 'Đã thêm vào giỏ hàng',
          position: 'top',
        });
        return true;
      }
      return false;
    } catch (error) {
      console.error('Add to cart error:', error);
      Toast.show({
        type: 'error',
        text1: 'Thêm vào giỏ hàng thất bại',
        //text2: error.message || 'Vui lòng thử lại sau',
        position: 'top',
      });
      return false;
    } finally {
      setAddingToCart(false);
    }
  };

  return {
    handleAddToCart,
    addingToCart,
  };
};