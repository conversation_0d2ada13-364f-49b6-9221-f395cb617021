import React from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';

import metrics from 'themes/metrics';
import PostCard from 'components/PostCard';
import { Divider, Text } from '@ui-kitten/components';
import Tag from 'components/Tag';
import fonts from 'themes/fonts';
import colors from 'themes/colors';

import post from 'utils/post2.json';
import commonStyles from 'themes/commonStyles';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';

const ActivityVideos = () => {
  return (
    <View style={styles.container}>
      <View style={commonStyles.rowSpaceBetween}>
        <Text style={styles.title}>Video hướng dẫn bài tập</Text>
        <TouchableOpacity>
          <Text style={styles.subTitle}>
            Xem tất cả
            <MaterialCommunityIcons
              color={colors['Grayiron/600']}
              name={'chevron-right'}
              size={16}
            />
          </Text>
        </TouchableOpacity>
      </View>
      <ScrollView
        horizontal
        style={styles.tags}
        bounces
        showsHorizontalScrollIndicator={false}>
        <Tag
          customStyles={styles.tag}
          size="large"
          type="success"
          title="Xem nhiều nhất"
        />
        <Tag customStyles={styles.tag} size="large" title="Mới nhất" />
        <Tag customStyles={styles.tag} size="large" title="Giảm mỡ" />
      </ScrollView>
      <ScrollView
        horizontal
        style={styles.tags}
        bounces
        showsHorizontalScrollIndicator={false}>
        <PostCard
          customStyles={{ marginRight: metrics.spacing4 }}
          type="full"
          title={post.title}
          author={post.author}
          uri={post.thumbnail}
          avatarUri={post.thumbnail}
          topicName={post?.topic?.name}
          createdAt={post?.createdAt}
          onPress={() => navigation.navigate('PostDetail')}
        />
        <PostCard
          customStyles={{ marginRight: metrics.spacing4 }}
          type="full"
          title={post.title}
          author={post.author}
          uri={post.thumbnail}
          avatarUri={post.thumbnail}
          topicName={post?.topic?.name}
          createdAt={post?.createdAt}
          onPress={() => navigation.navigate('PostDetail')}
        />
        <PostCard
          type="full"
          title={post.title}
          author={post.author}
          uri={post.thumbnail}
          avatarUri={post.thumbnail}
          topicName={post?.topic?.name}
          createdAt={post?.createdAt}
          onPress={() => navigation.navigate('PostDetail')}
        />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: metrics.spacing4,
    paddingTop: metrics.spacing4,
    backgroundColor: 'white',
    marginBottom: metrics.spacing4
  },
  content: {
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing4,
    backgroundColor: 'white',
    borderRadius: metrics.radius4,
  },
  subTitle: {
    fontSize: fonts.size.input,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  row: {
    justifyContent: 'space-around',
    alignItems: 'center',
    flexWrap: 'wrap',
    flexDirection: 'row',
    gap: metrics.spacing2,
    marginBottom: metrics.spacing3,
  },
  title: {
    fontSize: fonts.size.h3,
    fontWeight: '600',
    color: colors['Grayiron/600'],
    marginBottom: metrics.spacing4,
  },
  tag: {
    marginRight: metrics.spacing2,
  },
  tags: {
    marginBottom: metrics.spacing4,
  },
});

export default ActivityVideos;
