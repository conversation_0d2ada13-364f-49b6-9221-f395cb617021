import { useEffect, useState } from 'react';
import {
  getRateableOrders,
  getProductRatings,
  createProductRating,
} from 'api/products';

// Type definitions
interface Rating {
  id: string;
  userAvatar?: string;
  userName?: string;
  rate: number;
  comment: string;
  createdAt: string;
  imageUrls?: string;
}

interface RateableOrder {
  productId: string;
  orderItemId?: string;
}

interface RatingsResponse {
  rates: Rating[];
  totalPages: number;
}

interface RateableOrdersResponse {
  data: {
    content: RateableOrder[];
  };
}

interface UseProductRatingProps {
  id: string;
}

export const useProductRating = ({ id }: UseProductRatingProps) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [ratings, setRatings] = useState<Rating[]>([]);
  const [hasRated, setHasRated] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState<number>(0);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const size = 10;

  const fetchRatings = async (pageNum: number = 0, append: boolean = false) => {
    try {
      if (pageNum === 0) setLoading(true);
      else setIsLoadingMore(true);
      setError(null);
      const response: RatingsResponse = await getProductRatings({
        id,
        page: pageNum,
        size,
      });
      console.log('Ratings response:', response);
      setRatings((prev) =>
        append && pageNum > 0 ? [...prev, ...response.rates] : response.rates
      );
      setTotalPages(response.totalPages || 1);
      setPage(pageNum);
    } catch (error) {
      console.error('Error fetching ratings:', error);
      setError('Không thể tải đánh giá. Vui lòng thử lại.');
    } finally {
      setLoading(false);
      setIsLoadingMore(false);
    }
  };

  const fetchRatingStatus = async (): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      const response: RateableOrdersResponse = await getRateableOrders({
        rated: false,
      });
      console.log('Rateable orders response:', response);
      const canRate =
        response?.data?.content?.some((order) => order.productId === id) || false;
      setHasRated(!canRate);
      return canRate;
    } catch (error) {
      console.error('Error fetching rating status:', error);
      setError('Không thể kiểm tra trạng thái đánh giá.');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const submitRating = async (
    rating: number,
    comment: string,
    imageUrl?: string
  ) => {
    try {
      setLoading(true);
      setError(null);
      const rateableOrders: RateableOrdersResponse = await getRateableOrders({
        rated: false,
      });
      const order = rateableOrders?.data?.content?.find(
        (o) => o.productId === id
      );
      const payload = {
        productId: id,
        rating,
        comment,
        ...(order?.orderItemId && { orderItemId: order.orderItemId }),
        ...(imageUrl && { imageUrls: imageUrl }),
      };
      const response = await createProductRating(payload);
      if (response) {
        await fetchRatings(0); // Reset to first page
        setHasRated(true);
        return response;
      }
      throw new Error('Phản hồi từ server không hợp lệ');
    } catch (error) {
      console.error('Error submitting rating:', error);
      setError('Không thể gửi đánh giá. Vui lòng thử lại.');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const loadMoreRatings = () => {
    if (!loading && !isLoadingMore && page + 1 < totalPages) {
      fetchRatings(page + 1, true);
    }
  };

  useEffect(() => {
    const initialize = async () => {
      const canRate = await fetchRatingStatus();
      if (!canRate) await fetchRatings(0);
    };
    initialize();
  }, [id]);

  return {
    loading,
    ratings,
    hasRated,
    submitRating,
    error,
    loadMoreRatings,
    isLoadingMore,
    hasMore: page + 1 < totalPages,
  };
};