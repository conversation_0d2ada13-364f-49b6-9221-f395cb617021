// components/MerchantItem.tsx
import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
} from 'react-native';
import metrics from 'themes/metrics';
import fonts from 'themes/fonts';
import colors from 'themes/colors';
import Toast from 'react-native-toast-message';
import Product from 'components/Product';
import { Product as ProductType } from 'types/product';
import { useCart } from 'hooks/contexts/CartContext';
import { useNavigation } from '@react-navigation/native';
import { addRecentProducts } from 'utils/cart';

interface MerchantItemProps {
  product: ProductType;
  onAddToCart: (product: ProductType, quantity: number, variant?: any) => Promise<boolean>;
}

const MerchantItem: React.FC<MerchantItemProps> = ({ product, onAddToCart }) => {
  const navigation = useNavigation();
  const { fetchCartCount } = useCart();
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState<any>(null);
  const [quantity, setQuantity] = useState(1);
  const [modalLoading, setModalLoading] = useState(false);

  const openModal = () => {
    if (product?.variants?.length > 0) {
      setSelectedVariant(product.variants[0]);
    } else {
      setSelectedVariant(null);
    }
    setModalVisible(true);
  };

  const closeModal = () => {
    setModalVisible(false);
    setQuantity(1);
    setSelectedVariant(null);
  };

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity > 0) {
      setQuantity(newQuantity);
    }
  };

  const handleVariantSelect = (variant: any) => {
    setSelectedVariant(variant);
  };

  const handleAddToCartAction = async () => {
    setModalLoading(true);
    try {
      const success = await onAddToCart(product, quantity, selectedVariant);
      if (success) {
        Toast.show({
          type: 'success',
          text1: 'Thêm vào giỏ hàng thành công',
          position: 'top',
        });
        await fetchCartCount();
        closeModal();
      }
    } catch (e: any) {
      Toast.show({
        type: 'error',
        text1: 'Thêm vào giỏ hàng thất bại',
        text2: e.message || 'Vui lòng thử lại sau',
        position: 'top',
      });
    } finally {
      setModalLoading(false);
    }
  };

  const renderVariantOptions = () => {
    if (!product?.mainAttribute || !product?.variants) return null;

    return (
      <View style={styles.modalSection}>
        <Text style={styles.modalSectionTitle}>{product.mainAttribute.name}:</Text>
        <View style={styles.variantOptions}>
          {product.variants.map((variant) => {
            const isSelected = selectedVariant?.id === variant.id;
            return (
              <TouchableOpacity
                key={variant.id}
                style={[styles.variantButton, isSelected && styles.selectedVariantButton]}
                onPress={() => handleVariantSelect(variant)}
              >
                <Text
                  style={[styles.variantText, isSelected && styles.selectedVariantText]}
                >
                  {variant.title.split('-')[1]?.trim() || variant.title}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </View>
    );
  };

  // Kiểm tra dữ liệu product trước khi render
  if (!product || !product.id) {
    console.warn('Invalid product data:', product);
    return null;
  }

  return (
    <View style={styles.cardContainer}>
      <Product
        customStyles={{ width: metrics.fullWidth / 2 - metrics.spacing6 }}
        title={product.title}
        uri={product.mainImageUrl}
        price={product.price}
        anchoPrice={product.anchoPrice}
        rating={product.rating}
        discountPercent={product.discountPercent}
        onPress={() => {
          addRecentProducts(product);
          navigation.navigate('ProductDetail', { id: product.id });
        }}
        onAddToCart={openModal}
      />

      {/* Modal thêm vào giỏ hàng */}
      <Modal animationType="slide" transparent={true} visible={modalVisible} onRequestClose={closeModal}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Thêm vào giỏ hàng</Text>
              <TouchableOpacity onPress={closeModal} style={styles.closeButton}>
                <Text style={styles.closeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.modalBody}>
              <View style={styles.productPreview}>
                <View style={styles.productImageContainer}>
                  <Product
                    uri={product.mainImageUrl}
                    style={styles.productThumbnail}
                    hideDetails
                  />
                </View>
                <View style={styles.productInfoModal}>
                  <Text style={styles.productTitle} numberOfLines={2}>
                    {product.title}
                  </Text>
                  <Text style={styles.productPrice}>
                    {selectedVariant
                      ? selectedVariant.price.toLocaleString()
                      : product.price.toLocaleString()}{' '}
                    đ
                  </Text>
                </View>
              </View>
              {renderVariantOptions()}
              <View style={styles.modalSection}>
                <Text style={styles.modalSectionTitle}>Số lượng:</Text>
                <View style={styles.quantityControls}>
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => handleQuantityChange(quantity - 1)}
                  >
                    <Text style={styles.quantityButtonText}>-</Text>
                  </TouchableOpacity>
                  <Text style={styles.quantityValue}>{quantity}</Text>
                  <TouchableOpacity
                    style={styles.quantityButton}
                    onPress={() => handleQuantityChange(quantity + 1)}
                  >
                    <Text style={styles.quantityButtonText}>+</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={handleAddToCartAction}
              disabled={modalLoading}
            >
              {modalLoading ? (
                <ActivityIndicator size="small" color={colors['Moss/700']} />
              ) : (
                <Text style={styles.modalButtonText}>Thêm vào giỏ hàng</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    width: metrics.fullWidth / 2 - metrics.spacing6,
    marginBottom: metrics.spacing4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: metrics.spacing4,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: metrics.spacing4,
    paddingVertical: metrics.spacing3,
    borderBottomWidth: 1,
    borderBottomColor: colors['Gray/100'],
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors['Gray/900'],
  },
  closeButton: {
    padding: metrics.spacing2,
  },
  closeButtonText: {
    fontSize: 16,
    color: colors['Gray/700'],
    fontWeight: '600',
  },
  modalBody: {
    padding: metrics.spacing4,
  },
  productPreview: {
    flexDirection: 'row',
    marginBottom: metrics.spacing4,
    alignItems: 'center',
  },
  productImageContainer: {
    width: 80,
    height: 80,
    borderRadius: 8,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors['Gray/100'],
  },
  productThumbnail: {
    width: 80,
    height: 80,
    resizeMode: 'cover',
  },
  productInfoModal: {
    flex: 1,
    marginLeft: metrics.spacing3,
  },
  productTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors['Gray/800'],
    marginBottom: metrics.spacing2,
  },
  productPrice: {
    fontSize: 18,
    fontWeight: '700',
    color: colors['Warning/500'],
  },
  modalSection: {
    marginBottom: metrics.spacing4,
  },
  modalSectionTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: colors['Gray/800'],
    marginBottom: metrics.spacing3,
  },
  variantOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: metrics.spacing2,
  },
  variantButton: {
    paddingHorizontal: metrics.spacing3,
    paddingVertical: metrics.spacing2,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors['Gray/200'],
    backgroundColor: colors.white,
    marginBottom: metrics.spacing2,
  },
  selectedVariantButton: {
    borderColor: colors['Moss/500'],
    backgroundColor: colors['Moss/50'],
  },
  variantText: {
    fontSize: 14,
    color: colors['Gray/700'],
  },
  selectedVariantText: {
    color: colors['Moss/700'],
    fontWeight: '500',
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors['Gray/100'],
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: metrics.spacing2,
  },
  quantityButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors['Gray/700'],
  },
  quantityValue: {
    fontSize: 16,
    fontWeight: '500',
    color: colors['Gray/900'],
    minWidth: 30,
    textAlign: 'center',
  },
  modalButton: {
    marginHorizontal: metrics.spacing4,
    height: 48,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors['Moss/500'],
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});

export default MerchantItem;