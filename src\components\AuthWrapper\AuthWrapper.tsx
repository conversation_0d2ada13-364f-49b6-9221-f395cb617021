import React, { useState, useEffect } from 'react';
import { View, ActivityIndicator, Text, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import CustomAlert from 'utils/widgets/CustomAlert';
import colors from 'themes/colors';
import metrics from 'themes/metrics';
import { checkAuthentication } from 'utils/authUtils';

interface AuthWrapperProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  loadingText?: string;
  authRequiredText?: string;
  customAuthMessage?: string;
  onAuthRequired?: () => void;
}

const AuthWrapper: React.FC<AuthWrapperProps> = ({
  children,
  requireAuth = true,
  loadingText = 'Đang kiểm tra đăng nhập...',
  authRequiredText = 'Vui lòng đăng nhập để tiếp tục',
  customAuthMessage,
  onAuthRequired,
}) => {
  const navigation = useNavigation<any>();
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [alert, setAlert] = useState<{
    visible: boolean;
    type: 'success' | 'error' | 'info' | 'confirm';
    title: string;
    message: string;
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[];
  } | null>(null);

  const showAlert = (
    type: 'success' | 'error' | 'info' | 'confirm',
    title: string,
    message: string,
    buttons?: { text: string; onPress?: () => void; style?: 'default' | 'cancel' | 'destructive' }[]
  ) => {
    setAlert({ visible: true, type, title, message, buttons });
  };

  const hideAlert = () => {
    setAlert(null);
  };

  useEffect(() => {
    if (!requireAuth) {
      setIsAuthenticated(true);
      return;
    }

    const checkAuth = async () => {
      const { isAuthenticated: authStatus } = await checkAuthentication();
      setIsAuthenticated(authStatus);
      
      if (!authStatus) {
        if (onAuthRequired) {
          onAuthRequired();
        } else {
          showAlert(
            'info',
            'Yêu cầu đăng nhập',
            customAuthMessage || authRequiredText,
            [
              { 
                text: 'Đăng nhập', 
                style: 'default', 
                onPress: () => {
                  hideAlert();
                  navigation.navigate('Authentication');
                }
              }
            ]
          );
        }
      }
    };

    checkAuth();
  }, [requireAuth, navigation, customAuthMessage, authRequiredText, onAuthRequired]);

  if (requireAuth && isAuthenticated === null) {
    return (
      <View style={styles.centerContent}>
        <ActivityIndicator size="large" color={colors['Moss/500']} />
        <Text style={styles.loadingText}>{loadingText}</Text>
      </View>
    );
  }

  if (requireAuth && !isAuthenticated) {
    return (
      <>
        <View style={styles.centerContent}>
          <Text style={styles.authRequiredText}>{authRequiredText}</Text>
        </View>
        {alert && (
          <CustomAlert
            visible={alert.visible}
            type={alert.type}
            title={alert.title}
            message={alert.message}
            buttons={alert.buttons}
            onDismiss={hideAlert}
            dismissable={alert.type !== 'success'}
          />
        )}
      </>
    );
  }

  return <>{children}</>;
};

const styles = StyleSheet.create({
  centerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: metrics.spacing4,
  },
  loadingText: {
    fontSize: 16,
    marginTop: metrics.spacing3,
    color: colors['Gray/700'],
    textAlign: 'center',
  },
  authRequiredText: {
    fontSize: 16,
    color: colors['Gray/700'],
    textAlign: 'center',
  },
});

export default AuthWrapper;
