/* eslint-disable react-hooks/exhaustive-deps */
import { find, flattenDeep, forEach, map, pick } from 'lodash';
import { useTopicStore } from 'stores/topics';
import { flatTopics } from 'utils/index';

export const usePostMenu = ({ id }: any) => {
  const topics = useTopicStore(state => state?.topics);

  const flatted = flatTopics(topics);
  if (id) {
    const topic = find(flatted, { id });

    return {
      topics: topic?.childTopics,
    };
  } else {
    return {
      topics: map(topics, item => pick(item, ['id', 'name'])),
    };
  }
};
